import type { Observable } from "rxjs";

export type StreamTextEvent = {
  type: "text";
  payload: {
    text: string;
  };
};

export type StreamToolCallEvent<T> = {
  type: "tool-call";
  payload: {
    toolCallId: string;
    toolName: string;
    args: T;
  };
};

export type StreamToolResultEvent<T> = {
  type: "tool-result";
  payload: {
    toolCallId: string;
    result: T;
    elapsedTime: number;
  };
};

export type StreamStepStartEvent = {
  type: "step-start";
};

export type StreamStepFinishEvent = {
  type: "step-finish";
};

export type StreamTimeoutEvent = {
  type: "timeout";
  payload: {
    timeoutType: "text" | "tool" | "step";
    error: string;
  };
};

export type StreamUserMessageEvent = {
  type: "user-message";
  payload: {
    message: string;
  };
};

export type StreamErrorEvent = {
  type: "error";
  payload: {
    error: string;
  };
};

export type StreamUpdateProcessStatusEvent = {
  type: "update-process-status";
  payload: {
    status: "idle" | "pending" | "processing" | "done" | "canceled";
  };
};
export type StreamMessageFinishEvent = {
  type: "stream-finish";
};

export type StreamEvent<T> =
  | StreamUserMessageEvent
  | StreamTextEvent
  | StreamToolCallEvent<T>
  | StreamToolResultEvent<T>
  | StreamStepStartEvent
  | StreamStepFinishEvent
  | StreamTimeoutEvent
  | StreamErrorEvent
  | StreamUpdateProcessStatusEvent
  | StreamMessageFinishEvent;

export interface StreamProcessor<T, S> {
  emitEvent: (event: StreamEvent<T>) => void;
  setState: (state: Partial<S>) => void;
  reset: (newState?: Partial<S>) => void;
  cleanup: () => void;
  streamEvents$: Observable<StreamEvent<T>>;
}

export type StreamProcessorCreator<T, S> = ({
  onAbort,
}: {
  onAbort: (reason: string) => void;
}) => StreamProcessor<T, S>;
