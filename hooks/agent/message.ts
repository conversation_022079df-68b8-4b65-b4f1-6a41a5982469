import type { CoreMessage, MastraMessageV1 } from "@mastra/core";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import type {
  AssistantMessage,
  Message,
  TextMessage,
  ToolCallMessage,
  UserMessage,
} from "~/types/message";
import type { StreamEvent } from "./types";

export const convertMessagesToStreamEvents = (
  messages: Message[],
  ignoreRole?: "assistant" | "user" | "tool"
): StreamEvent<unknown>[] => {
  const events: StreamEvent<unknown>[] = [];

  messages.forEach((message) => {
    if (message.role === ignoreRole) {
      return;
    }
    if (message.role === "user") {
      if (!Array.isArray(message.content)) {
        return;
      }
      message.content.forEach((content) => {
        if (content.type === "text") {
          events.push({
            type: "user-message",
            payload: {
              message: content.text,
            },
          });
        }
      });
    } else if (message.role === "assistant") {
      if (!Array.isArray(message.content)) {
        return;
      }
      message.content.forEach((content) => {
        if (content.type === "text") {
          events.push({
            type: "text",
            payload: {
              text: content.text,
            },
          });
        } else if (content.type === "tool-call") {
          events.push({
            type: "tool-call",
            payload: {
              toolCallId: content.toolCallId,
              toolName: content.toolName,
              args: content.args,
            },
          });
          if (content.result) {
            events.push({
              type: "tool-result",
              payload: {
                toolCallId: content.toolCallId,
                result: content.result,
                elapsedTime: 0,
              },
            });
          }
        }
      });
    }
  });

  events.push({
    type: "stream-finish",
  });

  return events;
};

export const isInteractiveToolCallStart = (
  content: TextMessage | ToolCallMessage
) => {
  return (
    content.type === "tool-call" &&
    content.toolName === "askUserTool" &&
    !content.result
  );
};

export const isInteractiveToolCallEnd = (
  content: TextMessage | ToolCallMessage
) => {
  return (
    content.type === "tool-call" &&
    content.toolName === "askUserTool" &&
    content.result
  );
};

export const isLookupCluesToolCall = (
  content: TextMessage | ToolCallMessage
) => {
  return content.type === "tool-call" && content.toolName === "lookupCluesTool";
};

export const isLookupCluesToolCallEnd = (
  content: TextMessage | ToolCallMessage
) => {
  return (
    content.type === "tool-call" &&
    content.toolName === "lookupCluesTool" &&
    content.result
  );
};

export const isToolCall = (content: TextMessage | ToolCallMessage) => {
  return content.type === "tool-call";
};

export const isNonInteractiveToolCall = (
  content: TextMessage | ToolCallMessage
) => {
  return content.type === "tool-call" && content.toolName !== "askUserTool";
};

export const isText = (content: TextMessage | ToolCallMessage) => {
  return content.type === "text";
};

export const isArtifactReportToolCall = (
  content: TextMessage | ToolCallMessage
) => {
  return content.type === "tool-call" && content.toolName === "reportTool";
};

export const isPlanToolCall = (content: TextMessage | ToolCallMessage) => {
  return content.type === "tool-call" && content.toolName === "planTool";
};

export const isSummaryToolCall = (content: TextMessage | ToolCallMessage) => {
  return content.type === "tool-call" && content.toolName === "summaryTool";
};

export const isRetrieveProcessTool = (
  content: TextMessage | ToolCallMessage
) => {
  return (
    content.type === "tool-call" && content.toolName === "retrieveProcessTool"
  );
};

function toTextMessage(content: string): TextMessage {
  return { type: "text", text: content };
}

function newAssistantMessage(): AssistantMessage {
  return {
    role: "assistant",
    content: [],
  };
}

function newUserMessage(): UserMessage {
  return {
    role: "user",
    content: [],
  };
}

function toToolCallMessage(
  toolName: string,
  toolCallId: string,
  args: unknown,
  result?: unknown,
  elapsedTime?: number
): ToolCallMessage {
  return {
    type: "tool-call",
    toolName,
    toolCallId,
    args: args as Record<string, unknown>,
    result: result as Record<string, unknown>,
    elapsedTime,
  };
}

export const coreMessageToClientMessage = (
  messages: CoreMessage[]
): Message[] => {
  const _messages: Message[] = [];
  let lastAssistantMessage: AssistantMessage | null = null;
  for (const message of messages) {
    if (message.role === "user") {
      const userMessage = newUserMessage();
      if (Array.isArray(message.content)) {
        for (const content of message.content) {
          if (content.type === "text") {
            userMessage.content.push(toTextMessage(content.text));
          }
        }
      } else {
        if (typeof message.content === "string") {
          userMessage.content.push(toTextMessage(message.content));
        }
      }
      _messages.push(userMessage);
      lastAssistantMessage = newAssistantMessage();
      _messages.push(lastAssistantMessage);
    } else if (message.role === "assistant") {
      for (const content of message.content) {
        if (typeof content === "string") {
          continue;
        }
        if (content.type === "text") {
          if (lastAssistantMessage) {
            lastAssistantMessage!.content.push(toTextMessage(content.text));
          }
        }
        if (content.type === "tool-call") {
          if (lastAssistantMessage) {
            lastAssistantMessage.content.push(
              toToolCallMessage(
                content.toolName,
                content.toolCallId,
                content.args,
                undefined,
                undefined
              )
            );
          }
        }
      }
    } else if (message.role === "tool") {
      for (const content of message.content) {
        if (typeof content === "string") {
          continue;
        }
        if (content.type === "tool-result") {
          for (let i = _messages.length - 1; i >= 0; i--) {
            let toolResultUpdated = false;
            if (_messages[i].role === "assistant") {
              for (let j = _messages[i].content.length - 1; j >= 0; j--) {
                if (_messages[i].content[j].type !== "tool-call") {
                  continue;
                }

                if (
                  content.toolCallId ===
                  (_messages[i].content[j] as ToolCallMessage).toolCallId
                ) {
                  (_messages[i].content[j] as ToolCallMessage) = {
                    ...(_messages[i].content[j] as ToolCallMessage),
                    result: content.result as Record<string, unknown>,
                  };
                  toolResultUpdated = true;
                  break;
                }
              }
            }
            if (toolResultUpdated) {
              break;
            }
          }
        }
      }
    }
  }
  return _messages;
};

export const toMastraMessageV1 = (
  messages: Message[],
  threadId: string,
  resourceId?: string
): MastraMessageV1[] => {
  const _messages: MastraMessageV1[] = [];
  const startDateTime = dayjs().subtract(1, "hour");
  for (const [msgIndex, msg] of messages.entries()) {
    if (!Array.isArray(msg.content)) {
      console.warn("message content is not an array, skip", msg.content);
      continue;
    }
    for (const [contentIndex, content] of msg.content.entries()) {
      const index = msgIndex * 10 + contentIndex;
      if (msg.role === "user") {
        _messages.push({
          id: uuidv4(),
          threadId,
          resourceId: resourceId,
          role: msg.role,
          createdAt: startDateTime.add(index, "second").toDate(),
          type: (content as TextMessage).type,
          content: (content as TextMessage).text,
        });
      } else if (msg.role === "assistant") {
        if (content.type === "text") {
          _messages.push({
            id: uuidv4(),
            threadId,
            resourceId: resourceId,
            role: msg.role,
            createdAt: startDateTime.add(index, "second").toDate(),
            type: (content as TextMessage).type,
            content: [content],
          });
        } else if (content.type === "tool-call") {
          _messages.push({
            id: uuidv4(),
            threadId,
            resourceId: resourceId,
            role: msg.role,
            createdAt: startDateTime.add(index, "second").toDate(),
            type: "tool-call",
            content: [content],
          });

          if (content.result) {
            _messages.push({
              id: uuidv4(),
              threadId,
              resourceId: resourceId,
              role: "tool",
              createdAt: startDateTime.add(index, "second").toDate(),
              type: "tool-result",
              content: [
                {
                  toolCallId: content.toolCallId,
                  toolName: content.toolName,
                  result: content.result,
                  type: "tool-result",
                },
              ],
            });
          }
        }
      }
    }
  }

  return _messages;
};
