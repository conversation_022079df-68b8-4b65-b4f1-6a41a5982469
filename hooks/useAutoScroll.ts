import { nextTick, onMounted, onUnmounted, ref } from "vue";

export function useAutoScroll(
  scrollContainerRef: Ref<HTMLElement | undefined>,
  isRunning: Ref<boolean>
) {
  const isAutoScrollEnabled = ref(false);
  const isProgrammaticScrolling = ref(false);
  const userHasScrolled = ref(false);
  const isAtBottom = ref(false);

  let scrollToBottomId: number | null = null;
  let userScrollCheckId: number | null = null;
  let contentChangeDebounceId: NodeJS.Timeout | null = null;

  const checkIfAtBottom = () => {
    const scrollTop = scrollContainerRef.value?.scrollTop ?? 0;
    const windowHeight = scrollContainerRef.value?.clientHeight ?? 0;
    const scrollHeight = scrollContainerRef.value?.scrollHeight ?? 0;
    isAtBottom.value = scrollTop + windowHeight >= scrollHeight - 100;
  };

  const _scrollToBottom = (behavior: "smooth" | "instant" = "smooth") => {
    if (scrollToBottomId) {
      cancelAnimationFrame(scrollToBottomId);
    }

    scrollToBottomId = requestAnimationFrame(() => {
      scrollContainerRef.value?.scrollTo({
        top: scrollContainerRef.value?.scrollHeight ?? 0,
        behavior: behavior,
      });
    });
  };

  const handleAutoScroll = () => {
    if (!isAutoScrollEnabled.value || !scrollContainerRef.value) {
      return;
    }

    isProgrammaticScrolling.value = true;
    _scrollToBottom();
  };

  const handleScroll = async () => {
    if (isProgrammaticScrolling.value) {
      //console.log("is programmatic scroll, ignore handleScroll");
      return;
    }

    await nextTick();
    if (userScrollCheckId) {
      cancelAnimationFrame(userScrollCheckId);
    }

    userScrollCheckId = requestAnimationFrame(() => {
      checkIfAtBottom();

      if (isAutoScrollEnabled.value) {
        userHasScrolled.value = true;
        isAutoScrollEnabled.value = false;
        //console.log("user scroll detected, disable auto scroll");
      }

      if (isAtBottom.value && isRunning.value && !isAutoScrollEnabled.value) {
        //console.log("user scroll to bottom, enable auto scroll");
        userHasScrolled.value = false;
        isAutoScrollEnabled.value = true;
      }
    });
  };

  const handleScrollEnd = () => {
    if (isProgrammaticScrolling.value) {
      //console.log(">>> scroll end, reset programmatic scrolling");
      isProgrammaticScrolling.value = false;
    }
  };

  const triggerScrollToBottom = (smooth: "smooth" | "instant" = "smooth") => {
    isAutoScrollEnabled.value = true;
    userHasScrolled.value = false;
    isProgrammaticScrolling.value = true;
    _scrollToBottom(smooth);
  };

  const resetAutoScroll = () => {
    isAutoScrollEnabled.value = false;
    userHasScrolled.value = false;
    isAtBottom.value = false;
    isProgrammaticScrolling.value = false;
  };

  let mutationObserver: MutationObserver | null = null;
  let resizeObserver: ResizeObserver | null = null;

  const startModernObservers = () => {
    if (!import.meta.client || !scrollContainerRef.value) return;

    try {
      // ResizeObserver: 监控容器大小变化
      resizeObserver = new ResizeObserver(() => {
        if (!userHasScrolled.value && isAutoScrollEnabled.value) {
          if (isRunning.value) {
            handleAutoScroll();
          }
        }
      });

      resizeObserver.observe(scrollContainerRef.value);
    } catch (error) {
      console.warn("ResizeObserver not supported:", error);
    }

    try {
      mutationObserver = new MutationObserver((mutations) => {
        let shouldScroll = false;

        mutations.forEach((mutation) => {
          if (mutation.type === "childList") {
            if (
              mutation.addedNodes.length > 0 ||
              mutation.removedNodes.length > 0
            ) {
              shouldScroll = true;
            }
          }

          if (mutation.type === "characterData") {
            shouldScroll = true;
          }

          if (
            mutation.type === "attributes" &&
            (mutation.attributeName === "style" ||
              mutation.attributeName === "class")
          ) {
            shouldScroll = true;
          }
        });

        if (
          shouldScroll &&
          isRunning.value &&
          isAutoScrollEnabled.value &&
          !userHasScrolled.value
        ) {
          if (contentChangeDebounceId) {
            clearTimeout(contentChangeDebounceId);
          }

          contentChangeDebounceId = setTimeout(() => {
            nextTick(() => {
              handleAutoScroll();
            });
          }, 50);
        }
      });

      mutationObserver.observe(scrollContainerRef.value, {
        childList: true,
        subtree: true,
        characterData: true,
        attributes: true,
        attributeFilter: ["style", "class"],
      });
    } catch (error) {
      console.warn("MutationObserver not supported:", error);
    }
  };

  const stopModernObservers = () => {
    if (resizeObserver) {
      resizeObserver.disconnect();
      resizeObserver = null;
    }

    if (mutationObserver) {
      mutationObserver.disconnect();
      mutationObserver = null;
    }
  };

  onMounted(() => {
    scrollContainerRef.value?.addEventListener("scroll", handleScroll, {
      passive: true,
    });
    scrollContainerRef.value?.addEventListener("scrollend", handleScrollEnd);

    checkIfAtBottom();
    startModernObservers();
  });

  onUnmounted(() => {
    scrollContainerRef.value?.removeEventListener("scroll", handleScroll);
    scrollContainerRef.value?.removeEventListener("scrollend", handleScrollEnd);
    stopModernObservers();

    if (scrollToBottomId) {
      cancelAnimationFrame(scrollToBottomId);
    }

    if (userScrollCheckId) {
      cancelAnimationFrame(userScrollCheckId);
    }

    if (contentChangeDebounceId) {
      clearTimeout(contentChangeDebounceId);
    }
  });

  return {
    isAutoScrollEnabled: readonly(isAutoScrollEnabled),
    triggerScrollToBottom,
    resetAutoScroll,
  };
}
