import { ref } from "vue";

export function useTimer() {
  const currentElapsedTime = ref(0);
  const timer = ref<ReturnType<typeof setInterval> | null>(null);
  const startTime = ref<number | null>(null);

  const startTimer = () => {
    if (timer.value) {
      clearInterval(timer.value);
    }
    startTime.value = Date.now();
    currentElapsedTime.value = 0;

    // 开始时使用150ms间隔
    timer.value = setInterval(() => {
      if (startTime.value) {
        const elapsed = Date.now() - startTime.value;
        currentElapsedTime.value = elapsed;

        // 当超过1秒时，切换到1秒间隔
        if (elapsed >= 1000 && timer.value) {
          clearInterval(timer.value);
          timer.value = setInterval(() => {
            if (startTime.value) {
              currentElapsedTime.value = Date.now() - startTime.value;
            }
          }, 1000);
        }
      }
    }, 150);
  };

  const stopTimer = () => {
    if (timer.value) {
      clearInterval(timer.value);
      timer.value = null;
    }
  };

  return {
    startTimer,
    stopTimer,
    timer,
    currentElapsedTime,
  };
}
