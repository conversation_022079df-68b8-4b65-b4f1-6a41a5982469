<script setup lang="ts">
interface Props {
  baseColor?: string;
  animatedColor?: string;
  duration?: string;
  animated?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  baseColor: "rgb(156 163 175)",
  animatedColor: "rgb(58, 124, 165)",
  duration: "1s",
  animated: true,
});
</script>

<template>
  <div class="relative sweep-container">
    <!-- 基础层 -->
    <div class="sweep-base" :style="{ color: props.baseColor }">
      <slot />
    </div>

    <!-- 动画层（带平行四边形遮罩） -->
    <div
      v-if="props.animated"
      class="sweep-animated"
      :style="{
        color: props.animatedColor,
        animationDuration: props.duration,
      }"
    >
      <slot />
    </div>
  </div>
</template>

<style scoped>
.sweep-container {
  display: inline-block;
}

.sweep-base {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.sweep-animated {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: parallelogram-sweep infinite linear;
}

@keyframes parallelogram-sweep {
  0% {
    clip-path: polygon(-60px 0, 0px 0, -30px 100%, -90px 100%);
  }
  100% {
    clip-path: polygon(
      calc(100% + 30px) 0,
      calc(100% + 60px) 0,
      calc(100% + 30px) 100%,
      100% 100%
    );
  }
}
</style>
