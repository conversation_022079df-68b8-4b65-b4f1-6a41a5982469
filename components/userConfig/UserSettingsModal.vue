<template>
  <Dialog v-model:open="open">
    <DialogContent class="max-w-4xl max-h-[80vh] p-0">
      <DialogHeader
        class="px-4 py-3 border-b flex flex-row items-center justify-between"
      >
        <DialogTitle class="text-xl font-normal text-gray-800"
          >Setting</DialogTitle
        >
      </DialogHeader>

      <div class="flex h-[calc(80vh-108px)]">
        <!-- 左侧菜单 -->
        <div class="w-60 border-r border-gray-200 p-4">
          <nav class="space-y-2">
            <button
              v-for="item in menuItems"
              :key="item.key"
              :class="[
                'w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors',
                activeTab === item.key
                  ? 'bg-[#e2ecf2]  text-theme-color '
                  : 'hover:bg-gray-100 text-gray-800 ',
              ]"
              @click="activeTab = item.key"
            >
              <component :is="item.icon" class="w-5 h-5" />
              <span class="font-medium">{{ item.label }}</span>
            </button>
          </nav>
        </div>

        <!-- 右侧内容区域 -->
        <div class="flex-1 p-6 overflow-y-auto">
          <!-- General Set -->
          <div v-if="activeTab === 'general'" />

          <!-- Custom Prompt -->
          <div v-if="activeTab === 'customPrompt'">
            <UserCustomPromptContent @close="handleClose" />
          </div>

          <!-- Memory -->
          <div v-if="activeTab === 'memory'">
            <UserMemoryContent @close="handleClose" />
          </div>
          <!-- UserInfo Settings -->
          <div v-if="activeTab === 'userInfo'">
            <UserInfoContent @close="handleClose" />
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  SettingsIcon,
  Settings2Icon,
  NotebookPenIcon,
  UserIcon,
} from "lucide-vue-next";
import UserCustomPromptContent from "./UserCustomPromptContent.vue";
import UserMemoryContent from "./UserMemoryContent.vue";
import UserInfoContent from "./UserInfoContent.vue";

interface Props {
  open: boolean;
}

interface Emits {
  (e: "update:open", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const open = computed({
  get: () => props.open,
  set: (value) => emit("update:open", value),
});

const activeTab = ref("userInfo");

const menuItems = [
  // {
  //   key: "general",
  //   label: "General",
  //   icon: SettingsIcon,
  // },
  {
    key: "userInfo",
    label: "User Info",
    icon: UserIcon,
  },
  {
    key: "customPrompt",
    label: "Custom Prompt",
    icon: Settings2Icon,
  },
  {
    key: "memory",
    label: "Memory",
    icon: NotebookPenIcon,
  },
];

const handleClose = () => {
  open.value = false;
};
watch(open, (newVal) => {
  if (!newVal) {
    activeTab.value = "userInfo";
  }
});
</script>
