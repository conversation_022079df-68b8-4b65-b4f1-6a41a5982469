<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useSidebar } from "@/components/ui/sidebar";
import { UserStore } from "~/lib/userStore";
import UserSettingsModal from "./UserSettingsModal.vue";
import LogoutDialog from "./LogoutDialog.vue";
import { SettingsIcon, LogOutIcon, User as UserIcon } from "lucide-vue-next";
import { cn } from "@/lib/utils";

const { state } = useSidebar();
const userSettings = ref(UserStore.getSettings());
const showLogoutDialog = ref(false);
const showSettings = ref(false);
const resourceId = useCookie("resourceId");

// 计算用户头像
const userAvatar = computed(() => {
  const username = resourceId.value;
  if (!username) {
    return null;
  }
  return username.charAt(0).toUpperCase();
});

const handleLogout = () => {
  //   UserStore.clearAllData(); // 清除所有用户数据
  showLogoutDialog.value = false;
};

const openSettings = () => {
  showSettings.value = true;
};

// 刷新用户设置
const refreshUserSettings = () => {
  userSettings.value = UserStore.getSettings();
};

// 监听设置模态框关闭，刷新用户设置
const onSettingsModalClose = () => {
  refreshUserSettings();
};

onMounted(() => {
  refreshUserSettings();

  if (!resourceId.value) {
    throw new Error("resourceId not found");
  }

  if (!userSettings.value.username) {
    UserStore.saveSettings({
      systemPrompt: "",
      rolename: "",
      referToSavedMemories: true,
      referToHistoricalChatRecords: true,
      userMemories: [],
      username: resourceId.value,
    });
    refreshUserSettings();
  }
});
</script>

<template>
  <div class="p-3">
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <div
          class="w-full flex items-center justify-center gap-3 p-2 rounded-lg hover:bg-gray-100 transition-colors"
        >
          <!-- 用户头像 -->
          <Avatar class="w-8 h-8">
            <AvatarFallback
              :class="
                cn(
                  ' text-white font-medium text-sm flex items-center justify-center',
                  userAvatar ? 'bg-[#3a7ca5]' : 'bg-[#bfbfbf]'
                )
              "
            >
              <template v-if="userAvatar">{{ userAvatar }}</template>
              <template v-else><UserIcon class="w-5 h-5" /></template>
            </AvatarFallback>
          </Avatar>

          <!-- 用户名 (仅在展开状态显示) -->
          <div
            v-if="state === 'expanded'"
            class="flex-1 text-left overflow-hidden"
          >
            <div class="text-sm font-medium text-gray-900">
              {{ userSettings.username || "Log in" }}
            </div>
            <div
              class="text-xs text-gray-500 whitespace-nowrap overflow-hidden text-ellipsis"
            >
              {{ userSettings.username ? userSettings.rolename : "" }}
            </div>
          </div>
        </div>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="center" class="w-48">
        <DropdownMenuItem @click="openSettings">
          <SettingsIcon class="w-6 h-6 mr-1" />
          Setting
        </DropdownMenuItem>
        <DropdownMenuItem
          v-if="userSettings.username"
          @click="showLogoutDialog = true"
        >
          <LogOutIcon class="w-6 h-6 mr-1" />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    <!-- Logout confirmation dialog -->
    <LogoutDialog
      v-model:open="showLogoutDialog"
      :handle-logout="handleLogout"
    />

    <!-- 设置 -->
    <UserSettingsModal
      v-model:open="showSettings"
      @update:open="onSettingsModalClose"
    />
  </div>
</template>

<style scoped>
/* 折叠状态下居中头像 */
.sidebar[data-state="collapsed"] .user-profile {
  justify-content: center;
}

.sidebar[data-state="collapsed"] .user-profile .user-info {
  display: none;
}
</style>
