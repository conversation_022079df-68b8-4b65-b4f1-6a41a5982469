<template>
  <div class="space-y-6">
    <div>
      <div class="space-y-4 text-gray-700">
        <div class="space-y-2">
          <Label for="rolename" class="text-base font-medium">
            Your role
          </Label>
          <Input
            id="rolename"
            v-model="tempSettings.rolename"
            placeholder="Network Administrator"
            class="input-base"
          />
        </div>

        <div class="space-y-2">
          <Label for="system-prompt" class="text-base font-medium">
            Custom prompt
          </Label>
          <Textarea
            id="system-prompt"
            v-model="tempSettings.systemPrompt"
            placeholder="How Senti<PERSON> perform?"
            rows="15"
            class="input-base"
          />
        </div>
      </div>

      <div class="flex justify-end gap-3 mt-6">
        <Button
          size="sm"
          class="btn-base btn-primary rounded-full"
          @click="saveSettings"
        >
          Save
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { toast } from "vue-sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { UserStore, type UserSettings } from "~/lib/userStore";

const settings = ref<UserSettings>({ ...UserStore.getSettings() });
const tempSettings = ref<UserSettings>({ ...settings.value });

// 加载设置
const loadSettings = () => {
  settings.value = { ...UserStore.getSettings() };
  tempSettings.value = { ...settings.value };
};

const saveSettings = () => {
  UserStore.saveSettings(tempSettings.value);
  settings.value = { ...tempSettings.value };
  toast.success("Custom prompt settings saved successfully!");
};

// 初始化时加载设置
loadSettings();
</script>

<style scoped lang="scss">
.input-base {
  transition: border-color 0.2s;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-wrap: break-word;
}
.input-base:focus {
  border-color: #3a7ca5;
  box-shadow: none;
}
.input-base::placeholder {
  color: #9ca3af;
}

.btn-base {
  padding: 0.5rem 1rem;
  transition: background 0.2s, color 0.2s, border-color 0.2s;
}
.btn-outline {
  border: 1px solid #d1d5db;
  background: #fff;
  color: #374151;
}
.btn-outline:hover {
  background: #f9fafb;
}
.btn-primary {
  background: #3a7ca5;
  color: #fff;
}
</style>
