<template>
  <div class="space-y-6 text-gray-700">
    <!-- 设置视图  -->
    <div v-if="currentView === 'settings'">
      <h3 class="text-xl mb-4 font-normal">Memory Settings</h3>

      <div class="space-y-6">
        <!-- Refer to saved memories -->
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium text-base">Refer to saved memories</div>
              <div class="text-xs text-gray-500 mt-1">
                Toggle on to allow <PERSON><PERSON><PERSON> to save and utilize memories in its
                responses.
              </div>
            </div>
            <Switch v-model="referToSavedMemories" />
          </div>
        </div>

        <!-- Refer to historical chat records -->
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium text-base">
                Refer to historical chat records
              </div>
              <div class="text-xs text-gray-500 mt-1">
                Toggle on to enable <PERSON><PERSON><PERSON> to refer to the most recent
                conversations when replying.
              </div>
            </div>
            <Switch v-model="referToHistoricalChatRecords" />
          </div>
        </div>

        <!-- Manage memories -->
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium text-base">Manage memories</div>
              <div class="text-xs text-gray-500 mt-1">
                Click "Manage" to handle the memories.
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              class="h-8 px-3 rounded-full"
              @click="currentView = 'list'"
            >
              Manage
            </Button>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="flex justify-end gap-3 mt-6">
        <Button
          size="sm"
          class="btn-base btn-primary rounded-full"
          @click="saveSettings"
        >
          Save
        </Button>
      </div>
    </div>

    <!-- 记忆管理视图 -->
    <div v-else-if="currentView === 'list'">
      <div class="flex items-center mb-4 justify-between">
        <h3 class="text-xl font-normal">Memories saved</h3>
        <ArrowLeft
          class="h-5 w-5 cursor-pointer relative -translate-x-1"
          @click="goBack"
        />
      </div>

      <div>
        <div v-if="savedMemories.length === 0" class="space-y-2 mb-6">
          <div class="text-base text-gray-500">No memories saved.</div>
        </div>
        <div v-else class="space-y-4 mb-6">
          <div
            v-for="memory in savedMemories"
            :key="memory"
            class="flex items-center justify-between"
          >
            <span class="text-sm">{{ memory }}</span>
            <Button
              variant="ghost"
              size="icon"
              class="h-6 w-6 rounded-full text-gray-400 hover:text-red-500"
              @click="deleteMemory(memory)"
            >
              <Trash2 class="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div v-if="showAddInput" class="space-y-3 mb-4">
          <div class="relative">
            <Textarea
              v-model="newMemoryInput"
              type="text"
              placeholder="Add memory here"
              class="flex-1 text-sm textarea-base pr-20"
              @keyup.enter="addMemory"
            />
            <Button
              size="sm"
              class="px-4 absolute bottom-2 right-2 z-10 rounded-full"
              :class="{
                'bg-title-bg text-default-text ': newMemoryInput.trim() === '',
              }"
              :disabled="newMemoryInput.trim() === ''"
              @click="addMemory"
            >
              Submit
            </Button>
          </div>
          <!-- 提示消息 -->
          <div
            v-if="showMessage"
            :class="[
              'text-sm',
              addMemoryMessage.includes('successful')
                ? ' text-green-700 '
                : ' text-red-700 ',
            ]"
          >
            {{ addMemoryMessage }}
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="flex gap-2 justify-end">
          <Button
            v-if="!showAddInput"
            variant="outline"
            class="rounded-full"
            size="sm"
            @click="showAddMemory"
          >
            Add
          </Button>
          <Button
            v-else
            variant="outline"
            size="sm"
            class="rounded-full"
            @click="
              showAddInput = false;
              newMemoryInput = '';
              showMessage = false;
            "
          >
            Cancel
          </Button>
          <Button
            variant="outline"
            size="sm"
            class="text-red-500 hover:text-red-600 rounded-full"
            @click="deleteAllMemories"
          >
            Delete ALL
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Trash2, ArrowLeft } from "lucide-vue-next";
import { UserStore } from "@/lib/userStore";
import { toast } from "vue-sonner";

// 视图状态管理
const currentView = ref<"settings" | "list">("settings");
const showAddInput = ref(false);
const newMemoryInput = ref("");
const addMemoryMessage = ref("");
const showMessage = ref(false);

// 从userStore获取数据
const userSettings = computed(() => UserStore.getSettings());
const referToSavedMemories = ref(userSettings.value.referToSavedMemories);
const referToHistoricalChatRecords = ref(
  userSettings.value.referToHistoricalChatRecords
);
const savedMemories = ref(userSettings.value.userMemories);

// 刷新记忆列表
const refreshMemories = () => {
  const settings = UserStore.getSettings();
  savedMemories.value = settings.userMemories;
};
// 保存设置
const saveSettings = () => {
  UserStore.updateMemorySettings(
    referToSavedMemories.value,
    referToHistoricalChatRecords.value
  );
  toast.success("Memory settings saved successfully!");
};

const deleteMemory = (memory: string) => {
  UserStore.removeUserMemory(memory);
  refreshMemories();
};

// 添加新记忆
const addMemory = () => {
  if (newMemoryInput.value.trim()) {
    const result = UserStore.addUserMemory(newMemoryInput.value.trim());

    // 显示提示信息
    addMemoryMessage.value = result.message;
    showMessage.value = true;

    if (result.success) {
      newMemoryInput.value = "";
      refreshMemories();
      showMessage.value = false;
      showAddInput.value = false;
    } else {
      setTimeout(() => {
        showMessage.value = false;
      }, 3000);
    }
  }
};

// 删除所有记忆
const deleteAllMemories = () => {
  UserStore.clearAllUserMemories();
  refreshMemories(); // 刷新列表
};

// 显示添加记忆输入框
const showAddMemory = () => {
  newMemoryInput.value = "";
  addMemoryMessage.value = "";
  showMessage.value = false;
  showAddInput.value = true;
};

// 返回上一级视图
const goBack = () => {
  if (currentView.value === "list") {
    currentView.value = "settings";
    showAddInput.value = false; // 重置输入框状态
  }
};

// 初始化数据
const initData = () => {
  const settings = UserStore.getSettings();
  referToSavedMemories.value = settings.referToSavedMemories;
  referToHistoricalChatRecords.value = settings.referToHistoricalChatRecords;
  refreshMemories();
};

// 初始化
initData();
</script>

<style scoped>
.textarea-base {
  transition: border-color 0.2s;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-wrap: break-word;
  padding-bottom: 2.5rem;
  padding-right: 5.5rem;
  resize: none;
}
.textarea-base:focus {
  border-color: #3a7ca5;
  box-shadow: none;
}
.btn-base {
  padding: 0.5rem 1rem;
  transition: background 0.2s, color 0.2s, border-color 0.2s;
}
.btn-outline {
  border: 1px solid #d1d5db;
  background: #fff;
  color: #374151;
}
.btn-outline:hover {
  background: #f9fafb;
}
.btn-primary {
  background: #3a7ca5;
  color: #fff;
}
</style>
