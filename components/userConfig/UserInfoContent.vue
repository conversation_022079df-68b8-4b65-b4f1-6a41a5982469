<template>
  <div class="space-y-6">
    <div class="space-y-4">
      <div class="space-y-2">
        <Label for="username">Username</Label>
        <Input
          id="username"
          v-model="username"
          type="text"
          placeholder="Enter your username"
          class="input-base"
        />
      </div>
    </div>

    <div class="flex gap-2 justify-end">
      <Button class="rounded-full" variant="outline" @click="reset">
        Reset
      </Button>
      <Button class="rounded-full" :disabled="!isFormValid" @click="save">
        Save
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UserStore } from "~/lib/userStore";
import { toast } from "vue-sonner";

const username = ref("");

const isFormValid = computed(() => {
  return username.value.trim() !== "";
});

onMounted(() => {
  const settings = UserStore.getSettings();
  username.value = settings.username || "";
});

const save = () => {
  if (!isFormValid.value) {
    return;
  }
  UserStore.updateUsername(username.value.trim());
  toast.success("Username saved successfully!");
};

const reset = () => {
  username.value = UserStore.getSettings().username || "";
};
</script>
<style scoped lang="scss">
.input-base {
  transition: border-color 0.2s;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-wrap: break-word;
}
.input-base:focus {
  border-color: #3a7ca5;
  box-shadow: none;
}
.input-base::placeholder {
  color: #9ca3af;
}
</style>
