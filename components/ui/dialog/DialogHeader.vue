<script setup lang="ts">
import type { HTMLAttributes } from "vue";
import { cn } from "@/lib/utils";
import { DialogClose } from "reka-ui";
import { X } from "lucide-vue-next";

const props = defineProps<{
  class?: HTMLAttributes["class"];
}>();
</script>

<template>
  <div
    data-slot="dialog-header"
    :class="cn('flex flex-row items-center justify-between gap-2', props.class)"
  >
    <div class="flex items-center min-w-0">
      <slot />
    </div>
    <DialogClose
      class="flex items-center cursor-pointer ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 hover:[&_svg]:scale-125 ml-2"
    >
      <X />
      <span class="sr-only">Close</span>
    </DialogClose>
  </div>
</template>
