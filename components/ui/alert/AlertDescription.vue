<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
  size?: 'sm' | 'base' | 'lg'
}>()

const sizeClasses = {
  sm: 'text-sm',
  base: 'text-base',
  lg: 'text-lg font-medium'
}
</script>

<template>
  <div
    data-slot="alert-description"
    :class="cn('text-muted-foreground col-start-2 grid justify-items-start gap-1 [&_p]:leading-relaxed', sizeClasses[size || 'sm'], props.class)"
  >
    <slot />
  </div>
</template>
