<script lang="ts" setup>
import type { RangeCalendarCellTriggerProps } from "reka-ui";
import type { HTMLAttributes } from "vue";
import { reactiveOmit } from "@vueuse/core";
import { RangeCalendarCellTrigger, useForwardProps } from "reka-ui";
import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";

const props = withDefaults(
  defineProps<
    RangeCalendarCellTriggerProps & { class?: HTMLAttributes["class"] }
  >(),
  {
    as: "button",
    class: undefined,
  }
);

const delegatedProps = reactiveOmit(props, "class");

const forwardedProps = useForwardProps(delegatedProps);
</script>

<template>
  <RangeCalendarCellTrigger
    data-slot="range-calendar-trigger"
    :class="
      cn(
        buttonVariants({ variant: 'ghost' }),
        'h-8 w-8 p-0 font-normal data-[selected]:opacity-100',
        'data-[selected]:bg-accent data-[selected]:text-accent-foreground',
        'data-[selected]:hover:bg-accent data-[selected]:hover:text-primary-foreground data-[selected]:focus:bg-accent data-[selected]:focus:text-accent-foreground',
        // In-range but not start/end
        'data-[selected]:not([data-selection-start]):not([data-selection-end]):rounded-none',
        // Default
        'rounded-full',
        '[&[data-today]:not([data-selected])]:bg-accent [&[data-today]:not([data-selected])]:text-accent-foreground',

        // Selection Start
        'data-[selection-start]:bg-primary data-[selection-start]:text-primary-foreground',
        'data-[selection-start]:hover:bg-primary data-[selection-start]:hover:text-primary-foreground data-[selection-start]:focus:bg-primary data-[selection-start]:focus:text-primary-foreground',

        // Selection End
        'data-[selection-end]:bg-primary data-[selection-end]:text-primary-foreground',
        'data-[selection-end]:hover:bg-primary data-[selection-end]:hover:text-primary-foreground data-[selection-end]:focus:bg-primary data-[selection-end]:focus:text-primary-foreground',

        // Highlighted (hover preview)
        'data-[highlighted]:bg-accent data-[highlighted]:text-accent-foreground',
        'data-[highlighted-start]:bg-primary data-[highlighted-start]:text-primary-foreground',
        'data-[highlighted-end]:bg-primary data-[highlighted-end]:text-primary-foreground',
        // Outside months
        'data-[outside-view]:text-muted-foreground',
        // Disabled
        'data-[disabled]:text-muted-foreground data-[disabled]:opacity-50',
        // Unavailable
        'data-[unavailable]:text-destructive-foreground data-[unavailable]:line-through',
        props.class
      )
    "
    v-bind="forwardedProps"
  >
    <slot />
  </RangeCalendarCellTrigger>
</template>
