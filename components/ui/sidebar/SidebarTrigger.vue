<script setup lang="ts">
import { cn } from "@/lib/utils";
import { useSidebar } from "./utils";

export type TogglerPosition = "left" | "right" | "top" | "bottom";

interface Props {
  showDefaultBar?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  class: "",
  showDefaultBar: true,
});

const { toggleSidebar, state } = useSidebar();

const triggerVisible = ref(false);
</script>

<template>
  <div
    class="h-full w-4 absolute top-0 bottom-0 z-[9998] right-[-2px] cursor-pointer"
    @mouseenter="triggerVisible = true"
    @mouseleave="triggerVisible = false"
    @click="toggleSidebar"
  >
    <!-- 触发器容器 -->
    <div
      class="flex h-10 w-4 items-center justify-center cursor-pointer absolute top-1/2 transform -translate-y-1/2 z-[9999] group right-0"
    >
      <div
        :class="
          cn(
            'flex h-10 w-4 items-center flex-col relative justify-center ',
            state === 'expanded' ? 'left-1' : 'left-1.5'
          )
        "
      >
        <!-- 动画效果：从竖线变为箭头 -->
        <div class="w-full h-full flex flex-col justify-center items-center">
          <!-- 第一条线 -->
          <div
            :class="
              cn(
                'h-8 w-1 rounded-t-full bg-[#bfbfbf] transition-all duration-300 ease-in-out',
                triggerVisible
                  ? state === 'expanded'
                    ? 'translate-y-[0.15rem] rotate-[15deg] rounded-b-full'
                    : 'translate-y-[0.15rem] rotate-[-15deg] rounded-b-full'
                  : props.showDefaultBar
                  ? 'translate-y-0 rotate-0 h-8'
                  : 'opacity-0'
              )
            "
          />
          <!-- 第二条线 -->
          <div
            :class="
              cn(
                'h-8 w-1 rounded-b-full bg-[#bfbfbf] transition-all duration-300 ease-in-out',
                triggerVisible
                  ? state === 'expanded'
                    ? 'translate-y-[-0.15rem] rotate-[-15deg] rounded-t-full'
                    : 'translate-y-[-0.15rem] rotate-[15deg] rounded-t-full'
                  : props.showDefaultBar
                  ? 'translate-y-0 rotate-0 h-8 relative'
                  : 'opacity-0'
              )
            "
          />
        </div>
      </div>
    </div>
  </div>
</template>
