<script setup lang="ts">
import { CalendarDate } from "@internationalized/date";
import dayjs from "dayjs";
import { Calendar } from "lucide-vue-next";
import { useDateFormatter } from "reka-ui";
import { computed, ref, withDefaults } from "vue";
import { Button } from "@/components/ui/button";
import { RangeCalendar } from "@/components/ui/range-calendar";
import { cn } from "@/lib/utils";

interface QuickSelection {
  label: string;
  getValue: () => { start: Date; end: Date };
}

const props = withDefaults(
  defineProps<{
    value: { start: Date; end: Date };
    quickSelections?: QuickSelection[];
  }>(),
  {
    quickSelections: () => [
      {
        label: "Today",
        getValue: () => {
          const start = dayjs().startOf("day").toDate();
          const end = dayjs().endOf("day").toDate();
          return { start, end };
        },
      },
      {
        label: "Yesterday",
        getValue: () => {
          const start = dayjs().subtract(1, "day").startOf("day").toDate();
          const end = dayjs().subtract(1, "day").endOf("day").toDate();
          return { start, end };
        },
      },
      {
        label: "Last 7 Days",
        getValue: () => {
          const end = dayjs().endOf("day").toDate();
          const start = dayjs().subtract(6, "days").startOf("day").toDate();
          return { start, end };
        },
      },
      {
        label: "Last 30 Days",
        getValue: () => {
          const end = dayjs().endOf("day").toDate();
          const start = dayjs().subtract(29, "days").startOf("day").toDate();
          return { start, end };
        },
      },
    ],
  }
);

const emit = defineEmits<{
  "update:value": [value: { start: Date; end: Date }];
}>();

const opened = ref(false);
const internalValue = ref(props.value);

const calendarValue = computed({
  get: () => ({
    start: new CalendarDate(
      internalValue.value.start.getFullYear(),
      internalValue.value.start.getMonth() + 1,
      internalValue.value.start.getDate()
    ),
    end: new CalendarDate(
      internalValue.value.end.getFullYear(),
      internalValue.value.end.getMonth() + 1,
      internalValue.value.end.getDate()
    ),
  }),
  set: (value) => {
    const newStart = new Date(internalValue.value.start);
    newStart.setFullYear(value.start.year);
    newStart.setMonth(value.start.month - 1);
    newStart.setDate(value.start.day);

    const newEnd = new Date(internalValue.value.end);
    newEnd.setFullYear(value.end.year);
    newEnd.setMonth(value.end.month - 1);
    newEnd.setDate(value.end.day);

    internalValue.value = {
      start: newStart,
      end: newEnd,
    };
  },
});

const timePickerValue = computed({
  get: () => ({
    start: internalValue.value.start,
    end: internalValue.value.end,
  }),
  set: (value) => {
    const newStart = new Date(internalValue.value.start);
    if (value.start) {
      newStart.setHours(value.start.getHours());
      newStart.setMinutes(value.start.getMinutes());
      newStart.setSeconds(value.start.getSeconds());
      newStart.setMilliseconds(value.start.getMilliseconds());
    }

    const newEnd = new Date(internalValue.value.end);
    if (value.end) {
      newEnd.setHours(value.end.getHours());
      newEnd.setMinutes(value.end.getMinutes());
      newEnd.setSeconds(value.end.getSeconds());
      newEnd.setMilliseconds(value.end.getMilliseconds());
    }

    internalValue.value = {
      start: newStart,
      end: newEnd,
    };

    // emit("update:value", internalValue.value);
  },
});

const formatter = useDateFormatter("en-US");

const updateTimePickerValue = (key: "start" | "end", date: Date) => {
  if (key === "start") {
    timePickerValue.value = {
      ...timePickerValue.value,
      start: date,
    };
  } else {
    timePickerValue.value = {
      ...timePickerValue.value,
      end: date,
    };
  }
};

const clear = () => {
  internalValue.value = {
    start: new Date(new Date().setHours(0, 0, 0, 0)),
    end: new Date(new Date().setHours(23, 59, 59, 999)),
  };
};

const confirm = () => {
  emit("update:value", internalValue.value);
  opened.value = false;
};

const selectQuickRange = (selection: QuickSelection) => {
  internalValue.value = selection.getValue();
};
</script>

<template>
  <Popover v-model:open="opened">
    <PopoverTrigger as-child>
      <Button
        variant="outline"
        :class="
          cn(
            'w-fit justify-start text-left font-normal',
            !value && 'text-muted-foreground'
          )
        "
      >
        <Calendar class="mr-2 h-4 w-4" />
        <template v-if="value.start">
          <template v-if="value.end">
            {{
              formatter.custom(internalValue.start, {
                dateStyle: "medium",
                timeStyle: "short",
              })
            }}
            -
            {{
              formatter.custom(internalValue.end, {
                dateStyle: "medium",
                timeStyle: "short",
              })
            }}
          </template>
        </template>
        <template v-else> Pick a date </template>
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-auto p-0">
      <div class="flex">
        <div class="flex flex-col w-40 gap-1 p-2 border-r">
          <Button
            v-for="selection in quickSelections"
            :key="selection.label"
            variant="ghost"
            class="justify-start w-full h-8 px-2 text-xs"
            @click="selectQuickRange(selection)"
          >
            {{ selection.label }}
          </Button>
        </div>
        <div>
          <RangeCalendar
            v-model="calendarValue"
            initial-focus
            :number-of-months="2"
          />

          <div class="flex justify-between text-xs">
            <div class="flex-1 px-3 flex gap-2 items-center justify-end">
              <span>Start Time</span>
              <TimePicker
                :date="timePickerValue.start"
                @update:date="updateTimePickerValue('start', $event)"
              />
            </div>
            <div class="flex-1 px-3 flex gap-2 items-center">
              <span>End Time</span>
              <TimePicker
                :date="timePickerValue.end"
                @update:date="updateTimePickerValue('end', $event)"
              />
            </div>
          </div>

          <div class="flex justify-end gap-2 p-2 border-t mt-4">
            <Button variant="outline" @click="clear">Clear</Button>
            <Button @click="confirm">Confirm</Button>
          </div>
        </div>
      </div>
    </PopoverContent>
  </Popover>
</template>
