<script setup lang="ts">
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { SearchIcon, ChartNoAxesCombinedIcon } from "lucide-vue-next";
import UserProfile from "./userConfig/UserProfile.vue";
import { useCurrentThreadStore } from "@/modules/sentire/stores/currentThreadStore";
import { Threads } from "@/modules/sentire/models/threads";

interface Props {
  collapsible?: "offcanvas" | "icon" | "none";
}

const props = withDefaults(defineProps<Props>(), {
  collapsible: "offcanvas",
});

const { state } = useSidebar();
const route = useRoute();
const resourceId = useCookie("resourceId", {
  watch: "shallow",
});

const handleSentireClick = async () => {
  // 如果当前已经在 sentire 页面，创建新线程
  if (route.path.startsWith("/sentire")) {
    const currentThreadStore = useCurrentThreadStore();
    const threads = new Threads();
    const thread = await threads.createThread(
      undefined,
      undefined,
      resourceId.value ?? ""
    );
    await currentThreadStore.initializeWithThreadId(thread.id);
    await navigateTo({
      path: "/sentire/",
      query: { threadId: thread.id },
    });
  } else {
    // 如果不在 sentire 页面，直接跳转
    await navigateTo("/sentire/");
  }
};

const items = computed(() => {
  return [
    {
      title: "Sentire",
      url: "/sentire/",
      icon: SearchIcon,
      active: route.path.startsWith("/sentire"),
      onClick: handleSentireClick,
    },
    {
      title: "Monitor",
      url: "/monitor/",
      icon: ChartNoAxesCombinedIcon,
      active: route.path.startsWith("/monitor"),
    },
  ];
});
</script>

<template>
  <Sidebar :collapsible="props.collapsible" class="sidebar-with-trigger">
    <div
      class="border-b h-16 min-h-16 flex items-center bg-brand"
      :class="state === 'expanded' ? 'px-5' : 'justify-center'"
    >
      <img
        v-if="state === 'expanded'"
        src="/images/logo.png"
        alt="logo"
        class="max-w-[180px] max-h-[30px]"
      />
      <img
        v-else
        src="/images/mini_logo.png"
        alt="mini_logo"
        class="max-w-10 max-h-6"
      />
    </div>
    <SidebarContent class="flex flex-col h-full">
      <SidebarGroup class="flex-1">
        <SidebarGroupContent class="px-3">
          <SidebarMenu>
            <SidebarMenuItem v-for="item in items" :key="item.title">
              <SidebarMenuButton
                as-child
                size="lg"
                :tooltip="state === 'collapsed' ? item.title : undefined"
              >
                <NuxtLink
                  v-if="!item.onClick"
                  :to="item.url"
                  :class="{
                    '!text-theme-color bg-[#e2ecf2]': item.active,
                    'justify-center !p-0': state === 'collapsed',
                  }"
                  class="sidebar-menu-item transition-colors duration-200 flex items-center"
                >
                  <component :is="item.icon" class="!size-[24px]" />
                  <span v-if="state === 'expanded'" class="text-[16px] pl-1">{{
                    item.title
                  }}</span>
                </NuxtLink>
                <button
                  v-else
                  :class="{
                    '!text-theme-color bg-[#e2ecf2]': item.active,
                    'justify-center !p-0': state === 'collapsed',
                  }"
                  class="sidebar-menu-item transition-colors duration-200 flex items-center w-full"
                  @click="item.onClick"
                >
                  <component :is="item.icon" class="!size-[24px]" />
                  <span v-if="state === 'expanded'" class="text-[16px] pl-1">{{
                    item.title
                  }}</span>
                </button>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
      <div>
        <ClientOnly>
          <UserProfile />
        </ClientOnly>
      </div>
    </SidebarContent>

    <!-- 右边边框触发区域 -->
    <SidebarTrigger :show-default-bar="true" />
  </Sidebar>
</template>

<style scoped>
.sidebar-menu-item {
  height: 36px !important;
  margin-bottom: 24px;
  color: #595959;

  &:hover {
    color: #3a7ca5 !important;
    background-color: #e2ecf2 !important;
  }

  &.justify-center {
    padding: 0 !important;
    justify-content: center !important;
  }

  &:not(.justify-center) {
    padding: 0 8px !important;
  }
}

.sidebar-with-trigger {
  position: relative;
}
</style>
