<script setup lang="ts">
import { CopyIcon, CopyCheckIcon } from "lucide-vue-next";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { useClipboard } from "@vueuse/core";
import { computed } from "vue";
import type { TextMessage, ToolCallMessage } from "@/types/message";

const props = withDefaults(
  defineProps<{
    copyData?:
      | Array<TextMessage | ToolCallMessage>
      | Record<string, unknown>
      | string;
    copyShow?: boolean;
    isToolBar?: boolean;
    status?: string;
  }>(),
  {
    copyData: () => [],
    copyShow: true,
    isToolBar: false,
    status: undefined,
  }
);

// 将 copyData 转换为可复制的文本
const copyText = computed(() => {
  if (!props.copyData) return "";

  if (typeof props.copyData === "string") {
    return props.copyData;
  }

  if (typeof props.copyData === "object" && !Array.isArray(props.copyData)) {
    return JSON.stringify(props.copyData, null, 2);
  }

  if (Array.isArray(props.copyData)) {
    return props.copyData
      .map((message) => (message.type === "text" ? message.text : ""))
      .join("\n");
  }

  return "";
});

const { copy, copied } = useClipboard({
  source: copyText,
});

const handleCopy = async () => {
  if (copyText.value) {
    await copy();
  }
};
</script>

<template>
  <Tooltip v-if="copyData && copyShow && copyText">
    <TooltipTrigger as-child>
      <CopyCheckIcon
        v-if="copied"
        :size="16"
        :class="[
          'cursor-pointer  hover:bg-stone-100 rounded-md p-2 transition-all duration-200 w-8 h-8 focus:outline-none focus:ring-0 focus:border-none',
          'text-green-500 hover:text-green-600',
          { 'relative -left-2': status !== 'idle' && status !== 'done' },
        ]"
        @click="handleCopy"
      />
      <CopyIcon
        v-else
        :size="16"
        :class="[
          'cursor-pointer  hover:bg-stone-100 rounded-md p-2 transition-all duration-200 w-8 h-8 focus:outline-none focus:ring-0 focus:border-none',
          'text-stone-400 hover:text-stone-500',
          {
            'relative -left-2':
              status !== 'idle' && status !== 'done' && isToolBar,
          },
        ]"
        @click="handleCopy"
      />
    </TooltipTrigger>
    <TooltipContent>
      <p>Copy</p>
    </TooltipContent>
  </Tooltip>
</template>
