<script setup lang="ts">
import { LoaderIcon } from "lucide-vue-next";

defineProps<{
  isPending: boolean;
}>();
</script>
<template>
  <div class="relative w-full h-full">
    <div :class="{ 'blur-xs': isPending }" class="w-full h-full">
      <slot />
    </div>

    <div
      v-show="isPending"
      class="absolute inset-0 bg-stone-500/10 z-10 w-full h-full top-0 left-0 flex items-center justify-center cursor-not-allowed rounded-xl overflow-hidden"
    >
      <LoaderIcon class="animate-spin" />
    </div>
  </div>
</template>
