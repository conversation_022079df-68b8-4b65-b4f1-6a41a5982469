CREATE SCHEMA "capehorn_app";
--> statement-breakpoint
CREATE TABLE "capehorn_app"."dashboard_config" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "capehorn_app"."dashboard_config_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"dashboard_id" integer,
	"dash_config" jsonb NOT NULL,
	"mini_card_config" jsonb NOT NULL,
	"version" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE "capehorn_app"."dashboard" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "capehorn_app"."dashboard_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"name" varchar(255) NOT NULL,
	"description" varchar(255),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"owner" varchar(255) NOT NULL,
	"thread_id" varchar(255) NOT NULL
);
--> statement-breakpoint
ALTER TABLE "capehorn_app"."dashboard_config" ADD CONSTRAINT "dashboard_config_dashboard_id_dashboard_id_fk" FOREIGN KEY ("dashboard_id") REFERENCES "capehorn_app"."dashboard"("id") ON DELETE cascade ON UPDATE no action;