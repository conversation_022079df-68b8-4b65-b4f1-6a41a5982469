{"id": "e4c47092-5207-4064-8a6e-4276d2399a15", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"capehorn_app.dashboard_config": {"name": "dashboard_config", "schema": "capehorn_app", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "dashboard_config_id_seq", "schema": "capehorn_app", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "dashboard_id": {"name": "dashboard_id", "type": "integer", "primaryKey": false, "notNull": false}, "dash_config": {"name": "dash_config", "type": "jsonb", "primaryKey": false, "notNull": true}, "mini_card_config": {"name": "mini_card_config", "type": "jsonb", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"dashboard_config_dashboard_id_dashboard_id_fk": {"name": "dashboard_config_dashboard_id_dashboard_id_fk", "tableFrom": "dashboard_config", "tableTo": "dashboard", "schemaTo": "capehorn_app", "columnsFrom": ["dashboard_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "capehorn_app.dashboard": {"name": "dashboard", "schema": "capehorn_app", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "dashboard_id_seq", "schema": "capehorn_app", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "owner": {"name": "owner", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "thread_id": {"name": "thread_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {"capehorn_app": "capehorn_app"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}