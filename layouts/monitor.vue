<script setup lang="ts">
import { SidebarProvider } from "@/components/ui/sidebar";
import AppSidebar from "@/components/AppSidebar.vue";

const defaultOpen = useCookie<boolean>("sidebar_state");
</script>
<template>
  <SidebarProvider :default-open="defaultOpen">
    <AppSidebar collapsible="icon" />
    <main class="w-full">
      <div
        class="flex items-center justify-between p-2 sticky top-0 h-16 z-10 border-b bg-white"
      >
        <div id="page-header-left" />
      </div>
      <div
        id="page-header-right"
        class="bg-white shadow-[0_0_10px_10px_rgba(255,255,255,0.7)] fixed top-4 right-2 z-40"
      />
      <div>
        <slot />
      </div>
    </main>
  </SidebarProvider>
</template>
