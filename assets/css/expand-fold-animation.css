/* 折叠展开动画 - 公共样式 */
.pack-expand-enter-active,
.pack-expand-leave-active {
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transition-delay: var(--enter-delay, 0ms);

  overflow: hidden;
  will-change: opacity, max-height, transform;
}

.pack-expand-enter-from {
  opacity: 0;
  max-height: 0;
  transform: translateY(-5px);
}

.pack-expand-enter-to {
  opacity: 1;
  max-height: 600px;
  transform: translateY(0);
}

.pack-expand-leave-from {
  opacity: 1;
  max-height: 600px;
  transform: translateY(0);
}

.pack-expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-5px);
}
