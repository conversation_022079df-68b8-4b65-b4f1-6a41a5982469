import { round } from "lodash-es";

export enum UnitNames {
  millisecond = "milliseconds",
  bps = "bps",
  count = "count",
  percentage = "percentage",
}

// BPS (bits per second)
export const BpsBase = 1000;
export const BpsB = 1;
export const BpsKb = BpsBase * BpsB;
export const BpsMb = BpsBase * BpsKb;
export const BpsGb = BpsBase * BpsMb;
export const BpsTb = BpsBase * BpsGb;
export const BpsGrowthUnits = ["bps", "Kbps", "Mbps", "Gbps", "Tbps"];

// Count
export const CountBase = 1000;
export const CountB = 1;
export const CountK = CountBase * CountB;
export const CountM = CountBase * CountK;
export const CountBillion = CountBase * CountM;
export const CountT = CountBase * CountBillion;
export const CountGrowthUnits = ["", "K", "M", "B", "T"];

function trunc(num: number): number {
  return Math.trunc(num);
}

function fract(num: number): number {
  return num % 1;
}

function getIntegerPartLength(num: number): number {
  const integerPart = trunc(num);
  return `${integerPart}`.length;
}

function isMatchPrecision(num: number, precision: number): boolean {
  return getIntegerPartLength(num) <= precision;
}

function roundNumByPrecision(num: number, precision: number): number {
  return round(num, precision - getIntegerPartLength(num));
}

function roundNumByPrecision2(
  num: number,
  precision: number,
  carryBit: (value: number) => boolean = () => false
) {
  const integerPartLength = getIntegerPartLength(num);
  // 整数部分达到指定精度
  if (integerPartLength === precision) {
    if (`${trunc(Math.round(num))}`.length > precision) {
      // 四舍五入后大于指定精度
      return {
        num: Math.round(num),
        goBack: true,
        goNext: false,
      };
    } else {
      // 小于等于指定精度，退出计算
      if (carryBit(num)) {
        return {
          num: Math.round(num),
          goBack: false,
          goNext: true,
        };
      } else {
        return {
          num: Math.round(num),
          goBack: false,
          goNext: false,
        };
      }
    }
  } else if (integerPartLength < precision) {
    // 整数部分小于指定精度，直接退出
    if (carryBit(num)) {
      // 可以继续进位
      return {
        num: round(num, precision - integerPartLength),
        goBack: false,
        goNext: true,
      };
    } else {
      // 不能继续进位，直接退出
      return {
        num: round(num, precision - integerPartLength),
        goBack: false,
        goNext: false,
      };
    }
  } else {
    // 整数部分超过指定精度，继续进位计算
    return {
      num,
      goBack: false,
      goNext: true,
    };
  }
}

// Format milliseconds

const TimeGrowthUnits = ["µs", "ms", "s", "min", "hour", "day", "week"];
const TimeBaseUnits = [1000, 1000, 60, 60, 24, 7];

export const formatMilliseconds = (value: number, precision: number = 3) => {
  let ret = value;
  let cursor = 1;

  if (value < 1 && value > 0) {
    ret = value * 1000;
    cursor = 0;

    if (isMatchPrecision(ret, precision)) {
      return {
        value: roundNumByPrecision(ret, precision),
        unit: TimeGrowthUnits[cursor] || "",
        fullUnit: UnitNames.millisecond,
      };
    }
  }

  const carryBit =
    cursor < TimeBaseUnits.length - 1 &&
    Math.trunc(ret / TimeBaseUnits[cursor]!) >= 1;
  if (isMatchPrecision(ret, precision) && !carryBit) {
    return {
      value: roundNumByPrecision(ret, precision),
      unit: TimeGrowthUnits[cursor] || "",
      fullUnit: UnitNames.millisecond,
    };
  }

  while (ret >= 1 && cursor < TimeBaseUnits.length - 1) {
    const baseUnit = TimeBaseUnits[cursor]!;
    ret = ret / baseUnit;
    const { goNext, goBack, num } = roundNumByPrecision2(
      ret,
      precision,
      (v) => {
        return (
          cursor + 1 < TimeBaseUnits.length &&
          Math.trunc(v / TimeBaseUnits[cursor + 1]!) >= 1
        );
      }
    );

    if (goBack && !goNext) {
      ret = num;
      cursor += 1;
    } else if (!goBack && !goNext) {
      ret = num;
      cursor += 1;
      break;
    } else if (!goBack && goNext) {
      cursor += 1;
    }
  }

  return {
    // TODO, remov toFixed
    value: ret.toFixed(1),
    unit: TimeGrowthUnits[cursor] || "",
    fullUnit: UnitNames.millisecond,
  };
};

// Format BPS (bits per second)
export const formatBps = (value: number, precision: number = 3) => {
  let ret = value;
  let cursor = 0;

  const carryBit = trunc(value / BpsBase) >= 1;
  if (isMatchPrecision(value, precision) && !carryBit) {
    return {
      value: roundNumByPrecision(value, precision),
      unit: BpsGrowthUnits[cursor] || "",
      fullUnit: UnitNames.bps,
    };
  }

  while (ret > 1 && cursor < BpsGrowthUnits.length - 1) {
    ret = ret / BpsBase;
    const { goNext, goBack, num } = roundNumByPrecision2(ret, precision);
    if (goBack && !goNext) {
      ret = num;
      cursor--;
    } else if (!goBack && !goNext) {
      ret = num;
      cursor++;
      break;
    } else if (!goBack && goNext) {
      cursor++;
    }
  }

  return {
    value: ret,
    unit: BpsGrowthUnits[cursor] || "",
    fullUnit: UnitNames.bps,
  };
};

// Format Count
export const formatCount = (value: number, precision: number = 3) => {
  let ret = value;
  let cursor = 0;

  const carryBit = trunc(value / CountBase) >= 1;
  if (isMatchPrecision(value, precision) && !carryBit) {
    return {
      value: roundNumByPrecision(value, precision),
      unit: CountGrowthUnits[cursor] || "",
      fullUnit: UnitNames.count,
    };
  }

  while (ret > 1 && cursor < CountGrowthUnits.length - 1) {
    ret = ret / CountBase;
    const { goNext, goBack, num } = roundNumByPrecision2(ret, 3, (v) => {
      return trunc(v / CountBase) >= 1;
    });
    if (goBack && !goNext) {
      ret = num;
      cursor--;
    } else if (!goBack && !goNext) {
      ret = num;
      cursor++;
      break;
    } else if (!goBack && goNext) {
      cursor++;
    }
  }

  return {
    value: ret,
    unit: CountGrowthUnits[cursor] || "",
    fullUnit: UnitNames.count,
  };
};

/*
 * value is raw, not multiplied by 100
 */
export const formatPercentage = (value: number, precision: number = 3) => {
  if (value === 0) {
    return {
      value: "0",
      unit: "%",
      fullUnit: UnitNames.percentage,
    };
  }
  if (value < 0.01) {
    return {
      value: "<0.01",
      unit: "%",
      fullUnit: UnitNames.percentage,
    };
  } else if (value < 1) {
    const rounded = roundNumByPrecision(value, precision);
    return {
      value:
        getIntegerPartLength(rounded) < precision && fract(rounded) === 0
          ? `${rounded}.0`
          : `${rounded}`,
      unit: "%",
      fullUnit: UnitNames.percentage,
    };
  } else if (value <= 999) {
    const rounded = Number.isInteger(value) ? value : Number(value.toFixed(3));
    if (rounded === 999) {
      return {
        value: "999",
        unit: "%",
        fullUnit: UnitNames.percentage,
      };
    } else {
      return {
        value: `${rounded}`,
        unit: "%",
        fullUnit: UnitNames.percentage,
      };
    }
  } else {
    return {
      value: `${value}`,
      unit: "%",
      fullUnit: UnitNames.percentage,
    };
  }
};

const NoopValueFormatter = (value: number) => ({
  value,
  unit: "",
  fullUnit: "",
});

export function getValueFormatter(unit?: UnitNames): (value: number) => {
  value: number | string;
  unit: string;
  fullUnit: UnitNames | string;
} {
  switch (unit) {
    case UnitNames.millisecond:
      return (value) => formatMilliseconds(value);
    case UnitNames.bps:
      return (value) => formatBps(value);
    case UnitNames.count:
      return (value) => formatCount(value);
    case UnitNames.percentage:
      return (value) => formatPercentage(value);
    default:
      return NoopValueFormatter;
  }
}

export function getUnitList(unit: UnitNames) {
  switch (unit) {
    case UnitNames.bps:
      return BpsGrowthUnits.map((u) => [u, UnitNames.bps]);
    case UnitNames.count:
      return CountGrowthUnits.map((u) => [u, UnitNames.count]);
    case UnitNames.millisecond:
      return [["ms", UnitNames.millisecond]];
    case UnitNames.percentage:
      return [["%", UnitNames.percentage]];
    default:
      return [];
  }
}

export function downCastValue(value: number, _unit: UnitNames) {
  // For these simple units, no downcast needed
  return value;
}

export function upCastValue(value: number, _unit: UnitNames) {
  // For these simple units, no upcast needed
  return value;
}

export function getBaseUnit(unit: UnitNames) {
  switch (unit) {
    case UnitNames.bps:
      return UnitNames.bps;
    case UnitNames.count:
      return UnitNames.count;
    case UnitNames.millisecond:
      return UnitNames.millisecond;
    case UnitNames.percentage:
      return UnitNames.percentage;
    default:
      return "";
  }
}
