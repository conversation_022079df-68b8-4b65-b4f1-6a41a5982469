// 格式化大数值的通用函数
const formatLargeNumber = (data: number): string => {
  const value = Number(data);
  if (value >= 1000000000) {
    const num = value / 1000000000;
    return Number.isInteger(num) ? `${num}B` : `${num.toFixed(2)}B`;
  } else if (value >= 1000000) {
    const num = value / 1000000;
    return Number.isInteger(num) ? `${num}M` : `${num.toFixed(2)}M`;
  } else if (value >= 1000) {
    const num = value / 1000;
    return Number.isInteger(num) ? `${num}K` : `${num.toFixed(2)}K`;
  } else {
    return Number.isInteger(value)
      ? String(value)
      : value.toFixed(2).toString();
  }
};

// 数据格式化函数
export const formatUnitValue = (value: number, unit: string = ""): string => {
  // 如果没有单位，对大数值进行格式化
  if (!unit) {
    return formatLargeNumber(value);
  }

  switch (unit) {
    case "bps":
      // bps: 除以1000加单位
      if (value >= 1000000000) {
        return `${(value / 1000000000).toFixed(2)}Gbps`;
      } else if (value >= 1000000) {
        return `${(value / 1000000).toFixed(2)}Mbps`;
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(2)}Kbps`;
      } else {
        return `${value.toFixed(2)}bps`;
      }

    case "bytes":
      // bytes: 除以1024加单位
      if (value >= 1024 * 1024 * 1024) {
        return `${(value / (1024 * 1024 * 1024)).toFixed(2)}GB`;
      } else if (value >= 1024 * 1024) {
        return `${(value / (1024 * 1024)).toFixed(2)}MB`;
      } else if (value >= 1024) {
        return `${(value / 1024).toFixed(2)}KB`;
      } else {
        return `${value.toFixed(2)}B`;
      }

    case "ms":
      // ms: 大于1000时变成秒，小于1000时变成毫秒
      if (value >= 1000) {
        return `${(value / 1000).toFixed(2)}s`;
      } else {
        return `${value.toFixed(2)}ms`;
      }

    case "percentage":
      // percentage: 除以100加%
      return `${(value / 100).toFixed(2)}%`;
    case "count":
      // count: 对大数值进行格式化
      return formatLargeNumber(value);

    default:
      // 默认情况下，对大数值进行格式化
      return formatLargeNumber(value);
  }
};

export const formatElapsedTime = (elapsedTime: number) => {
  if (elapsedTime >= 1000) {
    return `${parseInt(elapsedTime / 1000 + "", 10)} s`;
  } else {
    return `${elapsedTime} ms`;
  }
};
