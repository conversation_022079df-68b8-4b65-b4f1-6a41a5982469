export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export const sleepInRange = async (minMs: number, maxMs: number) => {
  const duration = Math.floor(Math.random() * (maxMs - minMs + 1)) + minMs;
  await sleep(duration);
};

export const slugify = (text: string) => {
  return text
    .replace(/[^\w\s]/gi, "")
    .replace(/\s+/g, "-")
    .toLowerCase();
};
