import { slugify } from "./index";

export const scrollToSlugifiedElement = (
  text: string,
  behavior: "smooth" | "instant" = "smooth"
) => {
  const targetId = slugify(text);
  const element =
    document.getElementById(targetId) ||
    document.querySelector(`[id*="${targetId}"]`);

  if (element) {
    const headerHeight = 64;
    const elementPosition =
      element.getBoundingClientRect().top + window.pageYOffset;
    const targetPosition = elementPosition - headerHeight - 20;
    window.scrollTo({
      top: targetPosition,
      behavior: behavior,
    });
  }
};
