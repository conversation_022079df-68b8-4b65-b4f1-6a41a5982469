# UI Components Development Guide

## Overview

This directory contains documentation for UI components in the Capehorn project. Each component modification should be properly documented to maintain code quality and team collaboration.

## Component Documentation Standards

### When to Document
Document UI component changes when:
- Modifying component behavior or appearance
- Adding new props or events
- Changing component structure
- Updating styling or positioning
- Fixing bugs that affect component functionality

### Documentation Template

Each component change should include:

```markdown
# Component Name - Change Description

## Overview
Brief description of the change

## Change Summary
- Date: YYYY-MM-DD
- Component: path/to/component.vue
- Type: [Enhancement|Bug Fix|Breaking Change]
- Impact: [Visual|Functional|Performance]

## Problem Statement
Why the change was needed

## Solution
How the problem was solved

## Technical Implementation
### Before
```code block```

### After
```code block```

## Design System Compliance
How the change follows project standards

## Testing Considerations
What to test when verifying the change

## References
Related files and documentation
```

## Project UI Standards

### Design System
- **Framework**: shadcn-vue with Tailwind CSS
- **Icons**: lucide-vue-next
- **Components**: Reka UI primitives
- **Styling**: CSS variables with theme support

### CSS Guidelines
- Use Tailwind utility classes
- Follow responsive design principles
- Maintain consistent spacing and typography
- Support dark mode when applicable

### Component Structure
- Use Vue 3 Composition API
- Follow TypeScript best practices
- Implement proper prop validation
- Include accessibility features

## Existing Components

### ScrollToBottom Button
- **File**: `modules/sentire/components/ScrollToBottom.vue`
- **Purpose**: Provides smooth scrolling to page bottom
- **Features**: Auto-hide when near bottom, loading animations
- **Documentation**: [Center Alignment Change](./scroll-to-bottom-button-center-alignment.md)

## Testing Guidelines

### Visual Testing
- Test on different screen sizes
- Verify responsive behavior
- Check dark/light mode compatibility
- Validate accessibility features

### Functional Testing
- Verify all props work correctly
- Test event emissions
- Check component lifecycle
- Validate error handling

### Cross-browser Testing
- Chrome/Edge (Chromium)
- Firefox
- Safari
- Mobile browsers

## Best Practices

### Code Quality
- Use TypeScript for type safety
- Follow Vue 3 best practices
- Implement proper error handling
- Write clear, self-documenting code

### Performance
- Minimize re-renders
- Use computed properties appropriately
- Implement lazy loading when needed
- Optimize bundle size

### Accessibility
- Include ARIA labels
- Support keyboard navigation
- Maintain proper contrast ratios
- Test with screen readers

### Documentation
- Document all public APIs
- Include usage examples
- Explain design decisions
- Maintain change history

## Common Patterns

### Button Components
- Use `Button` from `@/components/ui/button`
- Follow variant and size conventions
- Include proper loading states
- Support disabled states

### Layout Components
- Use CSS Grid or Flexbox
- Implement responsive breakpoints
- Support container queries
- Maintain consistent spacing

### Form Components
- Implement proper validation
- Support error states
- Include accessibility labels
- Follow form design patterns

## Tools and Resources

### Development Tools
- Vue DevTools
- Tailwind CSS IntelliSense
- TypeScript Language Server
- ESLint with Vue rules

### Design Resources
- Tailwind CSS Documentation
- shadcn-vue Component Library
- Lucide Icons
- Reka UI Documentation

### Testing Tools
- Vitest for unit testing
- Playwright for e2e testing
- Storybook for component testing
- Accessibility testing tools
