# ScrollToBottom Button Center Alignment

## Overview

This document records the modification made to the ScrollToBottom button component to change its horizontal positioning from right-aligned to center-aligned within the conversation window.

## Change Summary

**Date**: 2025-01-20  
**Component**: `modules/sentire/components/ScrollToBottom.vue`  
**Type**: UI Enhancement  
**Impact**: Visual positioning change  

## Problem Statement

The ScrollToBottom button was positioned at the right edge of the conversation window (`right-0`), which may not provide the optimal user experience for center-focused content layouts.

## Solution

Changed the button's horizontal positioning to be centered within the conversation window using CSS transform techniques.

## Technical Implementation

### Before
```css
class="rounded-full absolute -top-12 cursor-pointer right-0 z-10"
```

### After
```css
class="rounded-full absolute -top-12 cursor-pointer left-1/2 transform -translate-x-1/2 z-10"
```

### CSS Classes Used
- `left-1/2`: Positions the button's left edge at 50% of the container width
- `transform -translate-x-1/2`: Shifts the button left by 50% of its own width to achieve true centering

## Code Changes

**File**: `modules/sentire/components/ScrollToBottom.vue`

```diff
  <Button
    variant="outline"
    size="icon"
-   class="rounded-full absolute -top-12 cursor-pointer right-0 z-10"
+   class="rounded-full absolute -top-12 cursor-pointer left-1/2 transform -translate-x-1/2 z-10"
    :class="{
      'opacity-0 top-0 pointer-events-none': !isVisible,
      'border-[#e2ecf2] border-t-theme-color animate-spin':
        isRunning && isVisible,
      'shadow-md': !isRunning,
    }"
    @click="scrollToBottom"
  >
```

## Design System Compliance

This change maintains compliance with the project's design system:

1. **Tailwind CSS**: Uses standard Tailwind utility classes
2. **Component Structure**: No changes to component logic or props
3. **Responsive Design**: Center alignment works across all screen sizes
4. **Accessibility**: Maintains all existing accessibility features
5. **Animation Support**: Compatible with existing spin animations

## Container Context

The button is positioned within the `#bottom-container` element which has:
- Fixed width of `768px` (default) or dynamic width based on `analysisPanelWidth`
- Fixed positioning at the bottom of the viewport
- Contains the conversation input area

## Testing Considerations

When testing this change, verify:

1. **Visual Positioning**: Button appears centered horizontally
2. **Responsive Behavior**: Centering works on different screen sizes
3. **Functionality**: Click behavior remains unchanged
4. **Animations**: Spin animations during loading states work correctly
5. **Visibility Logic**: Show/hide behavior based on scroll position works
6. **Z-index**: Button appears above other elements as expected

## Browser Compatibility

The CSS transform approach used is supported by all modern browsers:
- Chrome/Edge: Full support
- Firefox: Full support  
- Safari: Full support
- Mobile browsers: Full support

## Related Components

This change affects the visual layout in:
- `pages/sentire/index.vue` (main conversation page)
- Container element `#bottom-container`
- Interaction with `UserQuestionInput` component

## Future Considerations

- Monitor user feedback on the new centered positioning
- Consider making button position configurable if needed
- Evaluate consistency with other floating action buttons in the app

## References

- Component file: `modules/sentire/components/ScrollToBottom.vue`
- Usage context: `pages/sentire/index.vue` lines 332-336
- Design system: `components/ui/button/index.ts`
- CSS utilities: Tailwind CSS documentation
