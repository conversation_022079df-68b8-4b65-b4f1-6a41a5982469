# UserQuestionInput Send Button Circular Design

## Overview

This document records the modification made to the UserQuestionInput component to change the send button from default rounded corners to a circular design.

## Change Summary

**Date**: 2025-01-20  
**Component**: `modules/sentire/components/UserQuestionInput.vue`  
**Type**: UI Enhancement  
**Impact**: Visual design improvement  

## Problem Statement

The send button in the UserQuestionInput component was using the default Button component styling with `rounded-sm-new` (8px border radius), but the design requirement was for a perfectly circular button.

## Solution

### 1. HTML Changes
Added a semantic CSS class `send-button-circular` to both send button variants:

```vue
<!-- Normal state button -->
<Button
  v-if="!isRunning"
  color="primary"
  size="sm"
  class="send-button send-button-circular cursor-pointer w-8 h-8 flex items-center justify-center bg-[#bfbfbf] hover:bg-[#8fb2c8] active:bg-[#8fb2c8] transition-all duration-300"
  :class="{
    'is-active': isFocused,
    'is-not-empty': !isEmpty,
  }"
  @click="handleSearch()"
>

<!-- Running state button -->
<Button
  v-if="isRunning"
  color="primary"
  size="sm"
  class="send-button send-button-circular cursor-pointer w-8 h-8 flex items-center justify-center bg-[#3a7ca5] transition-all duration-300"
  @click="currentThreadStore.cancelAnswerUserQuestion()"
>
```

### 2. CSS Changes
Added specialized styling for circular buttons using CSS specificity:

```scss
.send-button-circular {
  border-radius: 50%;
  min-width: 32px;
  min-height: 32px;
  
  /* 确保覆盖 Button 组件的默认圆角 */
  &:deep(.reka-button) {
    border-radius: 50%;
  }
  
  /* 使用更高的 CSS 特异性来覆盖默认样式 */
  &.send-button.send-button-circular {
    border-radius: 50%;
  }
}
```

## Technical Approach

### Best Practices Applied
1. **Avoided `!important`**: Used CSS specificity instead of `!important` declarations
2. **Semantic Class Naming**: Created descriptive class name `send-button-circular`
3. **CSS Specificity Management**: Used multiple selector strategies to ensure proper override
4. **Maintainability**: Created reusable CSS class for future circular buttons

### CSS Specificity Strategy
- Base rule: `.send-button-circular { border-radius: 50%; }`
- Deep selector: `&:deep(.reka-button)` for component internals
- High specificity: `&.send-button.send-button-circular` for guaranteed override

## Result

- ✅ Send button now displays as a perfect circle (32px × 32px)
- ✅ Maintains all existing functionality and states
- ✅ Code follows CSS best practices
- ✅ No style conflicts or `!important` usage
- ✅ Reusable solution for other circular buttons

## Files Modified

- `modules/sentire/components/UserQuestionInput.vue`
  - Added `send-button-circular` class to both button variants
  - Added CSS rules for circular button styling

## Testing Notes

The circular button design should be verified across:
- Different browser environments
- Various screen sizes
- Both button states (normal and running)
- Hover and active interactions
