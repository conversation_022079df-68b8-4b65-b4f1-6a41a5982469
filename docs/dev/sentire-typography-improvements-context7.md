# Sentire 页面字体与排版规范改进报告

## 改进概述

根据 Context7 提供的字体层级规范，对 Sentire 页面的各级标题、段落进行了系统性的字体大小、行高和上下间距调整，建立了统一的竖向节奏。

## 应用的字体层级规范

### 字体层级标准

| 层级             | Tailwind 类  | px 值 | font-weight     | 行高              | 下间距  | 应用场景           |
| -------------- | ----------- | ---- | --------------- | --------------- | ---- | -------------- |
| H1 / 页面主标题     | `text-3xl`  | 24px | `font-semibold` | `leading-tight` | `mb-8` | SENTIRE 主标题    |
| H2 / 模块标题      | `text-xl`   | 20px | `font-semibold` | `leading-snug`  | `mb-4` | 深度分析标题         |
| H3 / 小标题 / 标签  | `text-sm`   | 14px | `font-medium`   | `leading-snug`  | `mb-2` | 步骤标题、任务标题      |
| 正文 / Body      | `text-base` | 16px | `font-normal`   | `leading-relaxed` | `mb-4` | 分析内容、用户问题      |
| 图表标注 / Caption | `text-xs`   | 12px | `font-normal`   | `leading-snug`  | -    | 建议文字、图表标注      |

### 竖向间距规范

- **页面主标题 → 下一块内容**: `mb-8` (32px)
- **模块标题 → 正文首段**: `mb-4` (16px)
- **小标题/标签 → 其内容**: `mb-2` (8px)
- **正文段落之间**: `mb-4` (16px)
- **图表组件上下**: `my-6` (24px)
- **大区块（模块）之间**: `mt-8` (32px)

## 具体改进内容

### 1. UserQuestionInput.vue

**改进内容**:
- 主标题保持 `text-3xl font-semibold leading-tight mb-8`
- 操作按钮标签: 添加 `font-medium leading-snug`

**改进后样式**:
```vue
<h1 class="title text-3xl font-semibold leading-tight text-center mb-8 quickfocus-title">
  SENTIRE
</h1>

<div class="text-sm font-medium leading-snug">{{ item.label }}</div>
```

### 2. UserQuestion.vue

**改进内容**:
- 容器间距: `mb-4` → `mb-8`
- 移除普通模式的重复 `mb-4`

**改进后样式**:
```vue
<!-- 容器 -->
<div class="flex items-center gap-4 mb-8 sticky top-0 z-20 h-16 leading-16 bg-white">

<!-- 普通模式 -->
<div class="pl-4 text-xl md:text-2xl lg:text-3xl leading-snug text-default-text font-semibold relative rounded-border-left">
```

### 3. UnderstandingPhase.vue

**改进内容**:
- 标题区域: 添加 `mb-2`
- 容器间距: `my-4` → `my-6`
- 步骤间距: `gap-3` → `gap-4`

**改进后样式**:
```vue
<!-- 标题 -->
<div class="text-sm font-medium leading-snug mb-2 text-default-text">
  {{ summary }}
</div>

<!-- 容器 -->
<div class="flex w-full flex-col justify-start gap-6 my-6">

<!-- 步骤 -->
<div class="flex flex-col gap-4 w-full">
```

### 4. DeepAnalysisPhase.vue

**改进内容**:
- 正文样式: `prose-p:text-deep-analysis-text` → `prose-p:text-base`
- 添加 `prose-p:mb-4` 确保段落间距
- 图片间距: `prose-img:my-3` → `prose-img:my-6`

**改进后样式**:
```vue
<Markdown
  class="prose prose-stone min-w-full prose-img:my-6 prose-img:mx-auto prose-sm prose-p:my-4 prose-p:text-base prose-p:leading-relaxed prose-p:mb-4 prose-headings:!font-semibold prose-headings:text-xl prose-headings:leading-snug prose-headings:mb-4 prose-ul:mb-4"
/>
```

### 5. Suggestions.vue

**改进内容**:
- 容器上间距: `mt-6` → `mt-8`

**改进后样式**:
```vue
<div class="text-xs leading-snug mt-8 text-deep-analysis-text">
```

### 6. ThinkingIndicator.vue

**改进内容**:
- 容器间距: `my-4` → `my-6`
- 文字大小: `text-lg` → `text-base`
- 添加行高: `leading-relaxed`

**改进后样式**:
```vue
<div class="flex items-center gap-2 my-6">
  <span class="font-medium text-base leading-relaxed">{{ currentStage }}</span>
</div>
```

### 7. pages/sentire/index.vue

**改进内容**:
- Pinwheel 间距: `my-2` → `my-4`

**改进后样式**:
```vue
<l-pinwheel
  class="my-4"
/>
```

### 8. PlanTasks.vue

**改进内容**:
- 标题添加下间距: `mb-2`
- 任务项间距: `py-1` → `py-2`
- 任务内容添加: `prose-p:mb-2`

**改进后样式**:
```vue
<!-- 标题 -->
<div class="text-sm font-medium leading-snug mb-2 text-gray-800">Process Tasks</div>

<!-- 任务项 -->
<div class="flex items-center gap-2 py-2">

<!-- 任务内容 -->
<Markdown class="prose-sm prose-p:leading-snug prose-p:mb-2" />
```

### 9. InteractiveToolCall.vue

**改进内容**:
- 标题区间距: `mb-3` → `mb-4`
- 问题文本: 添加 `text-base leading-relaxed`

**改进后样式**:
```vue
<!-- 标题区 -->
<div class="flex items-start mb-4">

<!-- 问题文本 -->
<p class="text-base leading-relaxed">
  {{ props.question }}
</p>
```

## 改进效果

### 视觉层次优化
- **H1 主标题**: 使用 `text-3xl` 确保页面主标题的权威性
- **H2 模块标题**: 使用 `text-xl` 建立清晰的模块分割
- **H3 小标题**: 使用 `text-sm` 提供细节层级
- **正文内容**: 统一使用 `text-base` 确保可读性

### 竖向节奏统一
- **大间距**: `mb-8` (32px) 用于主要区块分割
- **中间距**: `mb-4` (16px) 用于段落和模块内容
- **小间距**: `mb-2` (8px) 用于标题与内容的紧密关联

### 行高优化
- **标题**: 使用 `leading-tight` 和 `leading-snug` 保持紧凑
- **正文**: 使用 `leading-relaxed` 提升可读性

## 遵循的最佳实践

### Context7 规范
✅ 使用标准 Tailwind 字体类
✅ 建立清晰的字体层级
✅ 统一的行高和间距系统
✅ 语义化的字体权重使用

### 设计系统一致性
✅ 避免自定义字体大小
✅ 使用系统化的间距规范
✅ 保持响应式设计兼容性
✅ 维护组件间的视觉一致性

## 技术改进

### 代码质量
- 移除了内联样式和自定义字体大小
- 使用 Tailwind 标准类提升维护性
- 建立了可复用的设计模式

### 性能优化
- 减少了 CSS 覆盖和特异性冲突
- 利用 Tailwind 的优化和缓存机制
- 提升了样式的可预测性

## 总结

通过这次改进，Sentire 页面现在具备：

1. **统一的字体层级系统** - 清晰的视觉层次
2. **一致的竖向节奏** - 和谐的空间关系
3. **优化的可读性** - 合适的行高和字体大小
4. **标准化的实现** - 遵循 Context7 最佳实践

这些改进不仅提升了用户体验，也为后续的设计和开发工作建立了坚实的基础。
