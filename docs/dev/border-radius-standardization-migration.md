# 圆角规范标准化迁移文档

## 概述

本文档记录了将项目自定义圆角规范迁移到Tailwind CSS标准类的完整过程，采用混合方案平衡标准化和设计需求。

**迁移日期**: 2025-01-23  
**迁移类型**: 圆角规范标准化  
**影响范围**: 全项目UI组件  
**迁移策略**: 混合方案（标准类 + 必要自定义）

## 迁移目标

### 主要目标
1. **标准化**: 使用Tailwind CSS标准圆角类，减少自定义配置
2. **一致性**: 保持视觉效果的一致性，避免破坏性变更
3. **维护性**: 简化配置，提高代码可维护性
4. **兼容性**: 确保向后兼容，平滑过渡

### 设计原则
- 优先使用Tailwind标准类
- 保持像素值一致性（在可能的情况下）
- 接受合理的视觉调整（±4px以内）
- 只保留无法替代的自定义值

## 迁移映射表

### 完全匹配的映射（无视觉变化）

| 原自定义类 | 像素值 | 新标准类 | 像素值 | 状态 |
|------------|--------|----------|--------|------|
| `rounded-sm-new` | 8px | `rounded-lg` | 8px | ✅ 完全匹配 |
| `rounded-md-new` | 12px | `rounded-xl` | 12px | ✅ 完全匹配 |
| `rounded-lg-new` | 16px | `rounded-2xl` | 16px | ✅ 完全匹配 |
| `rounded-xl-plus` | 32px | `rounded-4xl` | 32px | ✅ 完全匹配 |

### 接受视觉调整的映射

| 原自定义类 | 像素值 | 新标准类 | 像素值 | 变化 | 状态 |
|------------|--------|----------|--------|------|------|
| `rounded-xl-new` | 28px | `rounded-3xl` | 24px | -4px | ✅ 可接受 |
| `rounded-lg-plus` | 20px | `rounded-3xl` | 24px | +4px | ✅ 可接受 |

### 特殊处理的映射

| 原自定义类 | 像素值 | 新方案 | 像素值 | 说明 |
|------------|--------|--------|--------|------|
| `rounded-2xl-new` | 48px | `rounded-5xl` | 48px | 保留为自定义类 |

## 组件迁移详情

### 基础UI组件

#### Input组件 (`components/ui/input/Input.vue`)
- **变更**: `rounded-sm-new` → `rounded-lg`
- **像素值**: 8px → 8px (无变化)
- **影响**: 无视觉变化

#### Button组件 (`components/ui/button/index.ts`)
- **变更**: `rounded-sm-new` → `rounded-lg`
- **像素值**: 8px → 8px (无变化)
- **影响**: 无视觉变化

#### Card组件 (`components/ui/card/Card.vue`)
- **变更**: `rounded-md-new` → `rounded-xl`
- **像素值**: 12px → 12px (无变化)
- **影响**: 无视觉变化

#### Alert组件 (`components/ui/alert/index.ts`)
- **变更**: `rounded-sm-new` → `rounded-lg`
- **像素值**: 8px → 8px (无变化)
- **影响**: 无视觉变化

#### Skeleton组件 (`components/ui/skeleton/Skeleton.vue`)
- **变更**: `rounded-sm-new` → `rounded-lg`
- **像素值**: 8px → 8px (无变化)
- **影响**: 无视觉变化

### 主要交互组件

#### UserQuestionInput组件 (`modules/sentire/components/UserQuestionInput.vue`)
- **外层容器**: `rounded-lg-new` → `rounded-2xl`
- **像素值**: 16px → 16px (无变化)
- **影响**: 无视觉变化

#### PlanTasks组件 (`modules/sentire/components/PlanTasks.vue`)
- **容器**: `var(--radius-md-new)` → `0.75rem` (直接使用rem值)
- **像素值**: 12px → 12px (无变化)
- **影响**: 无视觉变化

#### Toast通知 (`app.vue`)
- **变更**: `rounded-md-new` → `rounded-xl`
- **像素值**: 12px → 12px (无变化)
- **影响**: 无视觉变化

### 硬编码圆角值处理

#### 滚动条样式 (`assets/css/main.css`)
- **变更**: `border-radius: 6px` → `border-radius: 0.375rem`
- **像素值**: 6px → 6px (无变化)
- **说明**: 使用rem值替代硬编码像素值

#### Tooltip样式 (`assets/css/main.css`)
- **变更**: `border-radius: 6px !important` → `border-radius: 0.375rem !important`
- **像素值**: 6px → 6px (无变化)
- **说明**: 使用rem值替代硬编码像素值

#### 圆形按钮 (`modules/sentire/components/UserQuestionInput.vue`)
- **变更**: `border-radius: 50%` → `border-radius: var(--radius-full)`
- **说明**: 使用CSS变量替代硬编码值

#### 补全项目 (`modules/sentire/components/UserQuestionInput.vue`)
- **变更**: `border-radius: 4px` → `border-radius: 0.25rem`
- **像素值**: 4px → 4px (无变化)
- **说明**: 使用rem值替代硬编码像素值

#### 提及标签 (`modules/sentire/components/UserQuestionInput.vue`)
- **变更**: `border-radius: 4px` → `border-radius: 0.25rem`
- **像素值**: 4px → 4px (无变化)
- **说明**: 使用rem值替代硬编码像素值

## 配置文件变更

### Tailwind配置 (`tailwind.config.js`)

**变更前**:
```javascript
borderRadius: {
  'none': 'var(--radius-none)',
  'xs': 'var(--radius-xs)',
  'sm-new': 'var(--radius-sm-new)',
  'md-new': 'var(--radius-md-new)',
  'lg-new': 'var(--radius-lg-new)',
  'lg-plus': 'var(--radius-lg-plus)',
  'xl-new': 'var(--radius-xl-new)',
  'xl-plus': 'var(--radius-xl-plus)',
  '2xl-new': 'var(--radius-2xl)',
  'full': 'var(--radius-full)',
}
```

**变更后**:
```javascript
borderRadius: {
  // 只保留无法用标准类替代的值
  '5xl': '3rem',  // 48px，用于特大型容器（原 rounded-2xl-new）
}
```

### CSS变量 (`assets/css/main.css`)

**移除的变量**:
```css
/* 新的统一圆角规范 */
--radius-none: 0px;
--radius-xs: 0.25rem;        /* 4px */
--radius-sm-new: 0.5rem;     /* 8px */
--radius-md-new: 0.75rem;    /* 12px */
--radius-lg-new: 1rem;       /* 16px */
--radius-lg-plus: 1.25rem;   /* 20px */
--radius-xl-new: 1.75rem;    /* 28px */
--radius-xl-plus: 2rem;      /* 32px */
--radius-2xl: 3rem;          /* 48px */
```

**保留的变量**:
```css
/* 保留必要的圆角变量以确保向后兼容 */
--radius-full: 9999px;
```

## 最佳实践指南

### 使用标准Tailwind类
```html
<!-- 推荐：使用标准类 -->
<div class="rounded-lg">小型元素 (8px)</div>
<div class="rounded-xl">中型元素 (12px)</div>
<div class="rounded-2xl">大型元素 (16px)</div>
<div class="rounded-3xl">特大元素 (24px)</div>
<div class="rounded-4xl">超大元素 (32px)</div>
<div class="rounded-full">圆形元素</div>

<!-- 特殊情况：使用自定义类 -->
<div class="rounded-5xl">特大型容器 (48px)</div>
```

### CSS中使用rem值
```css
/* 推荐：使用rem值 */
.custom-element {
  border-radius: 0.5rem;  /* 8px */
  border-radius: 0.75rem; /* 12px */
  border-radius: 1rem;    /* 16px */
}

/* 特殊情况：使用CSS变量 */
.circular-element {
  border-radius: var(--radius-full);
}
```

## 迁移效果验证

### 视觉一致性检查
- ✅ 基础UI组件圆角保持一致
- ✅ 主要交互组件圆角保持一致
- ✅ 硬编码值已标准化
- ✅ 无破坏性视觉变更

### 代码质量提升
- ✅ 减少90%的自定义圆角配置
- ✅ 统一使用Tailwind标准类
- ✅ 消除硬编码像素值
- ✅ 提高代码可维护性

### 性能影响
- ✅ 减少CSS文件大小
- ✅ 减少自定义CSS变量
- ✅ 提高构建性能
- ✅ 无运行时性能影响

## 后续维护建议

### 新组件开发
1. 优先使用Tailwind标准圆角类
2. 避免硬编码像素值
3. 特殊需求时使用rem值
4. 圆形元素使用`rounded-full`

### 代码审查要点
1. 检查是否使用了已废弃的自定义类
2. 确保圆角值符合设计规范
3. 避免引入新的硬编码值
4. 保持与现有组件的一致性

### 升级路径
1. 新功能直接使用标准类
2. 重构时逐步替换旧的自定义类
3. 定期审查和清理不必要的自定义配置
4. 保持与Tailwind版本的同步更新

---

**文档版本**: v1.0  
**创建日期**: 2025-01-23  
**负责人**: 开发团队  
**审核状态**: 已完成
