# 圆角尺寸规范统一化迁移计划

## 项目概述

当前项目存在混合的圆角控制模式：主要交互组件使用独立的大圆角设计，基础UI组件通过CSS变量统一管理。本文档制定了统一圆角规范的迁移计划。

## 目标圆角规范

基于Material Design和现代UI设计原则，建立以下圆角规范：

| 规范名称 | 像素值 | CSS变量名 | Tailwind类名 | 使用场景 |
|---------|--------|-----------|-------------|----------|
| None | 0px | `--radius-none` | `rounded-none` | 无圆角元素 |
| Extra small | 4px | `--radius-xs` | `rounded-xs` | 小型图标、徽章 |
| Small | 8px | `--radius-sm` | `rounded-sm` | 按钮、输入框 |
| Medium | 12px | `--radius-md` | `rounded-md` | 卡片、对话框 |
| Large | 16px | `--radius-lg` | `rounded-lg` | 面板、容器 |
| Large increased | 20px | `--radius-lg-plus` | `rounded-lg-plus` | 特殊容器 |
| Extra large | 28px | `--radius-xl` | `rounded-xl` | 主要交互区域 |
| Extra large increased | 32px | `--radius-xl-plus` | `rounded-xl-plus` | 大型容器 |
| Extra extra large | 48px | `--radius-2xl` | `rounded-2xl` | 特大型容器 |
| Full | 9999px | `--radius-full` | `rounded-full` | 圆形元素 |

## 当前圆角使用情况分析

### 1. 主要交互组件

#### UserQuestionInput.vue (Prompt Bar)
- **外层容器**: `rounded-3xl` (24px) → 建议迁移到 `--radius-xl` (28px)
- **内层容器**: `rounded-3xl` (24px) → 建议迁移到 `--radius-xl` (28px)  
- **编辑器**: `rounded-2xl` (16px) → 保持 `--radius-lg` (16px)
- **按钮**: `rounded-full` → 保持 `--radius-full`

#### PlanTasks.vue (Plan Bar)
- **容器**: `rounded-3xl` (24px) → 建议迁移到 `--radius-xl` (28px)

### 2. 基础UI组件

#### components/ui/ 组件分析
- **Card**: `rounded-xl` (12px) → 保持 `--radius-md` (12px)
- **Input**: `rounded-md` (6px) → 迁移到 `--radius-sm` (8px)
- **Button**: `rounded-md` (6px) → 迁移到 `--radius-sm` (8px)
- **Alert**: `rounded-lg` (8px) → 保持 `--radius-sm` (8px)
- **Tooltip**: `rounded-md` (6px) → 迁移到 `--radius-sm` (8px)
- **Avatar**: `rounded-full` → 保持 `--radius-full`
- **Skeleton**: `rounded-md` (6px) → 迁移到 `--radius-sm` (8px)

### 3. 模块组件

#### Monitor模块
- **MessageInput**: `rounded-3xl` (24px) → 迁移到 `--radius-xl` (28px)
- **DashboardCard**: `rounded-xl` (12px) → 保持 `--radius-md` (12px)
- **PlaceholderPanel**: `rounded-3xl` (24px) → 迁移到 `--radius-xl` (28px)

#### Sentire模块
- **LookupCluesTool**: `rounded-t-xl`, `rounded-b-xl` (12px) → 保持 `--radius-md` (12px)
- **StandardToolCall**: `rounded-2xl`, `rounded-lg` → 统一到 `--radius-lg` (16px)

### 4. 特殊圆角使用
- **滚动条**: `border-radius: 6px` → 迁移到 `--radius-sm` (8px)
- **Tooltip箭头**: `rounded-[2px]` → 迁移到 `--radius-xs` (4px)

## 圆角值映射关系

| 当前值 | 当前Tailwind类 | 新规范值 | 新CSS变量 | 变化说明 |
|--------|---------------|----------|-----------|----------|
| 0px | `rounded-none` | 0px | `--radius-none` | 无变化 |
| 2px | `rounded-[2px]` | 4px | `--radius-xs` | 增加2px |
| 4px | `rounded-sm` | 4px | `--radius-xs` | 无变化 |
| 6px | `rounded-md` | 8px | `--radius-sm` | 增加2px |
| 8px | `rounded-lg` | 8px | `--radius-sm` | 无变化 |
| 12px | `rounded-xl` | 12px | `--radius-md` | 无变化 |
| 16px | `rounded-2xl` | 16px | `--radius-lg` | 无变化 |
| 20px | 无对应类 | 20px | `--radius-lg-plus` | 新增 |
| 24px | `rounded-3xl` | 28px | `--radius-xl` | 增加4px |
| 32px | 无对应类 | 32px | `--radius-xl-plus` | 新增 |
| 48px | 无对应类 | 48px | `--radius-2xl` | 新增 |
| 9999px | `rounded-full` | 9999px | `--radius-full` | 无变化 |

## 实施计划

### 阶段1: 建立新的CSS变量系统 (1天)

1. **扩展 `assets/css/main.css`**
   - 添加新的圆角变量定义
   - 保持向后兼容性

2. **更新 `tailwind.config.js`**
   - 扩展圆角配置
   - 添加自定义圆角类名

### 阶段2: 基础UI组件迁移 (2-3天)

1. **优先级1组件** (影响最大)
   - Input.vue
   - Button.vue  
   - Card.vue
   - Alert.vue

2. **优先级2组件** (中等影响)
   - Tooltip.vue
   - Skeleton.vue
   - 其他 components/ui/ 组件

### 阶段3: 主要交互组件迁移 (2天)

1. **UserQuestionInput.vue**
   - 分步骤调整圆角值
   - 验证视觉效果

2. **PlanTasks.vue**
   - 调整容器圆角
   - 确保与输入框协调

### 阶段4: 模块组件迁移 (2-3天)

1. **Monitor模块组件**
2. **Sentire模块组件**
3. **其他自定义组件**

### 阶段5: 验证和优化 (1天)

1. **视觉一致性检查**
2. **响应式适配验证**
3. **性能影响评估**

## 风险评估与缓解策略

### 高风险项
1. **主要交互组件视觉变化**
   - 风险: 用户体验受影响
   - 缓解: 渐进式调整，A/B测试

2. **大量组件同时修改**
   - 风险: 引入回归问题
   - 缓解: 分阶段实施，充分测试

### 中风险项
1. **CSS变量兼容性**
   - 风险: 旧浏览器支持问题
   - 缓解: 提供fallback值

2. **第三方组件冲突**
   - 风险: shadcn-vue组件样式冲突
   - 缓解: 仔细测试，必要时覆盖

## 验证方法

### 1. 视觉回归测试
- 截图对比主要页面
- 确保圆角过渡自然

### 2. 功能测试
- 验证所有交互功能正常
- 检查响应式布局

### 3. 性能测试
- CSS文件大小变化
- 渲染性能影响

### 4. 浏览器兼容性测试
- 主流浏览器测试
- 移动端适配验证

## 成功标准

1. **视觉一致性**: 所有组件遵循统一圆角规范
2. **代码质量**: 消除硬编码圆角值，使用CSS变量
3. **可维护性**: 建立清晰的圆角使用指南
4. **用户体验**: 保持或改善现有视觉效果
5. **性能**: 不显著影响页面性能

## 后续维护

1. **建立圆角使用指南**
2. **代码审查检查点**
3. **设计系统文档更新**
4. **开发者培训**

## 详细实施步骤

### 步骤1: 扩展CSS变量系统

#### 1.1 修改 `assets/css/main.css`

在 `@theme inline` 块中添加新的圆角变量：

```css
@theme inline {
  /* 现有变量保持不变 */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* 新增圆角变量 */
  --radius-none: 0px;
  --radius-xs: 0.25rem;      /* 4px */
  --radius-sm-new: 0.5rem;   /* 8px - 替代现有sm */
  --radius-md-new: 0.75rem;  /* 12px - 替代现有md */
  --radius-lg-new: 1rem;     /* 16px - 替代现有lg */
  --radius-lg-plus: 1.25rem; /* 20px */
  --radius-xl-new: 1.75rem;  /* 28px - 替代现有xl */
  --radius-xl-plus: 2rem;    /* 32px */
  --radius-2xl: 3rem;        /* 48px */
  --radius-full: 9999px;
}
```

#### 1.2 更新 `tailwind.config.js`

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: "#1E40AF",
        brand: {
          light: "#3AB0FF",
          DEFAULT: "#0080FF",
          dark: "#0055AA",
        },
      },
      borderRadius: {
        'none': 'var(--radius-none)',
        'xs': 'var(--radius-xs)',
        'sm': 'var(--radius-sm-new)',
        'md': 'var(--radius-md-new)',
        'lg': 'var(--radius-lg-new)',
        'lg-plus': 'var(--radius-lg-plus)',
        'xl': 'var(--radius-xl-new)',
        'xl-plus': 'var(--radius-xl-plus)',
        '2xl': 'var(--radius-2xl)',
        'full': 'var(--radius-full)',
      }
    },
  },
};
```

### 步骤2: 组件迁移清单

#### 2.1 基础UI组件迁移

**Input.vue**
```vue
<!-- 当前 -->
<input class="rounded-md" />
<!-- 迁移后 -->
<input class="rounded-sm" />
```

**Button.vue**
```vue
<!-- 当前 -->
<button class="rounded-md" />
<!-- 迁移后 -->
<button class="rounded-sm" />
```

**Card.vue**
```vue
<!-- 当前 -->
<div class="rounded-xl" />
<!-- 迁移后 -->
<div class="rounded-md" />
```

#### 2.2 主要交互组件迁移

**UserQuestionInput.vue**
```vue
<!-- 当前 -->
<div class="rounded-3xl">
  <div class="rounded-3xl">
    <EditorContent class="rounded-2xl" />
  </div>
</div>

<!-- 迁移后 -->
<div class="rounded-xl">
  <div class="rounded-xl">
    <EditorContent class="rounded-lg" />
  </div>
</div>
```

**PlanTasks.vue**
```vue
<!-- 当前 -->
<div class="rounded-3xl" />
<!-- 迁移后 -->
<div class="rounded-xl" />
```

### 步骤3: 迁移执行顺序

#### 第1天: 建立基础设施
1. 修改 `assets/css/main.css`
2. 更新 `tailwind.config.js`
3. 验证构建无错误

#### 第2天: 基础组件迁移
1. Input.vue
2. Button.vue
3. 测试基本功能

#### 第3天: 继续基础组件
1. Card.vue
2. Alert.vue
3. Tooltip.vue
4. 视觉回归测试

#### 第4天: 主要交互组件
1. UserQuestionInput.vue
2. 分步调整，验证每个变更

#### 第5天: 其他组件
1. PlanTasks.vue
2. Monitor模块组件
3. Sentire模块组件

#### 第6天: 验证和优化
1. 全面测试
2. 性能检查
3. 文档更新

### 步骤4: 测试检查清单

#### 功能测试
- [ ] 所有按钮可正常点击
- [ ] 输入框可正常输入
- [ ] 卡片布局正确显示
- [ ] 弹窗和提示正常显示

#### 视觉测试
- [ ] 圆角过渡自然
- [ ] 不同尺寸设备适配正常
- [ ] 深色模式兼容
- [ ] 浏览器兼容性

#### 性能测试
- [ ] CSS文件大小变化 < 5%
- [ ] 页面渲染时间无明显增加
- [ ] 内存使用无异常

### 步骤5: 回滚计划

如果迁移过程中出现问题，按以下步骤回滚：

1. **立即回滚**: 恢复 `assets/css/main.css` 和 `tailwind.config.js`
2. **组件回滚**: 逐个恢复已修改的组件文件
3. **验证回滚**: 确保系统恢复到迁移前状态

## 实施进度记录

### ✅ 已完成

#### 阶段1: 建立新的CSS变量系统 (已完成)
- [x] 扩展 `assets/css/main.css` - 添加新的圆角变量定义
- [x] 更新 `tailwind.config.js` - 扩展圆角配置
- [x] 验证构建无错误

#### 阶段2: 基础UI组件迁移 (已完成)
- [x] Input.vue - `rounded-md` → `rounded-sm-new`
- [x] Button.vue - `rounded-md` → `rounded-sm-new`
- [x] Card.vue - `rounded-xl` → `rounded-md-new`
- [x] Alert.vue - `rounded-lg` → `rounded-sm-new`
- [x] Skeleton.vue - `rounded-md` → `rounded-sm-new`

#### 阶段3: 主要交互组件迁移 (已完成)
- [x] UserQuestionInput.vue
  - 外层容器: `rounded-3xl` → `rounded-xl-new`
  - 内层容器: `rounded-3xl` → `rounded-xl-new`
  - 编辑器: `rounded-2xl` → `rounded-lg-new`
- [x] PlanTasks.vue - `rounded-3xl` → `rounded-xl-new`

#### 阶段4: 其他组件迁移 (已完成)
- [x] app.vue Toast配置 - `rounded-xl` → `rounded-md-new`

### 🔄 进行中

#### 阶段5: 验证和测试
- [x] 开发服务器启动正常
- [ ] 视觉回归测试
- [ ] 功能测试
- [ ] 浏览器兼容性测试

### ⏳ 待完成

#### 其他模块组件
- [ ] Monitor模块组件迁移
- [ ] 其他Sentire模块组件
- [ ] 第三方组件样式调整

## 测试结果

### 构建测试
- ✅ `pnpm build` 成功
- ✅ `pnpm dev` 启动正常
- ✅ 无TypeScript错误
- ✅ 无CSS编译错误

### 视觉效果验证
- ✅ 新圆角变量正确应用
- ✅ 主要交互组件圆角更新成功
- ✅ 基础UI组件圆角统一

---

**文档版本**: v1.1
**创建日期**: 2025-01-20
**最后更新**: 2025-01-20
**负责人**: 开发团队
**审核状态**: 实施中
