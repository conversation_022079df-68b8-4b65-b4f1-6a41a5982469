# Sentire页面字体和间距改进报告

## 改进概述

根据Context7提供的字体层级规范，我们对Sentire页面的各级标题、段落进行了系统性的字体大小、行高和上下间距调整，建立了统一的竖向节奏。

## 应用的字体层级规范

### 字体层级标准

| 层级 | Tailwind类 | 像素值 | 行高 | 下间距 | 应用场景 |
|------|------------|--------|------|--------|----------|
| H1 页面主标题 | `text-3xl` | 30px | `leading-tight` | `mb-8` | SENTIRE主标题 |
| H2 模块标题 | `text-xl` | 20px | `leading-snug` | `mb-4` | 深度分析标题 |
| H3 小标题·标签 | `text-sm` | 14px | `leading-snug` | `mb-2` | 步骤标题、任务标题 |
| Body 正文 | `text-base` | 16px | `leading-relaxed` | `mb-4` | 分析内容、用户问题 |
| Caption 说明 | `text-xs` | 12px | `leading-snug` | - | 建议文字、图表标注 |

### 竖向间距规范

- **页面主标题 → 下一块内容**: `mb-8` (32px)
- **模块标题 → 正文首段**: `mb-4` (16px)
- **小标题/标签 → 其内容**: `mb-2` (8px)
- **正文段落之间**: `mb-4` (16px)
- **图表组件上下**: `my-6` (24px)
- **大区块（模块）之间**: `mt-8` (32px)

## 具体改进内容

### 1. UserQuestionInput.vue
**改进前**:
```vue
<h1 class="title font-bold text-center mb-0 quickfocus-title text-2xl md:text-3xl lg:text-4xl">
```

**改进后**:
```vue
<h1 class="title text-3xl font-semibold leading-tight text-center mb-8 quickfocus-title">
```

**变更说明**:
- 应用H1页面主标题规范
- 统一字体大小为`text-3xl`
- 添加`leading-tight`行高
- 设置`mb-8`下间距

### 2. UnderstandingPhase.vue
**主要改进**:
- 容器间距: `my-4` → `my-6`
- 步骤标题: 添加`leading-snug mb-2`
- 内容区域: `space-y-2` → `space-y-4`
- 文本内容: 添加`leading-snug mb-4`

**改进后样式**:
```vue
<!-- 容器 -->
<div class="my-6 border rounded-lg-new border-bordercolor p-3">

<!-- 标题 -->
<div class="text-sm font-medium leading-snug text-default-text">

<!-- 步骤标题 -->
<div class="text-sm font-medium leading-snug mb-2 transition">

<!-- 内容区域 -->
<div class="text-xs leading-snug text-muted-foreground transition lg:text-sm flex flex-col space-y-4">

<!-- 文本内容 -->
<Markdown class="text-xs leading-snug prose-p:text-default-text font-normal prose-code:text-xs mb-4" />
```

### 3. DeepAnalysisPhase.vue
**主要改进**:
- 标题样式: `prose-headings:!font-medium` → `prose-headings:!font-semibold`
- 标题行高: 添加`prose-headings:leading-snug`
- 标题间距: 添加`prose-headings:mb-4`
- 段落间距: `prose-p:my-3` → `prose-p:my-4`
- 段落行高: 添加`prose-p:leading-relaxed`
- 列表间距: `prose-ul:mb-3` → `prose-ul:mb-4`
- 组件间距: `my-2` → `my-6`

**改进后样式**:
```vue
<Markdown class="prose prose-stone min-w-full prose-img:my-3 prose-img:mx-auto prose-sm prose-p:my-4 prose-p:text-deep-analysis-text prose-p:leading-relaxed prose-headings:!font-semibold prose-headings:text-xl prose-headings:leading-snug prose-headings:mb-4 prose-ul:mb-4" />
```

### 4. PlanTasks.vue
**主要改进**:
- 容器间距: `mb-3` → `my-6`
- 标题样式: 添加`text-sm font-medium leading-snug`
- 计数器样式: 添加`leading-snug`
- Markdown内容: 添加`prose-p:leading-snug`

**改进后样式**:
```vue
<!-- 容器 -->
<div class="glass-container my-6">

<!-- 标题 -->
<div class="text-sm font-medium leading-snug text-gray-800">Process Tasks</div>

<!-- 计数器 -->
<div class="text-sm leading-snug text-gray-600 w-10 text-right flex items-center justify-end gap-0.5">

<!-- 任务内容 -->
<Markdown class="prose-sm prose-p:leading-snug" />
```

### 5. Suggestions.vue
**主要改进**:
- 容器样式: `text-sm mt-3` → `text-xs leading-snug mt-6`
- 建议项间距: `mb-2.5` → `mb-4`
- 文字样式: 添加`leading-snug`

**改进后样式**:
```vue
<!-- 容器 -->
<div class="text-xs leading-snug mt-6 text-deep-analysis-text">

<!-- 建议项 -->
<component class="hover:shadow-lg mb-4" />

<!-- 文字内容 -->
<span class="whitespace-normal break-words text-deep-analysis-text text-xs leading-snug">
```

### 6. UserQuestion.vue
**主要改进**:
- 粘性模式: 添加`leading-relaxed`
- 普通模式: 添加`leading-snug mb-4`

**改进后样式**:
```vue
<!-- 粘性模式 -->
<div class="pl-4 text-base leading-relaxed text-default-text font-semibold relative rounded-border-left truncate">

<!-- 普通模式 -->
<div class="pl-4 text-xl md:text-2xl lg:text-3xl leading-snug text-default-text font-semibold relative rounded-border-left mb-4">
```

### 7. HistoryAnalysis/DeepAnalysis.vue
**主要改进**:
- 标题样式: `prose-headings:!font-medium` → `prose-headings:!font-semibold`
- 添加标题行高和间距: `prose-headings:leading-snug prose-headings:mb-4`
- 段落间距: `prose-p:my-3` → `prose-p:my-4`
- 段落行高: 添加`prose-p:leading-relaxed`
- 列表间距: `prose-ul:mb-3` → `prose-ul:mb-4`

## 竖向节奏验证

### 间距系统
所有间距都基于Tailwind的4px步进系统：
- `mb-2` = 8px
- `mb-4` = 16px
- `my-6` = 24px
- `mb-8` = 32px
- `mt-8` = 32px

### 层级关系
1. **页面主标题** (`text-3xl`) → 32px间距 → **下一区块**
2. **模块标题** (`text-xl`) → 16px间距 → **正文内容**
3. **小标题** (`text-sm`) → 8px间距 → **具体内容**
4. **正文段落** (`text-base`) → 16px间距 → **下一段落**
5. **说明文字** (`text-xs`) → 紧凑排列

### 行高系统
- **标题类**: `leading-tight` (H1) / `leading-snug` (H2, H3)
- **正文类**: `leading-relaxed` (Body)
- **说明类**: `leading-snug` (Caption)

## 改进效果

### ✅ 统一性
- 建立了清晰的字体层级体系
- 统一了行高标准
- 规范了间距使用

### ✅ 可读性
- 合适的行高提升了文字可读性
- 适当的间距改善了内容层次感
- 统一的字体大小减少了视觉混乱

### ✅ 维护性
- 使用标准Tailwind类，便于维护
- 建立了可复用的设计规范
- 为未来开发提供了明确指导

## 使用指南

### 新组件开发
在开发新的Sentire页面组件时，请遵循以下规范：

```vue
<!-- H1 页面主标题 -->
<h1 class="text-3xl font-semibold leading-tight mb-8">

<!-- H2 模块标题 -->
<h2 class="text-xl font-semibold leading-snug mb-4">

<!-- H3 小标题·标签 -->
<h3 class="text-sm font-medium leading-snug mb-2">

<!-- Body 正文 -->
<p class="text-base leading-relaxed mb-4">

<!-- Caption 说明 -->
<span class="text-xs leading-snug">

<!-- 大区块间距 -->
<div class="my-6">

<!-- 模块间距 -->
<section class="mt-8">
```

### 响应式考虑
对于需要响应式的标题，保持现有的响应式设计：
```vue
<h1 class="text-xl md:text-2xl lg:text-3xl leading-snug mb-4">
```

这套改进确保了Sentire页面具有统一、清晰、易读的视觉层次，为用户提供了更好的阅读体验。
