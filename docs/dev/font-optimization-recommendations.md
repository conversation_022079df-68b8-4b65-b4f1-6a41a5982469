# 字体层级优化建议

## 概述

基于 Context7 和 Tailwind CSS 最佳实践，为 Capehorn 项目提供理性而必要的字体优化方案，避免过度工程化。

## 优先级分级

### 🔴 高优先级 - 立即实施

#### 1. 补充缺失的字体层级

**问题**: `text-lg` (18px) 未被使用，造成层级缺口

**解决方案**:
```vue
<!-- 在适当组件中添加 text-lg 使用 -->

<!-- 重要提示文字 -->
<p class="text-lg font-medium text-primary">
  重要操作提示
</p>

<!-- 副标题 -->
<h5 class="text-lg font-semibold mb-3">
  功能模块副标题
</h5>

<!-- 引导文字 -->
<div class="text-lg leading-relaxed text-muted-foreground">
  用户引导说明
</div>
```

**实施位置**:
- `UserQuestionInput.vue` - 引导文字
- `AssistantMessage.vue` - 重要提示
- 各模块的副标题

#### 2. 字体预加载优化

**当前问题**: 缺少关键字体预加载，影响首屏渲染

**解决方案**:
```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  app: {
    head: {
      link: [
        {
          rel: 'preload',
          href: '/fonts/inter-regular.woff2',
          as: 'font',
          type: 'font/woff2',
          crossorigin: ''
        },
        {
          rel: 'preload', 
          href: '/fonts/roboto-mono-v23-latin_latin-ext-regular.woff2',
          as: 'font',
          type: 'font/woff2',
          crossorigin: ''
        }
      ]
    }
  }
})
```

### 🟡 中等优先级 - 近期实施

#### 3. 扩展品牌字体支持

**问题**: Sentire 字体仅支持 normal 字重

**解决方案**:
```css
/* assets/css/font.css */

/* 添加 Medium 字重 */
@font-face {
  font-family: "Sentire";
  src: url("/fonts/sentire-medium.woff2") format("woff2"),
       url("/fonts/sentire-medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* 添加 Bold 字重 */
@font-face {
  font-family: "Sentire";
  src: url("/fonts/sentire-bold.woff2") format("woff2"),
       url("/fonts/sentire-bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
```

**使用示例**:
```vue
<!-- 品牌标题 -->
<h1 class="font-['Sentire'] font-medium text-4xl">
  SENTIRE
</h1>

<!-- 强调品牌元素 -->
<span class="font-['Sentire'] font-bold text-xl">
  品牌强调文字
</span>
```

#### 4. 改进字体回退策略

**当前问题**: 字体回退链不够完整

**解决方案**:
```css
/* assets/css/main.css */
body {
  font-family: "Inter", 
               -apple-system, 
               BlinkMacSystemFont, 
               "Segoe UI", 
               "Roboto", 
               "Helvetica Neue", 
               Arial, 
               sans-serif;
}

/* 为代码字体添加更好的回退 */
.font-mono {
  font-family: "Roboto Mono",
               "SF Mono",
               Monaco,
               "Cascadia Code",
               "Roboto Mono",
               Consolas,
               "Courier New",
               monospace;
}
```

### 🟢 低优先级 - 长期优化

#### 5. 添加语义化字体工具类

**目标**: 提供更直观的字体使用方式

**实施方案**:
```css
/* assets/css/main.css */
@layer utilities {
  /* 语义化标题类 */
  .text-display { @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight; }
  .text-headline { @apply text-2xl md:text-3xl lg:text-4xl font-semibold leading-tight; }
  .text-title { @apply text-xl md:text-2xl font-medium leading-snug; }
  .text-subtitle { @apply text-lg font-medium leading-snug; }
  
  /* 语义化正文类 */
  .text-body-large { @apply text-lg leading-relaxed; }
  .text-body { @apply text-base leading-relaxed; }
  .text-body-small { @apply text-sm leading-normal; }
  .text-caption { @apply text-xs leading-snug text-muted-foreground; }
  
  /* 品牌字体类 */
  .text-brand { @apply font-['Sentire']; }
  .text-code { @apply font-mono text-sm; }
}
```

**使用示例**:
```vue
<template>
  <!-- 更直观的语义化使用 -->
  <h1 class="text-headline">页面标题</h1>
  <h2 class="text-title">模块标题</h2>
  <p class="text-body">正文内容</p>
  <span class="text-caption">说明文字</span>
  
  <!-- 品牌元素 -->
  <div class="text-brand text-display">SENTIRE</div>
</template>
```

## 实施计划

### 第一阶段 (本周)
1. ✅ 添加 `text-lg` 使用场景
2. ✅ 配置字体预加载
3. ✅ 更新字体回退策略

### 第二阶段 (下周)
1. 🔄 扩展 Sentire 字体字重
2. 🔄 添加语义化工具类
3. 🔄 更新组件使用规范

### 第三阶段 (下月)
1. 📋 性能监控和优化
2. 📋 可访问性测试
3. 📋 文档完善

## 避免过度工程化的原则

### ✅ 推荐做法

1. **渐进式改进**: 基于现有标准逐步优化
2. **保持简单**: 优先使用 Tailwind 标准类
3. **性能优先**: 只添加必要的字体文件
4. **向后兼容**: 确保现有组件正常工作

### ❌ 避免的做法

1. **过度自定义**: 不要创建过多自定义字体大小
2. **复杂层级**: 避免超过 10 个字体层级
3. **性能损失**: 不要加载不必要的字体变体
4. **破坏性变更**: 避免大规模重构现有组件

## 质量检查清单

### 实施前检查
- [ ] 确认字体文件存在且优化
- [ ] 验证 Tailwind 配置正确
- [ ] 检查现有组件兼容性
- [ ] 测试响应式表现

### 实施后验证
- [ ] 字体加载性能测试
- [ ] 跨浏览器兼容性测试
- [ ] 可访问性检查
- [ ] 视觉回归测试

## 监控指标

### 性能指标
- **字体加载时间**: < 100ms
- **首屏渲染**: 无字体闪烁
- **文件大小**: 字体文件总大小 < 500KB

### 用户体验指标
- **可读性**: 符合 WCAG 2.1 AA 标准
- **一致性**: 95% 以上组件使用标准类
- **响应式**: 所有断点下表现良好

## 总结

本优化方案遵循"理性而必要"的原则，重点解决实际问题：

1. **补充层级缺口** - 提升设计灵活性
2. **优化加载性能** - 改善用户体验
3. **扩展品牌支持** - 增强品牌表现力
4. **完善回退策略** - 提高兼容性

所有建议都基于现有架构，避免过度工程化，确保实施的可行性和维护性。
