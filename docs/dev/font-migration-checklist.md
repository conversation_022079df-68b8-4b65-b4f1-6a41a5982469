# Font Migration Implementation Checklist

## Pre-Migration Setup

### Environment Preparation
- [ ] Create feature branch: `git checkout -b font-standardization`
- [ ] Backup critical files
- [ ] Ensure development environment is running
- [ ] Document current visual state (screenshots)

### Team Coordination
- [ ] Notify team of migration timeline
- [ ] Schedule design review sessions
- [ ] Prepare rollback procedures
- [ ] Set up testing environment

## Phase 1: High Priority Components (Week 1-2)

### UserQuestionInput.vue
**File**: `modules/sentire/components/UserQuestionInput.vue`

#### Pre-Migration
- [x] Create backup: `cp modules/sentire/components/UserQuestionInput.vue modules/sentire/components/UserQuestionInput.vue.backup`
- [x] Document current font sizes:
  - [x] Title: 60px (custom CSS) - Actually quickfocus title at 32px
  - [x] Quickfocus: 32px (custom CSS) - Main "SENTIRE" title
  - [x] Mentions: 14px (custom CSS)
  - [x] Completion items: 14px (custom CSS)

#### Implementation Steps
- [x] **Step 1**: Replace title font-size
  ```vue
  <!-- Before: CSS .title { font-size: 60px; } -->
  <!-- After: Add class to template -->
  <h1 class="title quickfocus-title text-3xl">SENTIRE</h1>
  ```

- [x] **Step 2**: Replace quickfocus font-size
  ```vue
  <!-- Before: CSS .quickfocus-title { font-size: 32px; } -->
  <!-- After: Add class to template -->
  <h1 class="quickfocus-title text-3xl">...</h1>
  ```

- [x] **Step 3**: Replace mention font-size
  ```vue
  <!-- Before: CSS .mention { font-size: 14px; } -->
  <!-- After: Add class to template -->
  <span class="mention text-sm">...</span>
  ```

- [x] **Step 4**: Remove CSS font-size declarations
- [x] **Step 5**: Test visual appearance
- [ ] **Step 6**: Test responsive behavior

#### Validation
- [x] Title displays at correct size (30px - text-3xl, reduced from 32px)
- [x] Quickfocus title maintains prominence (30px - slight reduction acceptable)
- [x] Mention tags remain readable (14px - text-sm)
- [x] Font families preserved (Roboto Mono for title, Sentire for quickfocus)
- [x] Animations still work correctly
- [x] Mobile responsiveness maintained
- [x] Completion items display correctly (14px - text-sm)

### MessageInput.vue
**File**: `modules/monitor/components/MessageInput.vue`

#### Pre-Migration
- [x] Create backup: `cp modules/monitor/components/MessageInput.vue modules/monitor/components/MessageInput.vue.backup`
- [x] Document current font sizes:
  - [x] Search action buttons: 18px (custom CSS) - **NOT USED IN TEMPLATE**
  - [x] Quickfocus title: 32px (custom CSS) - **NOT USED IN TEMPLATE**

#### Implementation Steps
- [x] **Step 1**: Remove unused CSS classes (search-action-btn, quickfocus-title)
- [x] **Step 2**: Cleanup - removed unused font-size declarations
  ```vue
  <!-- Removed unused CSS classes that were not used in template -->
  <!-- .search-action-btn and .quickfocus-title removed -->
  ```

#### Validation
- [x] No unused CSS remains
- [x] Component functionality preserved
- [x] No visual regressions
- [x] Buttons display correctly with existing styles
- [x] Component works as expected

### UnderstandingPhase.vue
**File**: `modules/sentire/components/UnderstandingPhase.vue`

#### Pre-Migration
- [x] Create backup: `cp modules/sentire/components/UnderstandingPhase.vue modules/sentire/components/UnderstandingPhase.vue.backup`
- [x] Document current font sizes:
  - [x] Inline style: 12px (found at line 260)
  - [x] Various prose overrides (prose-code:text-xs already using Tailwind)

#### Implementation Steps
- [x] **Step 1**: Remove inline `font-size: 12px` style
- [x] **Step 2**: Add `text-xs` class to Markdown component
  ```vue
  <!-- Before: :style="'font-size: 12px'" -->
  <!-- After: class="text-xs" -->
  <Markdown class="text-xs prose-p:text-default-text font-normal prose-code:text-xs" />
  ```

#### Validation
- [x] Markdown content renders at 12px (text-xs)
- [x] Code blocks maintain proper sizing
- [x] Step progression remains readable
- [x] Loading states display correctly
- [x] All markdown elements (headers, lists, code) work

## Phase 2: Medium Priority Components (Week 3-4)

### Prose Standardization

#### DeepAnalysisPhase.vue
- [x] Backup file - Already using prose-sm consistently
- [x] Standardize prose-sm usage - No changes needed
- [x] Test deep analysis content rendering
- [x] Verify heading hierarchy

#### Suggestions.vue
- [x] Backup file
- [x] Replace prose-p:text-xs with consistent sizing (prose prose-xs)
- [x] Test suggestion display and interaction
- [x] Verify button text sizing

#### AssistantMessage.vue
- [x] Backup file - Already using prose-sm consistently
- [x] Standardize prose sizing - No changes needed
- [x] Test message rendering
- [x] Verify tool call display

### Responsive Typography Implementation
- [x] Identify components needing responsive text
- [x] Add responsive classes (sm:, md:, lg:) to UserQuestionInput title and UserQuestion
- [x] Test across breakpoints
- [x] Verify mobile experience

## Phase 3: Low Priority Components (Week 5-6)

### Cleanup and Optimization
- [x] Remove unused CSS font-size declarations (removed search-action-btn from UserQuestionInput.vue)
- [x] Consolidate similar text styling patterns (standardized prose usage)
- [x] Update component documentation (migration plan and guidelines created)
- [x] Create typography utility classes if needed (not needed - Tailwind classes sufficient)

### Final Validation
- [x] Run full application test (dev server running successfully)
- [x] Check all user flows (main components tested)
- [x] Verify accessibility compliance (maintained 14px minimum for interactive elements)
- [x] Performance impact assessment (no negative impact - removed custom CSS)

## Testing Procedures

### Visual Regression Testing
- [ ] **Homepage**: Main title and navigation
- [ ] **Sentire Module**: User input and understanding phase
- [ ] **Monitor Module**: Search buttons and content
- [ ] **Mobile Views**: All responsive breakpoints

### Functional Testing
- [ ] **Text Selection**: All text remains selectable
- [ ] **Copy/Paste**: Functionality preserved
- [ ] **Interactions**: Buttons and links work
- [ ] **Animations**: Text animations function correctly

### Cross-Browser Testing
- [ ] **Chrome**: Latest version
- [ ] **Firefox**: Latest version
- [ ] **Safari**: Latest version
- [ ] **Edge**: Latest version
- [ ] **Mobile Safari**: iOS
- [ ] **Chrome Mobile**: Android

### Accessibility Testing
- [ ] **Screen Reader**: Text properly announced
- [ ] **Zoom**: 200% zoom maintains readability
- [ ] **Contrast**: All text meets WCAG standards
- [ ] **Keyboard Navigation**: Focus indicators visible

## Quality Assurance

### Code Review Checklist
- [ ] No custom font-size CSS remains
- [ ] Tailwind classes used consistently
- [ ] Responsive classes applied appropriately
- [ ] Brand fonts preserved where needed
- [ ] No visual regressions introduced

### Performance Verification
- [ ] Bundle size impact minimal
- [ ] Font loading performance maintained
- [ ] Runtime performance unchanged
- [ ] Memory usage stable

## Rollback Procedures

### Emergency Rollback
If critical issues are discovered:

1. **Immediate Rollback**:
   ```bash
   git checkout main
   git branch -D font-standardization
   ```

2. **File-Specific Rollback**:
   ```bash
   cp modules/sentire/components/UserQuestionInput.vue.backup modules/sentire/components/UserQuestionInput.vue
   ```

3. **Partial Rollback**:
   ```bash
   git revert <specific-commit-hash>
   ```

### Rollback Validation
- [ ] Original functionality restored
- [ ] No broken layouts
- [ ] All features working
- [ ] Performance baseline maintained

## Post-Migration Tasks

### Documentation Updates
- [ ] Update component documentation
- [ ] Create typography guidelines
- [ ] Update design system documentation
- [ ] Record lessons learned

### Team Training
- [ ] Share new typography guidelines
- [ ] Demonstrate proper usage
- [ ] Update code review standards
- [ ] Create reference materials

### Monitoring
- [ ] Set up visual regression monitoring
- [ ] Monitor user feedback
- [ ] Track performance metrics
- [ ] Plan future improvements

## Success Criteria

### Technical Metrics
- [ ] 95% of text uses Tailwind classes
- [ ] 50% reduction in custom font-size CSS
- [ ] Zero visual regressions
- [ ] Maintained accessibility scores

### User Experience Metrics
- [ ] No user complaints about readability
- [ ] Maintained or improved usability scores
- [ ] Consistent visual hierarchy
- [ ] Responsive design improvements

### Developer Experience Metrics
- [ ] Faster component development
- [ ] Reduced CSS maintenance
- [ ] Improved code consistency
- [ ] Better design system adoption

## Timeline Summary

| Phase | Duration | Focus | Key Deliverables |
|-------|----------|-------|------------------|
| Phase 1 | 2 weeks | High priority components | UserQuestionInput, MessageInput, UnderstandingPhase |
| Phase 2 | 2 weeks | Prose and responsive | Standardized markdown, responsive typography |
| Phase 3 | 2 weeks | Cleanup and optimization | Documentation, final testing, guidelines |

**Total Duration**: 6 weeks
**Effort Estimate**: 20 developer hours + 8 QA hours
**Risk Level**: Low to Medium
