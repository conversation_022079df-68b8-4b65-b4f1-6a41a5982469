# PlanTasks 组件磨玻璃样式重设计

## 概述

本文档记录了 PlanTasks 组件从传统白色卡片样式改为现代磨玻璃（Glass Morphism）样式的设计改进。

## 设计目标

1. **现代化视觉效果**: 采用磨玻璃效果提升组件的现代感和视觉层次
2. **一致性设计**: 与 ScrollToBottom 组件的磨玻璃样式保持一致
3. **更好的视觉层次**: 通过半透明效果和模糊背景增强内容的可读性
4. **优雅的交互体验**: 添加悬停效果和平滑过渡动画

## 设计变更

### 原始设计
- 纯白色背景 (`bg-white`)
- 灰色边框 (`border-stone-200`)
- 传统阴影效果 (`shadow-xl`)
- 黑色文字

### 新设计 - 磨玻璃效果
- 半透明白色背景 (`rgba(255, 255, 255, 0.25)`) - 与ScrollToBottom组件保持一致
- 磨玻璃模糊效果 (`backdrop-filter: blur(10px)`)
- 半透明白色边框 (`rgba(255, 255, 255, 0.3)`)
- 深色文字配色方案 (gray-800, gray-600)
- 柔和阴影效果增强立体感

## 技术实现

### CSS 样式特性

```css
.glass-container {
  /* 半透明白色背景 - 与ScrollToBottom保持一致 */
  background: rgba(255, 255, 255, 0.25);

  /* 磨玻璃模糊效果 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  /* 边框增强玻璃质感 */
  border: 1px solid rgba(255, 255, 255, 0.3);

  /* 渐变背景增加深度 */
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );

  /* 柔和阴影效果 */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
```

### 颜色配色方案调整

| 元素 | 原始颜色 | 新颜色 | 说明 |
|------|----------|--------|------|
| 主文字 | `text-stone-500` | `text-gray-800` | 深色文字适配白色磨玻璃背景 |
| 副文字 | `text-stone-500` | `text-gray-600` | 中等深度灰色副文字 |
| 图标 | `text-stone-400` | `text-gray-500` | 中等深度灰色图标 |
| 成功状态 | `text-green-500` | `text-green-600` | 深绿色适配白色背景 |
| 错误状态 | `text-red-500` | `text-red-600` | 深红色适配白色背景 |
| 信息状态 | `text-blue-500` | `text-blue-600` | 深蓝色适配白色背景 |

### 交互效果

#### 悬停效果
- 背景透明度增加 (`rgba(255, 255, 255, 0.35)`)
- 模糊效果增强 (`blur(12px)`)
- 边框亮度提升 (`rgba(255, 255, 255, 0.4)`)
- 轻微上移效果 (`translateY(-1px)`)
- 阴影增强

#### 过渡动画
- 使用 `cubic-bezier(0.4, 0, 0.2, 1)` 缓动函数
- 300ms 过渡时间
- 应用于所有可变属性

## 兼容性考虑

### 浏览器支持
- `backdrop-filter` 需要现代浏览器支持
- 包含 `-webkit-backdrop-filter` 前缀以支持 Safari
- 在不支持的浏览器中会优雅降级为普通半透明背景

### 可访问性
- 保持足够的颜色对比度
- 使用 `prose-invert` 类确保 Markdown 内容在深色背景下可读
- 保留所有原有的键盘导航和屏幕阅读器支持

## 文件变更

### 修改的文件
- `modules/sentire/components/PlanTasks.vue`

### 主要变更点
1. 替换容器类名从 `bg-white border rounded-md-new mb-3 border-stone-200 shadow-xl` 到 `glass-container mb-3`
2. 更新所有文字颜色为深色系 (gray-800, gray-600, gray-500)
3. 移除 `prose-invert` 类，使用标准 Markdown 样式
4. 增加自定义 CSS 样式实现磨玻璃效果，与 ScrollToBottom 组件保持一致
5. 优化边框和分隔线样式，使用半透明白色

## 设计参考

本设计参考了项目中已有的 ScrollToBottom 组件的磨玻璃实现，确保整体设计语言的一致性。

## 测试验证

### 视觉测试
- [x] 磨玻璃效果正确显示
- [x] 文字对比度足够
- [x] 悬停效果流畅
- [x] 展开/折叠动画正常

### 功能测试
- [x] 任务状态图标正确显示
- [x] 计数器功能正常
- [x] 展开/折叠功能正常
- [x] Markdown 渲染正常

### 浏览器兼容性
- [x] Chrome/Edge (Chromium)
- [x] Safari
- [x] Firefox

## 后续优化建议

1. **响应式优化**: 考虑在移动设备上调整模糊强度以提升性能
2. **主题适配**: 未来可考虑添加浅色主题下的磨玻璃变体
3. **动画增强**: 可以添加更多微交互动画提升用户体验
4. **性能优化**: 监控 backdrop-filter 对性能的影响

---

**文档版本**: v1.0  
**创建日期**: 2025-01-20  
**最后更新**: 2025-01-20  
**负责人**: 开发团队  
**审核状态**: 已完成
