# Prose 配置简化迁移备份

## 迁移日期
2025-01-27

## 原始配置备份

### DeepAnalysisPhase.vue 原始配置
```vue
<Markdown
  :source="extractAndRemoveSummary((message as TextMessage).text).textWithoutSummary"
  class="prose prose-stone min-w-full prose-img:my-6 prose-img:mx-auto prose-sm prose-p:my-4 prose-p:text-base prose-p:leading-relaxed prose-p:mb-4 prose-headings:!font-semibold prose-headings:text-xl prose-headings:leading-snug prose-headings:mb-4 prose-ul:mb-4"
/>
```

### HistoryAnalysis/DeepAnalysis.vue 原始配置
```vue
<Markdown
  :source="extractAndRemoveSummary((content as TextMessage).text).textWithoutSummary"
  class="prose prose-stone min-w-full prose-img:my-6 prose-img:mx-auto prose-sm prose-p:my-4 prose-p:text-base prose-p:leading-relaxed prose-headings:!font-semibold prose-headings:text-xl prose-headings:leading-snug prose-headings:mb-4 prose-li:text-base prose-ul:mb-4"
/>
```

### Suggestions.vue 原始配置
```vue
<Markdown
  source={suggestion.content}
  class="prose prose-xs prose-p:whitespace-normal text-left"
/>
```

### PlanTasks.vue 原始配置
```vue
<Markdown :source="runningTask?.title ?? ''" class="prose-sm prose-p:leading-snug" />
<Markdown :source="plan.title" class="prose-sm prose-p:leading-snug prose-p:mb-2" />
```

### UnderstandingPhase.vue 原始配置
```vue
<Markdown
  :source="TOOL_CALL_NAMES[step.title as keyof typeof TOOL_CALL_NAMES] ?? step.title"
  class="prose-p:text-default-text font-normal !prose-code:text-sm prose-p:truncate"
/>

<Markdown
  :source="(content as TextMessage).text"
  class="text-xs leading-snug prose-p:text-default-text font-normal prose-code:text-xs mb-4"
/>
```

### HistoryAnalysis/Understand.vue 原始配置
```vue
<Markdown
  :source="(content as TextMessage).text"
  class="text-xs prose-p:text-default-text prose-code:text-xs"
/>
```

## 回滚指令

如果需要回滚，请执行以下步骤：

1. 恢复 tailwind.config.js 到原始状态
2. 恢复各组件的原始 prose 配置
3. 重新构建项目

## 迁移验证清单

- [ ] DeepAnalysisPhase.vue 视觉效果一致
- [ ] HistoryAnalysis/DeepAnalysis.vue 视觉效果一致  
- [ ] Suggestions.vue 视觉效果一致
- [ ] PlanTasks.vue 视觉效果一致
- [ ] UnderstandingPhase.vue 视觉效果一致
- [ ] Markdown 渲染功能正常
- [ ] Mermaid 图表渲染正常
- [ ] 代码高亮功能正常
- [ ] 图片查看器功能正常
- [ ] 响应式显示正常
