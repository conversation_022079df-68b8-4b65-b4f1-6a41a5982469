# Typography Guidelines

## Overview

This document establishes typography standards for the project, following Context7 best practices and Tailwind CSS conventions. These guidelines ensure consistency, maintainability, and accessibility across all components.

## Font Stack

### Primary Font Family
```css
font-family: "Inter", "Roboto Mono", sans-serif;
```

- **Inter**: Primary typeface for all UI text
- **Roboto Mono**: Monospace font for code and technical content
- **sans-serif**: System fallback

### Special Font Families
- **Sentire**: Brand-specific font for special elements (logos, hero text)
- **Roboto Mono**: Used for technical/code content and specific brand elements

## Typography Scale

### Standard Tailwind Classes (Preferred)

| Class | Size | Use Case | Examples |
|-------|------|----------|----------|
| `text-xs` | 12px | Captions, metadata, small labels | Stepper descriptions, debug info |
| `text-sm` | 14px | UI controls, buttons, form labels | Button text, form labels, tooltips |
| `text-base` | 16px | Body text, default content | Standard paragraphs, form inputs |
| `text-lg` | 18px | Emphasized content, large buttons | Action button text, emphasized content |
| `text-xl` | 20px | Small headings, section titles | Prose headings, content sections |
| `text-2xl` | 24px | Medium headings | User questions, page headings |
| `text-3xl` | 30px | Large headings | Section titles, modal headings |
| `text-4xl` | 36px | Display headings | Page titles, hero headings |
| `text-5xl` | 48px | Large display text | Landing page headings |
| `text-6xl` | 60px | Hero text | Main application title |

### Custom Utilities (When Needed)

For non-standard sizes that don't fit Tailwind's scale:

```css
/* Add to assets/css/main.css */
@layer utilities {
  .text-brand-32 {
    font-size: 32px;
    line-height: 1.2;
  }
}
```

## Usage Guidelines

### 1. Component Text Sizing

#### Buttons
```vue
<!-- Standard button -->
<Button class="text-sm">Action</Button>

<!-- Large button -->
<Button class="text-base">Primary Action</Button>

<!-- Small button -->
<Button class="text-xs">Secondary</Button>
```

#### Form Elements
```vue
<!-- Form label -->
<Label class="text-sm">Field Label</Label>

<!-- Input placeholder and content -->
<Input class="text-base" />

<!-- Help text -->
<p class="text-xs text-muted-foreground">Help text</p>
```

#### Headings
```vue
<!-- Page title -->
<h1 class="text-3xl font-bold">Page Title</h1>

<!-- Section heading -->
<h2 class="text-2xl font-semibold">Section</h2>

<!-- Subsection -->
<h3 class="text-xl font-medium">Subsection</h3>

<!-- Small heading -->
<h4 class="text-lg font-medium">Small Heading</h4>
```

### 2. Responsive Typography

Use responsive classes for better mobile experience:

```vue
<!-- Responsive heading -->
<h1 class="text-2xl md:text-3xl lg:text-4xl">
  Responsive Title
</h1>

<!-- Responsive body text -->
<p class="text-sm md:text-base">
  Responsive paragraph
</p>
```

### 3. Prose Content

For markdown and rich text content:

```vue
<!-- Standard prose -->
<Markdown class="prose prose-sm" />

<!-- Large prose -->
<Markdown class="prose prose-base" />

<!-- Custom prose sizing -->
<Markdown class="prose prose-sm prose-headings:text-xl" />
```

## Component-Specific Standards

### UserQuestionInput
- **Title**: `text-6xl` (60px) with Roboto Mono
- **Quickfocus**: `text-3xl` (30px) with Sentire font
- **Mentions**: `text-sm` (14px)

### MessageInput
- **Action buttons**: `text-lg` (18px)
- **Labels**: `text-sm` (14px)

### Understanding Phase
- **Step titles**: `text-sm` (14px)
- **Content**: `text-xs` (12px)
- **Status indicators**: `text-xs` (12px)

### Navigation
- **Menu items**: `text-sm` (14px)
- **Sidebar labels**: `text-xs` (12px)
- **Tooltips**: `text-sm` (14px)

## Best Practices

### DO ✅

1. **Use Tailwind classes** for standard sizes
   ```vue
   <p class="text-base">Standard paragraph</p>
   ```

2. **Implement responsive typography**
   ```vue
   <h1 class="text-xl md:text-2xl lg:text-3xl">Title</h1>
   ```

3. **Maintain semantic hierarchy**
   ```vue
   <h1 class="text-3xl">Main Title</h1>
   <h2 class="text-2xl">Section</h2>
   <h3 class="text-xl">Subsection</h3>
   ```

4. **Use consistent prose sizing**
   ```vue
   <Markdown class="prose prose-sm" />
   ```

### DON'T ❌

1. **Avoid custom font-size in CSS**
   ```css
   /* Don't do this */
   .custom-text {
     font-size: 15px;
   }
   ```

2. **Don't use inline styles**
   ```vue
   <!-- Don't do this -->
   <p style="font-size: 14px">Text</p>
   ```

3. **Don't mix sizing approaches**
   ```vue
   <!-- Don't mix custom CSS with Tailwind -->
   <p class="text-base custom-font-size">Mixed approach</p>
   ```

4. **Don't override prose unnecessarily**
   ```vue
   <!-- Avoid excessive overrides -->
   <Markdown class="prose prose-p:text-xs prose-h1:text-lg prose-h2:text-base" />
   ```

## Accessibility Considerations

### Font Size Minimums
- **Never go below 12px** for body text
- **Use 14px minimum** for interactive elements
- **Ensure sufficient contrast** with background colors

### Line Height
- **Body text**: Use default Tailwind line heights
- **Headings**: Consider tighter line heights for large text
- **Code**: Use monospace-appropriate line heights

### Responsive Behavior
- **Scale appropriately** for mobile devices
- **Maintain readability** at all screen sizes
- **Test with browser zoom** up to 200%

## Migration Strategy

When updating existing components:

1. **Identify current font sizes**
2. **Map to nearest Tailwind class**
3. **Test visual impact**
4. **Update responsive behavior if needed**
5. **Remove custom CSS**

### Common Migrations
```vue
<!-- Before -->
<style>
.title { font-size: 18px; }
</style>
<div class="title">Title</div>

<!-- After -->
<div class="text-lg">Title</div>
```

## Tools and Resources

### Development Tools
- **Tailwind CSS IntelliSense**: VS Code extension for class suggestions
- **Browser DevTools**: Inspect computed font sizes
- **Accessibility Inspector**: Check font size compliance

### References
- [Tailwind Typography Documentation](https://tailwindcss.com/docs/font-size)
- [Web Content Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/Understanding/resize-text.html)
- [Inter Font Documentation](https://rsms.me/inter/)

## Validation Checklist

Before deploying typography changes:

- [ ] All text uses Tailwind classes or approved custom utilities
- [ ] No custom font-size CSS declarations remain
- [ ] Responsive behavior works across breakpoints
- [ ] Accessibility standards are maintained
- [ ] Visual hierarchy is preserved
- [ ] Brand fonts (Sentire, Roboto Mono) are used appropriately
- [ ] Prose content renders correctly
- [ ] Interactive elements meet minimum size requirements

## Future Considerations

### Potential Enhancements
- **CSS custom properties** for dynamic font scaling
- **Container queries** for component-based responsive typography
- **Variable fonts** for enhanced performance and flexibility
- **Design tokens** for systematic typography management

### Monitoring
- **Regular audits** of font size usage
- **Performance monitoring** of font loading
- **Accessibility testing** with real users
- **Design system evolution** based on usage patterns
