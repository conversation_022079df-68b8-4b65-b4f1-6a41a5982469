# Font Standardization Migration Plan

## Executive Summary

This plan standardizes font usage across the project by migrating from mixed custom CSS font-size declarations to Tailwind's built-in text size classes, following Context7 best practices while preserving visual hierarchy and functionality.

## Goals

- **Consistency**: Standardize font sizes using Tailwind classes
- **Maintainability**: Reduce custom CSS in favor of utility classes
- **Accessibility**: Maintain responsive typography and readability
- **Performance**: Leverage Tailwind's optimized class system
- **Preservation**: Keep existing visual hierarchy intact

## Current State Analysis

### Font Size Distribution
| Size | Current Usage | Tailwind Equivalent | Priority |
|------|---------------|-------------------|----------|
| 60px | Custom CSS (title) | Custom variable | High |
| 32px | Custom CSS (quickfocus) | Custom variable | High |
| 24px | text-2xl | ✅ Standard | Low |
| 20px | text-xl | ✅ Standard | Low |
| 18px | Custom CSS | text-lg | High |
| 16px | text-base | ✅ Standard | Low |
| 14px | text-sm | ✅ Standard | Low |
| 12px | Custom CSS + text-xs | text-xs | Medium |

### Issues Identified
1. **Mixed Implementation**: Custom CSS alongside Tailwind classes
2. **Inline Styles**: Direct font-size in style attributes
3. **Prose Overrides**: Inconsistent markdown typography
4. **Missing Responsive**: Limited responsive typography usage

## Implementation Strategy

### Phase 1: High Priority (Week 1-2)
**Target**: Components with custom font-size CSS declarations

#### 1.1 UserQuestionInput.vue
**Current Issues**:
- Custom 60px title
- Custom 32px quickfocus-title
- Custom 14px mention tags

**Migration Plan**:
```vue
<!-- BEFORE -->
<style lang="scss" scoped>
.title {
  font-size: 60px;
  color: $primary-color;
  font-weight: 900;
  font-family: "Roboto Mono", sans-serif;
}

.quickfocus-title {
  font-family: "Sentire", sans-serif;
  font-size: 32px;
}

.mention {
  font-size: 14px;
  font-weight: 500;
}
</style>

<!-- AFTER -->
<style lang="scss" scoped>
.title {
  color: $primary-color;
  font-weight: 900;
  font-family: "Roboto Mono", sans-serif;
}

.quickfocus-title {
  font-family: "Sentire", sans-serif;
}
</style>

<!-- Template changes -->
<div class="title text-6xl">...</div>
<div class="quickfocus-title text-3xl">...</div>
<span class="mention text-sm font-medium">...</span>
```

**Risk Assessment**: Low - Only affects styling, no functional changes
**Testing**: Visual regression on title display and mention tags

#### 1.2 MessageInput.vue
**Current Issues**:
- Custom 18px search-action-btn

**Migration Plan**:
```vue
<!-- BEFORE -->
<style lang="scss" scoped>
.search-action-btn {
  font-size: 18px;
  // other styles...
}
</style>

<!-- AFTER -->
<!-- Remove font-size from CSS, add to template -->
<Button class="search-action-btn text-lg">...</Button>
```

**Risk Assessment**: Low - Simple class replacement
**Testing**: Verify button text sizing in search actions

#### 1.3 UnderstandingPhase.vue
**Current Issues**:
- Inline style `font-size: 12px`
- Mixed prose overrides

**Migration Plan**:
```vue
<!-- BEFORE -->
<Markdown
  :source="(content as TextMessage).text"
  :class="'prose-p:text-default-text font-normal prose-code:text-xs'"
  :style="'font-size: 12px'"
/>

<!-- AFTER -->
<Markdown
  :source="(content as TextMessage).text"
  class="text-xs prose-p:text-default-text font-normal prose-code:text-xs"
/>
```

**Risk Assessment**: Medium - Affects markdown rendering
**Testing**: Verify markdown content readability and code block sizing

### Phase 2: Medium Priority (Week 3-4)
**Target**: Prose overrides and responsive typography gaps

#### 2.1 Standardize Prose Typography
**Components Affected**:
- DeepAnalysisPhase.vue
- Suggestions.vue
- AssistantMessage.vue

**Migration Strategy**:
```vue
<!-- BEFORE: Multiple prose size variants -->
<Markdown class="prose-sm" />
<Markdown class="prose-p:text-xs" />

<!-- AFTER: Consistent prose sizing -->
<Markdown class="prose prose-sm" />
<Markdown class="prose prose-xs" />
```

#### 2.2 Implement Responsive Typography
**Target Areas**:
- User questions (sticky vs normal mode)
- Form inputs and labels
- Navigation elements

**Implementation**:
```vue
<!-- Enhanced responsive sizing -->
<div class="text-base md:text-lg lg:text-xl">Responsive heading</div>
<p class="text-sm md:text-base">Responsive body text</p>
```

### Phase 3: Low Priority (Week 5-6)
**Target**: Components already using standard classes

#### 3.1 Optimization and Cleanup
- Remove unused CSS font-size declarations
- Consolidate similar text styling patterns
- Update documentation

#### 3.2 Create Typography Utilities
**Add to assets/css/main.css**:
```css
@layer utilities {
  .text-display {
    font-size: 60px;
    line-height: 1.1;
  }
  
  .text-brand-title {
    font-size: 32px;
    line-height: 1.2;
  }
}
```

## Detailed File Migration Plan

### High Priority Files

#### File: `modules/sentire/components/UserQuestionInput.vue`
**Estimated Time**: 2 hours
**Changes Required**:
1. Replace `.title` font-size with `text-6xl` class
2. Replace `.quickfocus-title` font-size with `text-3xl` class
3. Replace `.mention` font-size with `text-sm` class
4. Test title display in both desktop and mobile views

**Before/After Comparison**:
- Title: 60px custom → 60px (text-6xl)
- Quickfocus: 32px custom → 30px (text-3xl) *slight reduction*
- Mentions: 14px custom → 14px (text-sm)

**Rollback Procedure**: Revert CSS changes, remove Tailwind classes

#### File: `modules/monitor/components/MessageInput.vue`
**Estimated Time**: 1 hour
**Changes Required**:
1. Remove font-size from `.search-action-btn`
2. Add `text-lg` to button template
3. Test search action button appearance

**Before/After Comparison**:
- Button text: 18px custom → 18px (text-lg)

#### File: `modules/sentire/components/UnderstandingPhase.vue`
**Estimated Time**: 1.5 hours
**Changes Required**:
1. Remove inline `font-size: 12px` style
2. Add `text-xs` class to Markdown component
3. Verify prose code sizing consistency
4. Test understanding phase content readability

**Before/After Comparison**:
- Markdown content: 12px inline → 12px (text-xs)

### Medium Priority Files

#### File: `modules/sentire/components/DeepAnalysisPhase.vue`
**Estimated Time**: 1 hour
**Changes Required**:
1. Standardize prose-sm usage
2. Ensure consistent heading sizes with prose-headings:text-xl
3. Test deep analysis content rendering

#### File: `modules/sentire/components/Suggestions.vue`
**Estimated Time**: 1 hour
**Changes Required**:
1. Replace prose-p:text-xs with consistent prose sizing
2. Standardize suggestion button text sizing
3. Test suggestion display and interaction

## Testing Strategy

### Visual Regression Checklist
- [ ] Title displays correctly at 60px
- [ ] Quickfocus titles maintain prominence
- [ ] Button text remains readable
- [ ] Markdown content preserves hierarchy
- [ ] Mention tags display properly
- [ ] Responsive behavior works across breakpoints

### Functional Testing
- [ ] All interactive elements remain clickable
- [ ] Text selection works properly
- [ ] Copy/paste functionality preserved
- [ ] Accessibility features maintained

### Cross-Browser Testing
- [ ] Chrome/Edge (Chromium)
- [ ] Firefox
- [ ] Safari
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

## Risk Mitigation

### High Risk Areas
1. **Title Sizing**: 32px → 30px reduction in quickfocus titles
   - **Mitigation**: Create custom utility class if visual impact is significant
2. **Markdown Rendering**: Changes to prose typography
   - **Mitigation**: Thorough testing of all markdown content types

### Rollback Procedures
1. **Git Branch Strategy**: Create feature branch for each phase
2. **Component-Level Rollback**: Maintain backup of original CSS
3. **Quick Revert**: Document exact class changes for rapid reversal

## Timeline and Resources

### Phase 1 (High Priority): 2 weeks
- **Week 1**: UserQuestionInput.vue and MessageInput.vue
- **Week 2**: UnderstandingPhase.vue and testing

### Phase 2 (Medium Priority): 2 weeks
- **Week 3**: Prose standardization
- **Week 4**: Responsive typography implementation

### Phase 3 (Low Priority): 2 weeks
- **Week 5**: Optimization and cleanup
- **Week 6**: Documentation and final testing

### Resource Requirements
- **Developer Time**: 1 developer, ~20 hours total
- **QA Time**: 8 hours for comprehensive testing
- **Design Review**: 2 hours for visual approval

## Success Metrics

1. **Consistency**: 95% of text uses Tailwind classes
2. **Maintainability**: 50% reduction in custom font-size CSS
3. **Performance**: No impact on bundle size or runtime performance
4. **Accessibility**: Maintain or improve accessibility scores
5. **Visual Integrity**: Zero visual regressions in core user flows

## Post-Migration Guidelines

### For Developers
1. **Always use Tailwind text classes** for new components
2. **Avoid custom font-size** unless absolutely necessary
3. **Use responsive typography** for better user experience
4. **Follow established hierarchy** when choosing text sizes

### For Designers
1. **Reference Tailwind scale** when specifying font sizes
2. **Consider responsive behavior** in design specifications
3. **Maintain consistency** with established typography patterns

## Implementation Commands

### Phase 1 Commands

#### UserQuestionInput.vue Migration

```bash
# 1. Create backup
cp modules/sentire/components/UserQuestionInput.vue modules/sentire/components/UserQuestionInput.vue.backup

# 2. Apply changes (manual editing required)
# Replace CSS font-size with Tailwind classes in template

# 3. Test changes
npm run dev
# Navigate to main input area and verify title sizing
```

#### MessageInput.vue Migration

```bash
# 1. Create backup
cp modules/monitor/components/MessageInput.vue modules/monitor/components/MessageInput.vue.backup

# 2. Apply changes
# Remove font-size from .search-action-btn CSS
# Add text-lg class to button elements

# 3. Test changes
# Navigate to monitor section and verify button text
```

### Validation Scripts

#### Visual Regression Test

```bash
# Run development server
npm run dev

# Test checklist URLs
echo "Testing URLs:"
echo "- http://localhost:3000/ (main title)"
echo "- http://localhost:3000/monitor (search buttons)"
echo "- http://localhost:3000/sentire (understanding phase)"
```

#### Build Verification

```bash
# Ensure no build errors after changes
npm run build

# Check for unused CSS
npm run lint:css
```

## Appendix A: Tailwind Text Size Reference

### Standard Tailwind Classes

```css
.text-xs    { font-size: 0.75rem; /* 12px */ }
.text-sm    { font-size: 0.875rem; /* 14px */ }
.text-base  { font-size: 1rem; /* 16px */ }
.text-lg    { font-size: 1.125rem; /* 18px */ }
.text-xl    { font-size: 1.25rem; /* 20px */ }
.text-2xl   { font-size: 1.5rem; /* 24px */ }
.text-3xl   { font-size: 1.875rem; /* 30px */ }
.text-4xl   { font-size: 2.25rem; /* 36px */ }
.text-5xl   { font-size: 3rem; /* 48px */ }
.text-6xl   { font-size: 3.75rem; /* 60px */ }
```

### Custom Utilities (if needed)

```css
/* Add to assets/css/main.css if exact pixel matching required */
@layer utilities {
  .text-brand-32 {
    font-size: 32px; /* For quickfocus titles if 30px is too small */
  }
}
```

## Appendix B: Component-Specific Notes

### UserQuestionInput.vue Considerations

- **Title font-family**: Preserve "Roboto Mono" for brand consistency
- **Quickfocus font-family**: Preserve "Sentire" for brand elements
- **Responsive behavior**: Consider mobile title sizing
- **Animation compatibility**: Ensure text animations still work

### MessageInput.vue Considerations

- **Button hierarchy**: Maintain visual prominence of action buttons
- **Icon alignment**: Verify icon-text alignment after size changes
- **Hover states**: Test hover effects with new text sizing

### UnderstandingPhase.vue Considerations

- **Markdown rendering**: Test all markdown elements (headers, lists, code)
- **Step progression**: Ensure step text remains readable
- **Loading states**: Verify text during loading animations

## Appendix C: Emergency Rollback

### Quick Rollback Commands

```bash
# Rollback specific file
git checkout HEAD~1 -- modules/sentire/components/UserQuestionInput.vue

# Rollback entire phase
git revert <commit-hash>

# Restore from backup
cp modules/sentire/components/UserQuestionInput.vue.backup modules/sentire/components/UserQuestionInput.vue
```

### Rollback Verification

```bash
# Verify rollback success
npm run dev
# Test affected components
# Confirm original functionality restored
```

## Conclusion

This migration plan provides a systematic approach to standardizing font usage while minimizing risk and preserving functionality. The phased approach allows for careful testing and validation at each step, ensuring a smooth transition to a more maintainable typography system.

### Key Benefits

- **Reduced maintenance overhead** through standardized classes
- **Improved consistency** across the application
- **Better responsive behavior** with Tailwind's built-in responsive utilities
- **Enhanced developer experience** with predictable typography patterns

### Success Indicators

- All custom font-size CSS replaced with Tailwind classes
- No visual regressions in user-facing components
- Improved code maintainability and consistency
- Preserved accessibility and responsive design principles
