# text-lg 字体层级回退报告

## 回退概述

根据用户反馈，对之前实施的部分 `text-lg` 字体层级使用进行回退，保留合适的使用场景，移除可能过于突出的应用。

## 已回退的组件

### 1. InteractiveToolCall 组件

**文件**: `modules/sentire/components/InteractiveToolCall.vue`

**回退内容**:
```vue
<!-- 回退前 -->
<p class="text-lg font-medium leading-relaxed text-stone-700">
  {{ props.question }}
</p>

<!-- 回退后 -->
<p class="text-base leading-relaxed">
  {{ props.question }}
</p>
```

**回退原因**: 交互式工具调用中的问题文字使用原有的 `text-base` 更为合适，避免过度突出

### 2. ArtifactReportToolCall 组件

**文件**: `modules/sentire/components/ArtifactReportToolCall.vue`

**回退内容**:
```vue
<!-- 回退前 -->
<div class="text-lg font-semibold mb-2 text-black leading-snug">
  <span class="break-all block whitespace-pre-wrap">
    {{ title }}
  </span>
</div>

<!-- 回退后 -->
<div class="!text-sm mb-1 text-black">
  <span class="break-all block whitespace-pre-wrap">
    {{ title }}
  </span>
</div>
```

**回退原因**: AI 报告标题恢复到原有的紧凑设计，保持与整体布局的协调性

### 3. StandardToolCall 组件

**文件**: `components/StandardToolCall.vue`

**回退内容**:
```vue
<!-- 回退前 -->
<div class="flex items-center cursor-pointer gap-x-1 mr-1 text-lg font-medium">
  {{ getToolDisplayName(props.name, props.input) }}
  <!-- ... -->
</div>

<!-- 回退后 -->
<div class="flex items-center cursor-pointer gap-x-1 mr-1">
  {{ getToolDisplayName(props.name, props.input) }}
  <!-- ... -->
</div>
```

**回退原因**: 工具调用名称使用原有样式，避免在工具展示中过度强调

## 保留的 text-lg 使用场景

### 1. PlanTasks 组件 ✅ 保留

**文件**: `modules/sentire/components/PlanTasks.vue`

**使用场景**: 任务列表展开状态的标题

```vue
<div v-if="isExpanded" class="text-lg font-semibold leading-snug mb-3 text-gray-800">
  Process Tasks
</div>
```

**保留原因**: 作为模块标题，使用 `text-lg` 提供清晰的层次结构

### 2. Suggestions 组件 ✅ 保留

**文件**: `modules/sentire/components/Suggestions.vue`

**使用场景**: 建议操作区域的标题

```vue
<h3 class="text-lg font-semibold leading-snug mb-4 text-gray-800">
  Suggested Actions
</h3>
```

**保留原因**: 作为功能区域的标题，`text-lg` 提供适当的视觉引导

### 3. AlertDescription 组件 ✅ 保留

**文件**: `components/ui/alert/AlertDescription.vue`

**使用场景**: 可选的大尺寸警告描述

```vue
<AlertDescription size="lg">
  重要系统提示信息
</AlertDescription>
```

**保留原因**: 作为可选功能，为重要警告提供更突出的显示选项

## 当前 text-lg 使用策略

### 适合使用 text-lg 的场景

1. **功能模块标题**: 如 "Process Tasks", "Suggested Actions"
2. **重要警告信息**: 需要用户特别注意的系统提示
3. **页面级副标题**: 介于主标题和正文之间的层级
4. **状态指示器**: 重要的状态或进度信息

### 不适合使用 text-lg 的场景

1. **工具调用名称**: 保持紧凑的工具展示
2. **报告标题**: 在卡片布局中保持协调性
3. **交互问题文字**: 避免过度突出影响整体平衡
4. **列表项内容**: 保持列表的一致性和可读性

## 设计原则调整

### 1. 谨慎使用原则

- `text-lg` 应该用于真正需要突出的标题和重要信息
- 避免在密集布局中过度使用
- 考虑与周围元素的视觉平衡

### 2. 层次清晰原则

- 确保 `text-lg` 的使用形成清晰的信息层次
- 避免与其他字体大小产生冲突
- 保持整体设计的和谐性

### 3. 功能导向原则

- 优先在功能性标题中使用
- 避免在内容密集的区域使用
- 考虑用户的阅读体验

## 实施验证

### 代码质量检查

- ✅ 所有回退文件通过 ESLint 检查
- ✅ 无 TypeScript 类型错误
- ✅ 符合项目代码规范

### 功能验证

- ✅ 回退后的组件功能正常
- ✅ 样式恢复到预期状态
- ✅ 无破坏性变更

### 视觉效果验证

- ✅ 整体布局保持协调
- ✅ 信息层次依然清晰
- ✅ 用户体验未受影响

## 文档更新

### 更新的文档

1. **text-lg-usage-examples.md**: 标记已回退的使用场景
2. **text-lg-rollback-report.md**: 本回退报告

### 使用指南更新

更新了 `text-lg` 的使用建议：

```vue
<!-- 推荐：功能模块标题 -->
<h3 class="text-lg font-semibold leading-snug mb-4">
  功能模块标题
</h3>

<!-- 推荐：重要警告 -->
<AlertDescription size="lg">
  重要系统提示
</AlertDescription>

<!-- 不推荐：工具名称 -->
<!-- <div class="text-lg font-medium">工具名称</div> -->

<!-- 不推荐：报告标题 -->
<!-- <div class="text-lg font-semibold">报告标题</div> -->
```

## 总结

通过这次回退操作，我们：

1. **优化了 text-lg 的使用策略**: 更加谨慎和有针对性地使用
2. **保持了设计的平衡性**: 避免过度突出影响整体协调
3. **建立了更清晰的使用原则**: 为未来的字体层级使用提供指导

当前保留的 `text-lg` 使用场景都是经过仔细考虑的，确保在提供视觉层次的同时不影响整体的设计平衡。这种调整体现了"理性而必要"的设计原则，避免了过度工程化的问题。

## 后续建议

1. **观察用户反馈**: 持续关注保留的 `text-lg` 使用效果
2. **谨慎扩展**: 未来添加新的 `text-lg` 使用场景时要更加谨慎
3. **定期评估**: 定期评估字体层级的使用效果和用户体验
4. **团队共识**: 确保团队对字体层级使用原则达成共识
