# Font Size Analysis Report

## Overview
This document analyzes the current font size usage across different layers of the project to identify patterns, inconsistencies, and opportunities for standardization.

## Current Font Configuration

### Font Families
- **Primary**: Inter (global default)
- **Monospace**: Roboto Mono (code and technical content)
- **Brand**: Sentire (special branding elements)

### Global Font Setup
```css
/* assets/css/main.css */
body {
  font-family: "Inter", "Roboto Mono", sans-serif;
}

body * {
  font-family: "Inter", "Roboto Mono", sans-serif;
}
```

## Font Size Usage Analysis

### 1. Tailwind Text Size Classes

#### Current Usage Patterns
| Class | Size | Usage Context | Examples |
|-------|------|---------------|----------|
| `text-xs` | 12px | Small labels, descriptions, code | Stepper descriptions, sidebar labels, debug info |
| `text-sm` | 14px | Standard UI text, buttons | Button text, form labels, clue status, tooltips |
| `text-base` | 16px | Default body text | User questions (sticky mode), textarea |
| `text-lg` | 18px | - | Not found in current usage |
| `text-xl` | 20px | Headings | Prose headings in deep analysis |
| `text-2xl` | 24px | Large headings | User questions (normal mode) |
| `text-3xl` | 30px | - | Not found in current usage |

#### Responsive Patterns
- `md:text-sm` - Responsive sizing in textarea component
- `lg:text-sm` - Responsive sizing in understanding phase

### 2. Custom Font Sizes (CSS)

#### Large Display Text
```css
/* UserQuestionInput.vue */
.title {
  font-size: 60px;  /* Main title */
}

.quickfocus-title {
  font-size: 32px;  /* Quick focus title */
}
```

#### Medium Text
```css
/* MessageInput.vue */
.search-action-btn {
  font-size: 18px;  /* Action button text */
}
```

#### Small Text
```css
/* UnderstandingPhase.vue */
:style="'font-size: 12px'"  /* Inline style for markdown */

/* UserQuestionInput.vue */
.mention {
  font-size: 14px;  /* Mention tags */
}

/* assets/css/main.css */
.sidebar-tooltip {
  font-size: 14px !important;  /* Tooltip text */
}
```

### 3. Prose Typography

#### Markdown Content
```css
/* DeepAnalysisPhase.vue */
prose-sm  /* Small prose variant */
prose-headings:text-xl  /* Heading override */

/* Suggestions.vue */
prose-p:text-xs  /* Paragraph override */

/* UnderstandingPhase.vue */
prose-code:text-xs  /* Code text override */
prose-code:text-sm  /* Code text override */
```

## Typography Hierarchy Analysis

### Current Hierarchy (Identified Patterns)

1. **Display Level** (60px)
   - Main application title
   - Hero text

2. **Large Headings** (32px)
   - Section titles
   - Quick focus titles

3. **Medium Headings** (24px - text-2xl)
   - User questions (normal mode)
   - Page headings

4. **Small Headings** (20px - text-xl)
   - Prose headings
   - Content section titles

5. **Large Body** (18px)
   - Action button text
   - Emphasized content

6. **Standard Body** (16px - text-base)
   - Default text content
   - User questions (sticky mode)
   - Form inputs

7. **Small Body** (14px - text-sm)
   - UI controls
   - Button text
   - Form labels
   - Status indicators
   - Tooltips

8. **Caption/Meta** (12px - text-xs)
   - Descriptions
   - Metadata
   - Debug information
   - Small labels

## Issues and Inconsistencies

### 1. Mixed Approaches
- **Problem**: Combination of Tailwind classes and custom CSS font-size
- **Impact**: Inconsistent sizing and maintenance complexity
- **Examples**: 
  - `font-size: 12px` inline styles vs `text-xs`
  - Custom 18px vs standard Tailwind sizes

### 2. Missing Standard Sizes
- **text-lg (18px)**: Not used but could replace custom 18px
- **text-3xl (30px)**: Could be useful for intermediate heading sizes

### 3. Responsive Inconsistency
- Limited use of responsive typography
- Only found in textarea and understanding phase components

### 4. Prose Overrides
- Frequent overrides of prose typography
- Inconsistent prose size variants (prose-sm vs custom overrides)

## Recommendations

### 1. Establish Typography Scale
Create a standardized typography scale using Tailwind's built-in sizes:

```css
/* Recommended hierarchy */
--text-display: 60px;     /* Custom for hero text */
--text-title: 32px;       /* Custom for main titles */
--text-heading-1: 24px;   /* text-2xl */
--text-heading-2: 20px;   /* text-xl */
--text-heading-3: 18px;   /* text-lg */
--text-body-large: 16px;  /* text-base */
--text-body: 14px;        /* text-sm */
--text-caption: 12px;     /* text-xs */
```

### 2. Standardize Implementation
- Replace custom font-size with Tailwind classes where possible
- Use CSS variables for non-standard sizes (60px, 32px)
- Implement consistent responsive typography

### 3. Prose Typography
- Standardize prose variants
- Reduce custom overrides
- Create consistent prose size hierarchy

### 4. Component Guidelines
- Button text: `text-sm` (14px)
- Form labels: `text-sm` (14px)
- Body content: `text-base` (16px)
- Captions/meta: `text-xs` (12px)
- Headings: Use semantic hierarchy (text-xl, text-2xl, etc.)

## Next Steps

1. **Audit Phase**: Complete inventory of all font size usage
2. **Standardization Phase**: Replace custom sizes with Tailwind classes
3. **Documentation Phase**: Create typography guidelines
4. **Implementation Phase**: Apply standards across all components
5. **Testing Phase**: Verify visual consistency and accessibility

## Files Requiring Updates

### High Priority
- `modules/sentire/components/UserQuestionInput.vue` - Custom font sizes
- `modules/monitor/components/MessageInput.vue` - Custom font sizes
- `modules/sentire/components/UnderstandingPhase.vue` - Inline styles

### Medium Priority
- All components using prose overrides
- Components with responsive typography gaps
- Tooltip and small text components

### Low Priority
- Components already using standard Tailwind classes
- Well-structured prose content
