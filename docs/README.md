# Capehorn Documentation

This directory contains comprehensive documentation for the Capehorn project.

## Directory Structure

```
docs/
├── README.md                    # This file - documentation overview
├── dev/                         # Development documentation
│   ├── ui-components/          # UI component documentation
│   ├── api/                    # API documentation
│   ├── architecture/           # System architecture docs
│   └── deployment/             # Deployment guides
├── user/                       # User-facing documentation
│   ├── guides/                 # User guides
│   └── tutorials/              # Step-by-step tutorials
└── changelog/                  # Change logs and release notes
```

## Development Documentation

### UI Components
- [ScrollToBottom Button Center Alignment](./dev/ui-components/scroll-to-bottom-button-center-alignment.md) - Documentation for centering the scroll-to-bottom button

### UI System
- [Border Radius Migration Plan](./dev/border-radius-migration-plan.md) - Comprehensive plan for standardizing border radius across all components

## Recent Changes

### January 20, 2025
- [Border Radius Standardization](./changelog/2025-01-20-border-radius-standardization.md) - Major UI system refactoring to establish unified border radius design system
- [UI Improvements](./changelog/2025-01-20-ui-improvements.md) - ScrollToBottom button positioning and documentation structure

## Documentation Standards

### File Naming Convention
- Use kebab-case for file names
- Include descriptive names that clearly indicate the content
- Add date prefixes for time-sensitive documents (YYYY-MM-DD)
- Use `.md` extension for Markdown files

### Content Structure
Each documentation file should include:
1. **Overview** - Brief description of the topic
2. **Change Summary** - What was changed and when
3. **Problem Statement** - Why the change was needed
4. **Solution** - How the problem was solved
5. **Technical Implementation** - Code changes and technical details
6. **Testing Considerations** - How to verify the changes
7. **References** - Related files and resources

### Language
- Write all documentation in English
- Use clear, concise language
- Include code examples where relevant
- Provide context for technical decisions

## Contributing to Documentation

When making changes to the codebase:

1. **Create documentation** for significant changes
2. **Update existing docs** if your changes affect documented features
3. **Follow naming conventions** outlined above
4. **Include relevant code snippets** and examples
5. **Add cross-references** to related documentation

## Document Categories

### Development (`dev/`)
Technical documentation for developers including:
- Component modifications
- API changes
- Architecture decisions
- Deployment procedures

### User (`user/`)
End-user documentation including:
- Feature guides
- Tutorials
- FAQ

### Changelog (`changelog/`)
Historical records of changes including:
- Release notes
- Breaking changes
- Migration guides

## Maintenance

Documentation should be:
- **Kept up-to-date** with code changes
- **Reviewed regularly** for accuracy
- **Archived appropriately** when no longer relevant
- **Cross-referenced** with related documentation

## Tools and Standards

- **Format**: Markdown (.md)
- **Style**: GitHub Flavored Markdown
- **Code blocks**: Include language specification
- **Links**: Use relative paths for internal links
- **Images**: Store in `docs/assets/images/` if needed
