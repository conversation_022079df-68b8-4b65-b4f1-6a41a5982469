/** @type {import('tailwindcss').Config} */
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: "#1E40AF",
        brand: {
          light: "#3AB0FF",
          DEFAULT: "#0080FF",
          dark: "#0055AA",
        },
      },

      // borderRadius 扩展已移除，使用 Tailwind 标准圆角
      typography: {
        // Sentire 主要内容变体 - 对应 DeepAnalysisPhase 的复杂配置
        'sentire-base': {
          css: {
            // 基础设置
            fontSize: '0.875rem', // prose-sm
            lineHeight: '1.25rem',
            maxWidth: 'none', // min-w-full

            // 段落样式 - 对应 prose-p:my-4 prose-p:text-base prose-p:leading-relaxed prose-p:mb-4
            p: {
              marginTop: '1rem',
              marginBottom: '1rem',
              fontSize: '1rem',
              lineHeight: '1.625',
            },

            // 标题样式 - 对应 prose-headings:!font-semibold prose-headings:text-xl prose-headings:leading-snug prose-headings:mb-4
            'h1, h2, h3, h4, h5, h6': {
              fontWeight: '600',
              fontSize: '1.25rem',
              lineHeight: '1.375',
              marginBottom: '1rem',
              marginTop: '1.5rem',
            },

            // 列表样式 - 对应 prose-ul:mb-4
            ul: {
              marginBottom: '1rem',
            },
            ol: {
              marginBottom: '1rem',
            },

            // 图片样式 - 对应 prose-img:my-6 prose-img:mx-auto
            img: {
              marginTop: '1.5rem',
              marginBottom: '1.5rem',
              marginLeft: 'auto',
              marginRight: 'auto',
            },

            // 代码样式保持原有设置
            code: {
              color: '#24292f',
              '&::before': {
                content: '""',
              },
              '&::after': {
                content: '""',
              },
            },

            // 强调文本
            strong: {
              color: '#24292f',
            },
          },
        },

        // Sentire 辅助内容变体 - 对应 Suggestions、PlanTasks 等组件
        'sentire-sm': {
          css: {
            // 基础设置
            fontSize: '0.75rem', // prose-xs
            lineHeight: '1rem',
            maxWidth: 'none',

            // 段落样式 - 更紧凑的间距
            p: {
              marginTop: '0.5rem',
              marginBottom: '0.5rem',
              fontSize: '0.75rem',
              lineHeight: '1.25',
              whiteSpace: 'normal', // prose-p:whitespace-normal
            },

            // 标题样式 - 更小的标题
            'h1, h2, h3, h4, h5, h6': {
              fontWeight: '500',
              fontSize: '0.875rem',
              lineHeight: '1.25',
              marginBottom: '0.5rem',
              marginTop: '1rem',
            },

            // 列表样式
            ul: {
              marginBottom: '0.5rem',
            },
            ol: {
              marginBottom: '0.5rem',
            },

            // 代码样式
            code: {
              fontSize: '0.75rem',
              color: '#24292f',
              '&::before': {
                content: '""',
              },
              '&::after': {
                content: '""',
              },
            },
          },
        },

        // Sentire 内联内容变体 - 对应 UnderstandingPhase 等简单使用场景
        'sentire-inline': {
          css: {
            fontSize: '0.75rem',
            lineHeight: '1.25',
            maxWidth: 'none',

            p: {
              margin: '0',
              fontSize: '0.75rem',
              lineHeight: '1.25',
              display: 'inline',
            },

            'h1, h2, h3, h4, h5, h6': {
              fontSize: '0.75rem',
              fontWeight: '400',
              margin: '0',
              display: 'inline',
            },

            code: {
              fontSize: '0.75rem',
              color: '#24292f',
            },
          },
        },
      },
    },
  },
};
