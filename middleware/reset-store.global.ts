import { useSentireStore } from "@/modules/sentire/stores";
import { useMonitorEditStore } from "~/modules/monitor/stores/monitorEditStore";
import { useCurrentThreadStore } from "~/modules/sentire/stores/currentThreadStore";
import { useThreadsStore } from "~/modules/sentire/stores/threadsStore";

export default defineNuxtRouteMiddleware(async (to, from) => {
  if (from.path && from.path !== to.path) {
    if (from.path.startsWith("/sentire")) {
      const sentireStore = useSentireStore();
      const currentThreadStore = useCurrentThreadStore();
      const historyAnalysesStore = useThreadsStore();
      sentireStore.$reset();
      await currentThreadStore.$reset();
      historyAnalysesStore.$reset();
      console.log("Reset sentire stores when leaving:", from.path);
    }
    if (from.path.startsWith("/monitor/board/edit/")) {
      const monitorStore = useMonitorEditStore();
      monitorStore.$reset();
      console.log("Reset monitor stores when leaving:", from.path);
    }
  }
});
