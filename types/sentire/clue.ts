export type LinkId = string;
export type PackId = string;
export type ClueId = string;

export type SparklineDimension = {
  name: string;
};
export type SparklineMetric = {
  name: string;
  unit: "bps" | "bytes" | "ms" | "percentage" | "count";
};

export type Clue = {
  id: number;
  thread_id: string;
  run_id: string;
  pack_id: PackId;
  clue_id: ClueId;
  link_id: LinkId;
  tool_name: string;
  status: "pending" | "timeout" | "error" | "done";
  is_selected: boolean;
  input_json: {
    priority: number;
    pack_name: {
      cn: string;
      en: string;
    };
    checkpoint: {
      name: string;
      sparkline: {
        sqlContent: string;
        type: "bar" | "line";
        dimension?: SparklineDimension;
        metric?: SparklineMetric;
      };
      status: {
        sqlContent: string;
      };
    };
  };
  output_json: {
    sparkline: SparklineDataPoint[];
    status: ClueStatus;
  };
};

export type SparklineDataPoint = {
  time: number;
  dimension: string;
  metric: number;
};

export type ClueStatus = {
  status: "error" | "warning" | "success" | "unknown";
  message: string;
  clue_start_time?: string; // ISO format
  clue_end_time?: string; // ISO format
};

export type ClueWithData = Clue & {
  sparkline: SparklineDataPoint[];
  health: ClueStatus;
};
