import { z } from "zod";

export const updateDashboardSchema = z
  .object({
    name: z
      .string()
      .min(1, "Name is required")
      .max(255, "Name must be less than 255 characters")
      .optional(),
    description: z
      .string()
      .min(1, "Description is required")
      .max(255, "Description must be less than 255 characters")
      .optional(),
    thumbnailConfig: z
      .record(z.any())
      .refine(
        (config) => Object.keys(config).length > 0,
        "Thumbnail config must not be empty"
      )
      .optional(),
    dashConfig: z
      .record(z.any())
      .refine(
        (config) => Object.keys(config).length > 0,
        "Dashboard config must not be empty"
      )
      .optional(),
  })
  .refine(
    (data) => Object.keys(data).length > 0,
    "At least one field must be provided for update"
  );
