export type TextMessage = { type: "text"; text: string };
export type ToolCallMessage = {
  type: "tool-call";
  toolCallId: string;
  toolName: string;
  args: Record<string, unknown>;
  result?: Record<string, unknown>;
  elapsedTime?: number;
};

export type AssistantMessage = {
  role: "assistant";
  content: Array<TextMessage | ToolCallMessage>;
};

export type UserMessage = {
  role: "user";
  content: Array<TextMessage>;
};

export type Message = AssistantMessage | UserMessage;
