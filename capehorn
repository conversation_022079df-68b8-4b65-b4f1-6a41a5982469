#!/bin/sh
if ! command -v pm2 &> /dev/null; then
    echo "pm2 is not installed. Please install it using: npm install -g pm2"
    exit 1
fi

# Start the application
start() {
    env $(cat .env | xargs) pm2 start --name "capehorn-app" node -- -r dotenv/config .output/server/index.mjs 
    echo "Started capehorn-app"
}

# Stop the application
stop() {
    pm2 stop "capehorn-app"
    echo "Stopped capehorn-app"
}

# Restart the application
restart() {
    pm2 restart "capehorn-app"
    echo "Restarted capehorn-app"
}

logs() {
    pm2 logs -f "capehorn-app"
}

# Parse command line arguments
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    logs)
        logs
        ;;
    restart)
        restart
        ;;
    *)
        echo "Usage: $0 {start|stop|restart}"
        exit 1
        ;;
esac
