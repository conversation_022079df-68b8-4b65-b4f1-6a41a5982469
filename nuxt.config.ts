import tailwindcss from "@tailwindcss/vite";
import { z } from "zod";

const envSchema = z.object({
  AGENT_URL: z.string(),
  JWT_SECRET: z.string(),
  DATABASE_URL: z.string(),
  MONITOR_URL: z.string(),
  DEBUG_CUBE_QUERY: z.string().optional(),
});

const env = envSchema.safeParse(process.env);
if (!env.success) {
  throw new Error(`Invalid environment variables: ${env.error.message}`);
}

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-05-15",
  devtools: { enabled: true },
  modules: ["@nuxt/eslint", "@pinia/nuxt", "shadcn-nuxt", "nuxt-mcp"],
  css: ["~/assets/css/main.css"],

  // 字体预加载优化 - 提升首屏渲染性能
  app: {
    head: {
      link: [
        {
          rel: 'preload',
          href: '/fonts/inter-regular.woff2',
          as: 'font',
          type: 'font/woff2',
          crossorigin: ''
        },
        {
          rel: 'preload',
          href: '/fonts/roboto-mono-v23-latin_latin-ext-regular.woff2',
          as: 'font',
          type: 'font/woff2',
          crossorigin: ''
        },
        {
          rel: 'preload',
          href: '/fonts/sentire.ttf',
          as: 'font',
          type: 'font/ttf',
          crossorigin: ''
        }
      ]
    }
  },
  vue: {
    compilerOptions: {
      isCustomElement: (tag: string) => tag.startsWith("l-"),
    },
  },
  vite: {
    plugins: [tailwindcss()],
  },
  shadcn: {
    /**
     * Prefix for all the imported component
     */
    prefix: "",
    /**
     * Directory that the component lives in.
     * @default "./components/ui"
     */
    componentDir: "./components/ui",
  },

  nitro: {
    routeRules: {
      "/agent/**": {
        proxy: `${env.data.AGENT_URL}/**`,
      },
      "/monitor-api/**": {
        proxy: `${env.data.MONITOR_URL}/**`,
      },
    },
  },

  runtimeConfig: {
    agentUrl: env.data.AGENT_URL,
    databaseUrl: env.data.DATABASE_URL,
    public: {
      debugCubeQuery: env.data.DEBUG_CUBE_QUERY,
      agentUrl: env.data.AGENT_URL,
      jwtSecret: env.data.JWT_SECRET,
    },
  },
});
