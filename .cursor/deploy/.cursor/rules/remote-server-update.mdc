---
description: "Remote server update procedure for capehorn project"
alwaysApply: false
---

# 远程服务器更新指令

## 服务器信息
- **服务器地址**: **************
- **项目路径**: /opt/capehorn
- **项目名称**: capehorn

## 标准更新流程

当需要更新远程服务器上的 capehorn 项目时，按照以下步骤执行：

### 1. 更新代码
```bash
cd /opt/capehorn && git pull
```

### 2. 构建项目
```bash
pnpm build
```

### 3. 重启服务
```bash
./capehorn restart
```

## 完整命令

可以使用以下一行命令完成所有步骤：

```bash
cd /opt/capehorn && git pull && pnpm build && ./capehorn restart
```

## 验证步骤

更新完成后，可以通过以下命令验证：

```bash
# 检查 Git 状态
git status

# 检查构建产物
ls -la .output

# 检查服务状态
ps aux | grep -v grep | grep capehorn
```

## 注意事项

1. 构建过程中可能会出现关于代码分块大小的警告，这是正常的
2. Tailwind CSS 可能会有 sourcemap 相关的警告，不影响功能
3. 确保服务重启后状态为 "online"
4. 如果服务启动失败，检查日志: `./capehorn logs`

## 使用说明

执行更新时无需询问确认，按照步骤直接执行即可。整个过程是自动化的，只需要等待完成即可。
