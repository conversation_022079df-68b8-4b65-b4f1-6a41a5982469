---
description: 
globs: 
alwaysApply: true
---
---
description: Project Instruction
globs: 
alwaysApply: true
---

# Capehorn Project Cursor Rules

## Project Overview
This is a Nuxt.js 3 application with Vue 3, TypeScript, and Tailwind CSS. It includes a modular architecture with the main "sentire" module for AI agent functionality.

## Core Technologies
- **Framework**: Nuxt.js 3 with Vue 3 Composition API
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS with custom component library
- **State Management**: Pinia stores
- **Package Manager**: pnpm
- **Icons**: Lucide Vue Next
- **UI Components**: Custom components based on Radix Vue
- **Linting**: ESLint

## File Structure & Naming Conventions

### Components
- Use PascalCase for component names
- Place reusable UI components in `components/ui/`
- Module-specific components go in `modules/{module}/components/`
- Component files should end with `.vue`
- Use descriptive, semantic names (e.g., `StandardToolCall.vue`, `AiProcessingText.vue`)

### Composables & Utilities
- Use camelCase with descriptive names
- Prefix composables with `use` (e.g., `useAgent.ts`, `useAgentStore.ts`)
- Place in appropriate directories: `hooks/`, `stores/`, `utils/`

### API Routes
- Use kebab-case for file names
- Follow REST conventions
- Place in `server/api/` with logical grouping

### Types
- Use PascalCase for interfaces and types
- Place shared types in `types/` directory
- Module-specific types in `modules/{module}/types/`

## Vue 3 Composition API Standards

### Script Setup
- Always use `<script setup lang="ts">`
- Import types with `import type { ... }`
- Use `defineProps<{}>()` for typed props
- Use `defineEmits<{}>()` for typed emits

### Reactivity
- Use `ref()` for primitive values
- Use `reactive()` for objects when needed
- Use `computed()` for derived state
- Use `watchEffect()` or `watch()` for side effects

### Component Structure
```vue
<script setup lang="ts">
// 1. Imports (external libraries first, then local)
// 2. Type definitions
// 3. Props and emits
// 4. Reactive state
// 5. Computed properties
// 6. Methods/functions
// 7. Lifecycle hooks
// 8. Watchers
</script>

<template>
  <!-- Use semantic HTML -->
  <!-- Apply Tailwind classes consistently -->
  <!-- Use v-if/v-show appropriately -->
</template>

<style scoped lang="scss">
/* Only when necessary */
/* Prefer Tailwind classes */
</style>
```

## TypeScript Guidelines

### Type Definitions
- Use interfaces for object shapes
- Use type aliases for unions and complex types
- Always type props, emits, and function parameters
- Use generic types where appropriate

### Imports
- Use explicit imports, avoid `import *`
- Group imports: external libraries, then local modules
- Use type-only imports when possible: `import type { ... }`

## Tailwind CSS Guidelines

### Class Organization
- Use utility-first approach
- Apply responsive design: `sm:`, `md:`, `lg:`, `xl:`
- Use custom design tokens consistently
- Group related classes logically

### Custom Styles
- Avoid custom CSS when possible
- Use SCSS only for complex component-specific styles
- Use CSS custom properties for dynamic values

## Component Library Patterns

### UI Components
- Follow the existing pattern in `components/ui/`
- Each component should have an `index.ts` for exports
- Use Radix Vue primitives as base when possible
- Implement proper accessibility attributes

### Icon Usage
- Use Lucide Vue Next icons consistently
- Import specific icons, not the entire library
- Use semantic icon names that match their purpose

## State Management with Pinia

### Store Structure
- Use `defineStore` with setup syntax
- Export typed store functions
- Group related state, getters, and actions
- Use proper TypeScript types for state

### Store Usage
- Access stores in components using composables
- Avoid direct store mutations outside actions
- Use getters for computed state

## API & Server Routes

### API Routes
- Use proper HTTP methods (GET, POST, PUT, DELETE)
- Return consistent response structures
- Handle errors appropriately
- Use TypeScript for request/response types

### Database Integration
- Use proper connection pooling
- Handle database errors gracefully
- Use typed queries where possible

## Performance & Best Practices

### Code Splitting
- Use dynamic imports for large components
- Implement proper lazy loading
- Leverage Nuxt's auto-imports

### Accessibility
- Use semantic HTML elements
- Provide proper ARIA attributes
- Ensure keyboard navigation works
- Maintain proper color contrast

### Error Handling
- Use try-catch blocks for async operations
- Provide meaningful error messages
- Implement proper loading states

## Code Style Preferences

### Formatting
- Use 2 spaces for indentation
- Use semicolons consistently
- Use double quotes for strings
- Trailing commas in objects and arrays

### Naming
- Use descriptive, self-documenting names
- Avoid abbreviations unless commonly understood
- Use consistent naming patterns across the codebase

### Comments
- Write comments for complex business logic
- Use JSDoc for functions and components
- Keep comments up-to-date with code changes

## Module-Specific Guidelines

### Sentire Module
- Follow the established patterns for AI agent components
- Use proper typing for message and tool interfaces
- Implement proper error boundaries
- Handle async operations with loading states

## Testing & Quality

### Code Quality
- Follow ESLint rules strictly
- Use TypeScript strict mode
- Implement proper prop validation
- Handle edge cases and error states

### Performance
- Use `v-memo` for expensive computations
- Implement proper key attributes in v-for loops
- Use `shallowRef` when appropriate
- Optimize component re-renders

## Common Patterns to Follow

1. **Composable Pattern**: Extract reusable logic into composables
2. **Provider Pattern**: Use provide/inject for deep prop drilling
3. **Slot Pattern**: Use slots for flexible component composition
4. **Event Pattern**: Use emits for component communication

## Common Anti-Patterns to Avoid

1. Don't use `any` type - always provide proper typing
2. Don't mutate props directly - use emits or v-model
3. Don't use inline styles - prefer Tailwind classes
4. Don't create deeply nested component structures
5. Don't forget to handle loading and error states
6. Don't use `reactive()` for simple primitive values

## Development Workflow

1. Always run type checking before committing
2. Use proper git commit messages
3. Keep components focused and single-purpose
4. Test components in isolation when possible
5. Document complex business logic
6. Use proper error boundaries and fallbacks 