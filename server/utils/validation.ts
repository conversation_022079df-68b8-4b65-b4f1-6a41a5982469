import type { z } from "zod";

/**
 * Validates request body using a Zod schema and throws a formatted error if validation fails
 */
export function validateRequestBody<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): T {
  const result = schema.safeParse(data);

  if (!result.success) {
    throw createError({
      statusCode: 400,
      statusMessage: "Validation failed",
      data: {
        errors: result.error.errors.map((err) => ({
          field: err.path.join("."),
          message: err.message,
        })),
      },
    });
  }

  return result.data;
}

/**
 * Validates path parameters (like ID) and converts to number
 */
export function validateNumericId(
  id: string | undefined,
  fieldName = "ID"
): number {
  if (!id) {
    throw createError({
      statusCode: 400,
      statusMessage: `${fieldName} is required`,
    });
  }

  const numericId = parseInt(id, 10);
  if (isNaN(numericId)) {
    throw createError({
      statusCode: 400,
      statusMessage: `Invalid ${fieldName}`,
    });
  }

  return numericId;
}
