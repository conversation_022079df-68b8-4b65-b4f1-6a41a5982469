import {
  integer,
  jsonb,
  pgSchema,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

export const capehornSchema = pgSchema("capehorn_app");

export const dashboardTable = capehornSchema.table("dashboard", {
  id: integer("id").primaryKey().generatedAlwaysAsIdentity(),
  name: varchar("name", { length: 255 }).notNull(),
  description: varchar("description", { length: 255 }),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
  owner: varchar("owner", { length: 255 }).notNull(),
  threadId: varchar("thread_id", { length: 255 }).notNull(),
});

export const dashboardConfigTable = capehornSchema.table("dashboard_config", {
  id: integer("id").primaryKey().generatedAlwaysAsIdentity(),
  dashboardId: integer("dashboard_id").references(() => dashboardTable.id, {
    onDelete: "cascade",
  }),
  dashConfig: jsonb("dash_config").notNull(),
  miniCardConfig: jsonb("mini_card_config").notNull(),
  version: integer("version").notNull(),
});
