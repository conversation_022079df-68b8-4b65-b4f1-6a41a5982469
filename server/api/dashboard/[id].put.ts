import { and, eq } from "drizzle-orm";
import { db } from "~/server/db";
import { dashboardTable } from "~/server/db/schema";
import {
  validateNumericId,
  validateRequestBody,
} from "~/server/utils/validation";
import { updateDashboardSchema } from "~/types/monitor/dashboard";

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, "id");
    const body = await readBody(event);
    const resourceId = getCookie(event, "resourceId");

    if (!resourceId) {
      throw createError({
        statusCode: 401,
        statusMessage: "Unauthorized",
      });
    }

    const validatedData = validateRequestBody(updateDashboardSchema, body);
    const dashboardId = validateNumericId(id, "Dashboard ID");

    const [existingDashboard] = await db
      .select()
      .from(dashboardTable)
      .where(
        and(
          eq(dashboardTable.id, dashboardId),
          eq(dashboardTable.owner, resourceId)
        )
      );

    if (!existingDashboard) {
      throw createError({
        statusCode: 404,
        statusMessage: "Dashboard not found",
      });
    }

    const updateData: Partial<{
      name: string;
      description: string;
      updatedAt: Date;
    }> = {
      updatedAt: new Date(),
    };

    if (validatedData.name !== undefined) {
      updateData.name = validatedData.name;
    }
    if (validatedData.description !== undefined) {
      updateData.description = validatedData.description;
    }

    const [updatedDashboard] = await db
      .update(dashboardTable)
      .set(updateData)
      .where(
        and(
          eq(dashboardTable.id, dashboardId),
          eq(dashboardTable.owner, resourceId)
        )
      )
      .returning();

    return updatedDashboard;
  } catch (error) {
    console.error("Error updating dashboard:", error);

    if (error instanceof Error && "statusCode" in error) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: "Failed to update dashboard",
    });
  }
});
