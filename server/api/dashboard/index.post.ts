import { MastraClient } from "@mastra/client-js";
import {
  adjectives,
  animals,
  colors,
  uniqueNamesGenerator,
} from "unique-names-generator";
import { MONITOR_AGENT } from "~/modules/monitor/const";
import { db } from "~/server/db";
import { dashboardTable } from "~/server/db/schema";

export default defineEventHandler(async (event) => {
  try {
    const resourceId = getCookie(event, "resourceId");

    if (!resourceId) {
      throw createError({
        statusCode: 401,
        statusMessage: "Unauthorized",
      });
    }

    const config = useRuntimeConfig();
    const client = new MastraClient({
      baseUrl: config.public.agentUrl,
    });
    const thread = await client.createMemoryThread({
      agentId: MONITOR_AGENT,
      resourceId: resourceId ?? "",
    });

    const randomName = uniqueNamesGenerator({
      dictionaries: [adjectives, colors, animals],
    });

    const [newDashboard] = await db
      .insert(dashboardTable)
      .values({
        name: `Dashboard ${randomName}`,
        description: "",
        owner: resourceId,
        threadId: thread.id,
      })
      .returning();

    return newDashboard;
  } catch (error) {
    console.error("Error creating dashboard:", error);

    if (error instanceof Error && "statusCode" in error) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: "Failed to create dashboard",
    });
  }
});
