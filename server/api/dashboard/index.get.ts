import { and, desc, eq, getTableColumns, sql } from "drizzle-orm";
import { db } from "~/server/db";
import { dashboardConfigTable, dashboardTable } from "~/server/db/schema";

export default defineEventHandler(async (event) => {
  try {
    const resourceId = getCookie(event, "resourceId");

    if (!resourceId) {
      throw createError({
        statusCode: 401,
        statusMessage: "Unauthorized",
      });
    }

    const latestConfigSq = db
      .select({
        dashboardId: dashboardConfigTable.dashboardId,
        dashConfig: dashboardConfigTable.dashConfig,
        miniCardConfig: dashboardConfigTable.miniCardConfig,
        version: dashboardConfigTable.version,
        rn: sql<number>`row_number() OVER (PARTITION BY ${dashboardConfigTable.dashboardId} ORDER BY ${dashboardConfigTable.version} DESC)`.as(
          "rn"
        ),
      })
      .from(dashboardConfigTable)
      .as("latest_config_sq");

    const dashboards = await db
      .select({
        ...getTableColumns(dashboardTable),
        dashConfig: latestConfigSq.dashConfig,
        miniCardConfig: latestConfigSq.miniCardConfig,
        version: latestConfigSq.version,
      })
      .from(dashboardTable)
      .leftJoin(
        latestConfigSq,
        and(
          eq(dashboardTable.id, latestConfigSq.dashboardId),
          eq(latestConfigSq.rn, 1)
        )
      )
      .where(eq(dashboardTable.owner, resourceId))
      .orderBy(desc(dashboardTable.updatedAt));

    return dashboards;
  } catch (error) {
    console.error("Error fetching dashboards:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "Failed to fetch dashboards",
    });
  }
});
