import { and, eq } from "drizzle-orm";
import { db } from "~/server/db";
import { dashboardTable } from "~/server/db/schema";
import { validateNumericId } from "~/server/utils/validation";

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, "id");
    const dashboardId = validateNumericId(id, "Dashboard ID");

    const resourceId = getCookie(event, "resourceId");
    if (!resourceId) {
      throw createError({
        statusCode: 401,
        statusMessage: "Unauthorized",
      });
    }

    const [existingDashboard] = await db
      .select()
      .from(dashboardTable)
      .where(
        and(
          eq(dashboardTable.id, dashboardId),
          eq(dashboardTable.owner, resourceId)
        )
      );

    if (!existingDashboard) {
      throw createError({
        statusCode: 404,
        statusMessage: "Dashboard not found",
      });
    }

    const [deletedDashboard] = await db
      .delete(dashboardTable)
      .where(
        and(
          eq(dashboardTable.id, dashboardId),
          eq(dashboardTable.owner, resourceId)
        )
      )
      .returning();

    return deletedDashboard;
  } catch (error) {
    console.error("Error deleting dashboard:", error);

    if (error instanceof Error && "statusCode" in error) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: "Failed to delete dashboard",
    });
  }
});
