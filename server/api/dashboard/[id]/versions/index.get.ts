import { desc, eq } from "drizzle-orm";
import { db } from "~/server/db";
import { dashboardConfigTable } from "~/server/db/schema";

export default defineEventHandler(async (event) => {
  const dashboardId = getRouterParam(event, "id");

  const versions = await db
    .select({
      version: dashboardConfigTable.version,
    })
    .from(dashboardConfigTable)
    .where(eq(dashboardConfigTable.dashboardId, Number(dashboardId)))
    .orderBy(desc(dashboardConfigTable.version));

  return versions.map((v) => v.version);
});
