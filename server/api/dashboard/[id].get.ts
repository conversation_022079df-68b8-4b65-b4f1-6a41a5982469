import { and, desc, eq, getTableColumns } from "drizzle-orm";
import { db } from "~/server/db";
import { dashboardConfigTable, dashboardTable } from "~/server/db/schema";
import { validateNumericId } from "~/server/utils/validation";

export default defineEventHandler(async (event) => {
  try {
    const id = getRouterParam(event, "id");
    const dashboardId = validateNumericId(id, "Dashboard ID");

    const resourceId = getCookie(event, "resourceId");

    if (!resourceId) {
      throw createError({
        statusCode: 401,
        statusMessage: "Unauthorized",
      });
    }

    const latestConfigSq = db
      .select({
        dashboardId: dashboardConfigTable.dashboardId,
        dashConfig: dashboardConfigTable.dashConfig,
        miniCardConfig: dashboardConfigTable.miniCardConfig,
        version: dashboardConfigTable.version,
      })
      .from(dashboardConfigTable)
      .where(eq(dashboardConfigTable.dashboardId, dashboardId))
      .orderBy(desc(dashboardConfigTable.version))
      .limit(1)
      .as("latest_config_sq");

    // 使用LEFT JOIN获取dashboard和最新配置
    const [result] = await db
      .select({
        ...getTableColumns(dashboardTable),
        dashConfig: latestConfigSq.dashConfig,
        miniCardConfig: latestConfigSq.miniCardConfig,
        version: latestConfigSq.version,
      })
      .from(dashboardTable)
      .leftJoin(
        latestConfigSq,
        eq(dashboardTable.id, latestConfigSq.dashboardId)
      )
      .where(
        and(
          eq(dashboardTable.id, dashboardId),
          eq(dashboardTable.owner, resourceId)
        )
      );

    if (!result) {
      throw createError({
        statusCode: 404,
        statusMessage: "Dashboard not found",
      });
    }

    return {
      ...result,
      version: result.version,
    };
  } catch (error) {
    console.error("Error fetching dashboard:", error);

    if (error instanceof Error && "statusCode" in error) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: "Failed to fetch dashboard",
    });
  }
});
