import Cookies from "js-cookie";

export interface UserSettings {
  systemPrompt: string;
  username: string;
  rolename?: string;
  referToSavedMemories: boolean;
  referToHistoricalChatRecords: boolean;
  userMemories: string[];
}

const STORAGE_KEY = "user_settings";

const defaultSettings: UserSettings = {
  systemPrompt: "",
  username: "",
  rolename: "",
  referToSavedMemories: true,
  referToHistoricalChatRecords: true,
  userMemories: [],
};

export const UserStore = {
  /**
   * 获取用户设置
   */
  getSettings(): UserSettings {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...defaultSettings, ...parsed };
      }
    } catch (error) {
      console.error("Failed to parse user settings from localStorage:", error);
    }
    return { ...defaultSettings };
  },

  /**
   * 保存用户设置
   */
  saveSettings(settings: UserSettings): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error("Failed to save user settings to localStorage:", error);
    }
  },

  /**
   * 更新系统提示词
   */
  updateSystemPrompt(prompt: string): void {
    const settings = this.getSettings();
    settings.systemPrompt = prompt;
    this.saveSettings(settings);
  },

  /**
   * 更新用户名
   */
  updateUsername(username: string): void {
    const settings = this.getSettings();
    settings.username = username;
    this.saveSettings(settings);
    Cookies.set("resourceId", username);
  },
  /**
   * 更新用户名
   */
  updateRolename(rolename: string): void {
    const settings = this.getSettings();
    settings.rolename = rolename;
    this.saveSettings(settings);
  },

  /**
   * 更新记忆设置
   */
  updateMemorySettings(
    referToSavedMemories: boolean,
    referToHistoricalChatRecords: boolean
  ): void {
    const settings = this.getSettings();
    settings.referToSavedMemories = referToSavedMemories;
    settings.referToHistoricalChatRecords = referToHistoricalChatRecords;
    this.saveSettings(settings);
  },

  /**
   * 添加用户记忆
   */
  addUserMemory(memory: string): { success: boolean; message: string } {
    const settings = this.getSettings();
    if (!settings.userMemories.includes(memory)) {
      settings.userMemories.push(memory);
      this.saveSettings(settings);
      return { success: true, message: "Memory addition successful" };
    } else {
      return { success: false, message: "Memory already exists" };
    }
  },

  /**
   * 删除用户记忆
   */
  removeUserMemory(memory: string): void {
    const settings = this.getSettings();
    settings.userMemories = settings.userMemories.filter((m) => m !== memory);
    this.saveSettings(settings);
  },

  /**
   * 清除所有用户记忆
   */
  clearAllUserMemories(): void {
    const settings = this.getSettings();
    settings.userMemories = [];
    this.saveSettings(settings);
  },

  /**
   * 清除所有用户数据
   */
  clearAllData(): void {
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error("Failed to clear user data from localStorage:", error);
    }
  },
};
