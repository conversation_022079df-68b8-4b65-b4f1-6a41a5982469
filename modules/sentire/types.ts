import { z } from "zod";
import type { Message } from "~/types/message";
import type { ClueWithData, LinkId } from "~/types/sentire/clue";

export type UserInput = {
  question: string;
  entities: { type: string; label: string }[];
};

export type Plan = {
  id: number;
  title: string;
  status: "pending" | "running" | "completed";
  isNewAdded?: boolean;
  isRemoved?: boolean;
};

export type CheckingStatus = "idle" | "checking" | "done";
export type CheckingResult = "error" | "success" | "warning" | "unknown" | null;

export type NetworkLink = {
  expanded: boolean;
  selected: boolean;
  status: CheckingStatus;
  result: CheckingResult;
  selectedPackIds?: string[];
  clues: CluesType[];
};

export type CluesType = {
  id: number;
  clueId: string;
  threadId: string;
  packId: string;
  status: CheckingStatus;
  result: CheckingResult;
  clue?: ClueWithData;
};

export type NetworkLinks = Record<LinkId, NetworkLink>;

export type HistoryAnalysis = {
  userInput: UserInput;
  messages: Message[];
  summaryTitle: string;
};

export const SuggestionSchema = z.object({
  content: z
    .string()
    .describe(
      "A concise suggestion for the user to investigate next. if `type` is link, only return the link url."
    ),
  type: z.enum(["text", "link"]),
  link: z.string().optional().describe("The link url if `type` is link."),
});

export const suggestionsSchema = z
  .array(SuggestionSchema)
  .describe("A list of suggestions for the user to investigate next.");

export type Suggestions = z.infer<typeof suggestionsSchema>;
export type Suggestion = z.infer<typeof SuggestionSchema>;

export type Metadata = {
  entity_type: string;
  name: string;
  tags: string;
};
