import { MastraClient } from "@mastra/client-js";
import { DEFAULT_AGENT_URL, DIAGNOSTIC_AGENT } from "../const";

export function hiddenElementId(text: string) {
  return `${text + " hidden"}`;
}

export function getMastraClient(abortSignal?: AbortSignal) {
  const config = useRuntimeConfig();
  const client = new MastraClient({
    baseUrl:
      typeof window === "undefined"
        ? config.public.agentUrl
        : DEFAULT_AGENT_URL,
    abortSignal,
  });
  return client;
}

export function getDiagnosticAgent() {
  const client = getMastraClient();
  return client.getAgent(DIAGNOSTIC_AGENT);
}

export function getSuggestionAgent() {
  const client = getMastraClient();
  return client.getAgent("suggestionAgent");
}
