import type { MastraMessageV1 } from "@mastra/core";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import type { Message, TextMessage } from "~/types/message";
import type { HistoryAnalysis } from "../types";

export function extractAndRemoveSummary(text: string) {
  const summaryMatch = text.match(/<summary>([\s\S]*?)<\/summary>/i);
  const summaryContent = summaryMatch ? summaryMatch[1] : "";
  const textWithoutSummary = text.replace(/<summary>[\s\S]*?<\/summary>/gi, "");
  return { summaryContent, textWithoutSummary };
}

export const clientMessageToHistoryAnalysis = (messages: Message[]) => {
  let lastIndex = 0;
  const _historyAnalyses: Record<string, HistoryAnalysis> = {};
  for (const [index, message] of messages.entries()) {
    if (message.role === "user") {
      const content = message.content;
      let question = "";
      if (typeof content === "string") {
        question = content;
      } else if (Array.isArray(content)) {
        question = content[0]?.type === "text" ? content[0].text : "";
      }
      _historyAnalyses[index] = {
        userInput: {
          question,
          entities: [],
        },
        messages: [],
        summaryTitle: "",
      };
      _historyAnalyses[index].messages.push(message);
      lastIndex = index;
    } else {
      if (Array.isArray(message.content)) {
        try {
          _historyAnalyses[lastIndex].messages.push(message);
        } catch (e) {
          console.error(e);
        }
      } else {
        console.warn("unknown message content", message.content);
      }
    }
  }

  return Object.values(_historyAnalyses).map((analysis) => {
    let summaryContent = "";
    for (const message of analysis.messages) {
      if (message.role === "assistant") {
        for (const content of message.content) {
          if (
            typeof content === "object" &&
            content.type === "tool-call" &&
            content.toolName === "summaryTool"
          ) {
            summaryContent =
              ((content.args as unknown as Record<string, unknown>)
                ?.title as string) ?? "";
          }
        }
      }
    }
    return {
      ...analysis,
      summaryTitle: summaryContent,
    };
  });
};

export const toMastraMessageV1 = (
  messages: Message[],
  threadId: string,
  resourceId?: string
): MastraMessageV1[] => {
  const _messages: MastraMessageV1[] = [];
  const startDateTime = dayjs().subtract(1, "hour");
  for (const [msgIndex, msg] of messages.entries()) {
    if (!Array.isArray(msg.content)) {
      console.warn("message content is not an array, skip", msg.content);
      continue;
    }
    for (const [contentIndex, content] of msg.content.entries()) {
      const index = msgIndex * 10 + contentIndex;
      if (msg.role === "user") {
        _messages.push({
          id: uuidv4(),
          threadId,
          resourceId: resourceId,
          role: msg.role,
          createdAt: startDateTime.add(index, "second").toDate(),
          type: (content as TextMessage).type,
          content: (content as TextMessage).text,
        });
      } else if (msg.role === "assistant") {
        if (content.type === "text") {
          _messages.push({
            id: uuidv4(),
            threadId,
            resourceId: resourceId,
            role: msg.role,
            createdAt: startDateTime.add(index, "second").toDate(),
            type: (content as TextMessage).type,
            content: [content],
          });
        } else if (content.type === "tool-call") {
          _messages.push({
            id: uuidv4(),
            threadId,
            resourceId: resourceId,
            role: msg.role,
            createdAt: startDateTime.add(index, "second").toDate(),
            type: "tool-call",
            content: [content],
          });

          if (content.result) {
            _messages.push({
              id: uuidv4(),
              threadId,
              resourceId: resourceId,
              role: "tool",
              createdAt: startDateTime.add(index, "second").toDate(),
              type: "tool-result",
              content: [
                {
                  toolCallId: content.toolCallId,
                  toolName: content.toolName,
                  result: content.result,
                  type: "tool-result",
                },
              ],
            });
          }
        }
      }
    }
  }

  return _messages;
};
