import type { StorageThreadType } from "@mastra/core";
import { toast } from "vue-sonner";
import type { Message } from "~/types/message";
import { DEFAULT_CUSTOM_API_ENDPOINT, DIAGNOSTIC_AGENT } from "../const";
import { getMastraClient } from "../helpers";
import { toMastraMessageV1 } from "../helpers/message";

export class Threads {
  async listThreads(
    agentId: string,
    resourceId: string,
    keyword?: string[],
    signal?: AbortSignal
  ) {
    const { threads } = await $fetch<{
      threads: StorageThreadType[];
      keyword: string[];
      total: number;
    }>(`/threads`, {
      baseURL: DEFAULT_CUSTOM_API_ENDPOINT,
      method: "GET",
      query: {
        keyword: keyword?.filter((k) => k.trim() !== ""),
        resourceId,
        agentId,
      },
      signal,
    });
    return threads;
  }

  async createThread(threadId?: string, topic?: string, resourceId?: string) {
    const client = getMastraClient();
    const thread = await client.createMemoryThread({
      agentId: DIAGNOSTIC_AGENT,
      resourceId: resourceId ?? "",
      threadId,
      title: topic,
      metadata: topic
        ? {
            topic: topic,
            agentId: DIAGNOSTIC_AGENT,
          }
        : {
            agentId: DIAGNOSTIC_AGENT,
          },
    });
    return thread;
  }

  async updateThreadTopic(threadId: string, topic: string) {
    const client = getMastraClient();
    const thread = await client.getMemoryThread(threadId, DIAGNOSTIC_AGENT);
    if (!thread) {
      toast.error("Error", {
        description: "Thread not found",
      });
      return;
    }
    try {
      const threadData = await thread.get();
      if (!threadData.metadata?.["topic"]) {
        await thread.update({
          title: threadData.title ?? topic,
          resourceId: threadData.resourceId,
          metadata: {
            ...(threadData.metadata ?? {}),
            topic: topic,
          },
        });
      }
    } catch (e) {
      toast.error("Error", {
        description: `Failed to update thread title: ${e}`,
      });
    }
  }

  async deleteThread(threadId: string) {
    const client = getMastraClient();
    const thread = await client.getMemoryThread(threadId, DIAGNOSTIC_AGENT);
    if (!thread) {
      toast.error("Error", {
        description: "Thread not found",
      });
      return;
    }
    try {
      await thread.delete();
    } catch (e: unknown) {
      toast.error("Error", {
        description: `Failed to delete thread: ${e}`,
      });
    }
  }

  async saveMessages(
    threadId: string,
    messages: Message[],
    resourceId?: string
  ) {
    const client = getMastraClient();
    await client.saveMessageToMemory({
      agentId: DIAGNOSTIC_AGENT,
      messages: toMastraMessageV1(messages, threadId, resourceId),
    });
  }

  async reCreateThread(
    threadId: string,
    messages: Message[],
    resourceId?: string
  ) {
    if (messages.length > 0 && messages[0].role !== "user") {
      console.warn(
        "The first message is not a user message, skip re-creating thread"
      );
      return;
    }

    if (messages.length > 0 && !Array.isArray(messages[0].content)) {
      console.warn(
        "The first message content is not an array, skip re-creating thread"
      );
      return;
    }

    const mastraMessages = toMastraMessageV1(messages, threadId, resourceId);
    console.log(mastraMessages);

    await this.deleteThread(threadId);
    const topic =
      messages[0]?.content[0]?.type === "text"
        ? messages[0]?.content[0]?.text
        : undefined;
    const thread = await this.createThread(threadId, topic, resourceId);
    await this.saveMessages(threadId, messages, resourceId);
    return thread;
  }
}
