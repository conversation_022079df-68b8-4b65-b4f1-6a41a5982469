import {
  BehaviorSubject,
  Subject,
  merge,
  of,
  scan,
  shareReplay,
  startWith,
  switchMap,
  takeUntil,
  tap,
  timer,
  type Subscription,
} from "rxjs";
import { computed, onUnmounted, readonly, ref } from "vue";
import { toast } from "vue-sonner";
import type { StreamEvent, StreamToolCallEvent } from "~/hooks/agent/types";
import { useMessageStreamProcessor } from "~/hooks/agent/useMessageProcessor";
import type { TextMessage, ToolCallMessage } from "~/types/message";
import type { Plan } from "../types";

const AGENT_TEXT_TIMEOUT_MS = 30000;
const AGENT_TOOL_TIMEOUT_MS = 120000;

export type PlanStepWithMessages = Plan & {
  messages: Array<TextMessage | ToolCallMessage>;
};

export interface AnalysisState {
  plans: Plan[];
  steps: PlanStepWithMessages[];
  currentState: {
    planId: number;
  };
  summary: string;
  finalContent: Array<TextMessage | ToolCallMessage>;
  phase: "idle" | "planning" | "content-generation" | "done";
  status: "idle" | "pending" | "analyzing" | "done" | "canceled";
  extraContent?: Array<TextMessage | ToolCallMessage>;
}

type PlanToolArgs = {
  plans: Plan[];
  currentState: {
    planId: number;
  };
};

type SummaryToolArgs = {
  title: string;
};

const isPlanExecutionStateChanged = (
  currentState: AnalysisState["currentState"],
  newState: AnalysisState["currentState"]
) => {
  return currentState.planId !== newState.planId;
};

const isPlanExecuting = (state: AnalysisState["currentState"]) => {
  return state.planId !== -1;
};

export interface StreamProcessorOptions {
  onAbort?: (reason: string) => void;
  initialState?: Partial<AnalysisState>;
}

export const useStreamProcessor = (options: StreamProcessorOptions = {}) => {
  const { onAbort, initialState } = options;

  const destroy$ = new Subject<void>();
  const messageProcessor = useMessageStreamProcessor();
  let analysisState$ = new BehaviorSubject<AnalysisState>({
    steps: [],
    plans: [],
    currentState: {
      planId: -1,
    },
    summary: "",
    finalContent: [],
    phase: "idle",
    status: "idle",
    extraContent: [],
    ...initialState,
  });

  const getTimeoutConfig = (eventType: string) => {
    switch (eventType) {
      case "text":
        return { duration: AGENT_TEXT_TIMEOUT_MS, type: "text" as const };
      case "tool-call":
        return { duration: AGENT_TOOL_TIMEOUT_MS, type: "tool" as const };
      default:
        return null;
    }
  };

  const createStreamPipeline = () => {
    const baseEvents$ = messageProcessor.getStreamEvents();
    return baseEvents$.pipe(
      switchMap((event) => {
        const currentEvent$ = of(event);
        const timeoutConfig = getTimeoutConfig(event.type);

        if (!timeoutConfig) {
          return currentEvent$;
        }

        const timeoutEvent$ = timer(timeoutConfig.duration).pipe(
          switchMap(() =>
            of({
              type: "timeout" as const,
              payload: {
                timeoutType: timeoutConfig.type,
                error: `${timeoutConfig.type} operation timeout after ${timeoutConfig.duration}ms`,
              },
              timestamp: Date.now(),
            })
          ),
          takeUntil(
            baseEvents$.pipe(
              startWith(null) // 当有新事件时取消超时
            )
          )
        );

        return merge(currentEvent$, timeoutEvent$);
      }),
      takeUntil(destroy$),
      shareReplay(1)
    );
  };

  let streamEventsWithTimeout$ = createStreamPipeline();

  const handleTextEvent = (
    state: AnalysisState,
    event: StreamEvent<unknown>
  ): AnalysisState => {
    if (event.type !== "text") {
      return state;
    }
    const text = event.payload.text || "";
    // 如果正在执行计划，text 会添加到当前步骤的 messages 中
    if (isPlanExecuting(state.currentState)) {
      const currentStep = state.steps.find(
        (step) => step.id === state.currentState.planId
      );
      if (!currentStep) {
        console.warn(
          `current step not found, currentState: ${JSON.stringify(
            state.currentState
          )}`
        );
        return state;
      }

      const lastMessage = currentStep.messages[currentStep.messages.length - 1];
      if (lastMessage && lastMessage.type === "text") {
        lastMessage.text += text;
      } else {
        currentStep?.messages.push({ type: "text", text });
      }
    } else {
      if (state.phase !== "planning" && state.phase !== "content-generation") {
        state.extraContent = state.extraContent || [];
        state.extraContent.push({ type: "text", text });
      } else if (state.phase === "content-generation") {
        const lastContent = state.finalContent[state.finalContent.length - 1];
        if (lastContent && lastContent.type === "text") {
          lastContent.text += text;
        } else {
          state.finalContent.push({ type: "text", text });
        }
        return state;
      }
    }
    return state;
  };

  const handleToolCallEvent = (
    state: AnalysisState,
    event: StreamEvent<unknown>
  ): AnalysisState => {
    if (event.type !== "tool-call") {
      return state;
    }

    const { toolCallId, toolName } = event.payload;

    if (!toolCallId || !toolName) {
      return state;
    }

    if (toolName === "planTool") {
      state.extraContent = [];
      state.phase = "planning";
      const args = event.payload
        .args as unknown as StreamToolCallEvent<PlanToolArgs>["payload"]["args"];
      const plans = args.plans ?? [];

      if (isPlanExecutionStateChanged(state.currentState, args.currentState)) {
        state.currentState = args.currentState;
        const currentPlan = plans.find(
          (plan) => plan.id === args.currentState.planId
        );
        if (!currentPlan) {
          console.warn(
            `current plan not found, currentState: ${JSON.stringify(
              state.currentState
            )}`
          );
          return state;
        }

        const newStep: PlanStepWithMessages = {
          ...currentPlan,
          messages: [],
        };

        for (const step of state.steps) {
          step.status = "completed";
        }
        state.steps.push(newStep);
        state.plans = plans;
      }
    } else if (toolName === "summaryTool") {
      state.extraContent = [];
      state.phase = "content-generation";
      state.currentState = {
        planId: -1,
      };
      const args = event.payload
        .args as unknown as StreamToolCallEvent<SummaryToolArgs>["payload"]["args"];
      state.summary = args.title || "";
    } else {
      if (isPlanExecuting(state.currentState)) {
        const currentStep = state.steps.find(
          (step) => step.id === state.currentState.planId
        );
        if (!currentStep) {
          console.warn(
            `current step not found, currentState: ${JSON.stringify(
              state.currentState
            )}`
          );
          return state;
        }

        const args = (event.payload.args || {}) as Record<string, unknown>;

        const lastMessage =
          currentStep.messages[currentStep.messages.length - 1];
        if (
          lastMessage &&
          lastMessage.type === "tool-call" &&
          lastMessage.toolCallId === toolCallId
        ) {
          lastMessage.args = args;
        } else {
          currentStep.messages.push({
            type: "tool-call",
            toolCallId,
            toolName,
            args: args || {},
          });
        }
      } else {
        const args = (event.payload.args || {}) as Record<string, unknown>;
        if (
          state.phase !== "planning" &&
          state.phase !== "content-generation"
        ) {
          state.extraContent = state.extraContent || [];
          state.extraContent.push({
            type: "tool-call",
            toolCallId,
            toolName,
            args: args || {},
          });
        } else {
          const lastMessage = state.finalContent[state.finalContent.length - 1];
          if (
            lastMessage &&
            lastMessage.type === "tool-call" &&
            lastMessage.toolCallId === toolCallId
          ) {
            lastMessage.args = args;
          } else {
            state.finalContent.push({
              type: "tool-call",
              toolCallId,
              toolName,
              args: args || {},
            });
          }
        }
      }
    }

    return state;
  };

  const handleToolResultEvent = (
    state: AnalysisState,
    event: StreamEvent<unknown>
  ): AnalysisState => {
    if (event.type !== "tool-result") {
      return state;
    }
    const { toolCallId, result } = event.payload;

    if (!toolCallId) {
      return state;
    }

    if (isPlanExecuting(state.currentState)) {
      const currentStep = state.steps.find(
        (step) => step.id === state.currentState.planId
      );
      if (!currentStep) {
        console.warn(
          `current step not found, currentState: ${JSON.stringify(
            state.currentState
          )}`
        );
        return state;
      }

      for (let i = state.steps.length - 1; i >= 0; i--) {
        const stepMessages = state.steps[i].messages;
        for (let j = stepMessages.length - 1; j >= 0; j--) {
          const message = stepMessages[j];
          if (
            message.type === "tool-call" &&
            message.toolCallId === toolCallId &&
            !message.result
          ) {
            message.result = result as Record<string, unknown> | undefined;
            return state;
          }
        }
      }
    } else if (toolCallId.includes("summaryTool")) {
      return state;
    } else {
      for (const content of state.finalContent) {
        if (content.type === "tool-call" && content.toolCallId === toolCallId) {
          content.result = result as Record<string, unknown> | undefined;
          break;
        }
      }
    }

    return state;
  };

  const handleTimeoutEvent = (
    state: AnalysisState,
    event: StreamEvent<unknown>
  ): AnalysisState => {
    if (event.type !== "timeout") {
      return state;
    }

    const { timeoutType, error } = event.payload;

    console.warn(`${timeoutType} timeout occurred:`, error);

    if (onAbort) {
      onAbort(error || `${timeoutType} timeout`);
    }

    state.status = "done";
    state.phase = "done";

    toast.error("Request timeout", {
      description:
        error || `${timeoutType} operation timeout, please try again`,
    });

    return state;
  };

  const handleStreamFinishEvent = (state: AnalysisState) => {
    // 如果没有 summaryTool 也没有 planTool
    if (state.extraContent?.length) {
      state.phase = "content-generation";
      state.currentState = {
        planId: -1,
      };
      // 把 extraContent 合并到 finalContent 并清空
      if (state.extraContent && state.extraContent.length > 0) {
        state.finalContent.push(...state.extraContent);
        state.extraContent = [];
      }
    } else {
      state.extraContent = [];
    }
  };

  const createAnalysisStatePipeline = (newState?: Partial<AnalysisState>) => {
    analysisState$.complete();
    analysisState$ = new BehaviorSubject<AnalysisState>({
      steps: [],
      plans: [],
      currentState: {
        planId: -1,
      },
      summary: "",
      finalContent: [],
      phase: "idle",
      status: "idle",
      ...newState,
    });

    stateSubscription?.unsubscribe();
    stateSubscription = analysisState$.subscribe((state) => {
      steps.value = state.steps.map((step) => {
        return {
          ...step,
          messages: step.messages.map((message) => ({ ...message })),
        };
      });
      plans.value = state.plans.map((plan) => ({ ...plan }));
      summary.value = state.summary;
      finalContent.value = [...state.finalContent];
      analysisStatus.value = state.status;
      analysisPhase.value = state.phase;
    });

    return analysisState$;
  };

  const createProcessedStatePipeline = () => {
    return streamEventsWithTimeout$.pipe(
      scan((state: AnalysisState, event: StreamEvent<unknown>) => {
        const newState = { ...state };

        switch (event.type) {
          case "text":
            return handleTextEvent(newState, event);
          case "tool-call":
            return handleToolCallEvent(newState, event);
          case "tool-result":
            return handleToolResultEvent(newState, event);
          case "step-start":
            newState.status = "analyzing";
            break;
          case "step-finish": {
            break;
          }
          case "stream-finish":
            handleStreamFinishEvent(newState);
            break;
          case "timeout":
            return handleTimeoutEvent(newState, event);
          case "error":
            newState.status = "done";
            newState.phase = "done";
            break;
        }

        return newState;
      }, analysisState$.value),
      tap((state) => analysisState$.next(state)),
      shareReplay(1),
      takeUntil(destroy$)
    );
  };

  let processedState$ = createProcessedStatePipeline();
  let processedStateSubscription = processedState$.subscribe();

  const steps = ref<PlanStepWithMessages[]>([]);
  const plans = ref<Plan[]>([]);
  const summary = ref<string>("");
  const finalContent = ref<Array<TextMessage | ToolCallMessage>>([]);
  const analysisStatus = ref<AnalysisState["status"]>("idle");
  const analysisPhase = ref<AnalysisState["phase"]>("idle");
  let stateSubscription: Subscription;

  createAnalysisStatePipeline(initialState);

  const isAnalyzing = computed(() => {
    return analysisStatus.value === "analyzing";
  });

  const isPending = computed(() => {
    return analysisStatus.value === "pending";
  });

  const isDone = computed(() => {
    return analysisStatus.value === "done";
  });

  const isCanceled = computed(() => {
    return analysisStatus.value === "canceled";
  });

  // Phase-related computed properties
  const isPlanningPhase = computed(() => {
    return analysisPhase.value === "planning";
  });

  const isContentGenerationPhase = computed(() => {
    return analysisPhase.value === "content-generation";
  });

  const isPhaseCompleted = computed(() => {
    return analysisPhase.value === "done";
  });

  const emitEvent = (event: StreamEvent<unknown>) => {
    messageProcessor.emitEvent(event);
  };

  const setState = (state: Partial<AnalysisState>) => {
    const currentState = analysisState$.value;
    analysisState$.next({ ...currentState, ...state });
  };

  const reset = (newState?: Partial<AnalysisState>) => {
    messageProcessor.reset(undefined, { resetMessages: true });

    processedStateSubscription.unsubscribe();

    createAnalysisStatePipeline(newState);

    streamEventsWithTimeout$ = createStreamPipeline();
    processedState$ = createProcessedStatePipeline();
    processedStateSubscription = processedState$.subscribe();
  };

  const cleanup = () => {
    destroy$.next();
    destroy$.complete();
    messageProcessor.cleanup();
    stateSubscription?.unsubscribe();
    processedStateSubscription.unsubscribe();
  };

  onUnmounted(() => {
    cleanup();
  });

  return {
    // state
    steps: readonly(steps),
    plans: readonly(plans),
    summary: readonly(summary),
    finalContent: readonly(finalContent),
    analysisStatus: readonly(analysisStatus),
    analysisPhase: readonly(analysisPhase),
    messages: readonly(messageProcessor.messages),

    // status computed properties
    isAnalyzing,
    isPending,
    isDone,
    isCanceled,

    // phase computed properties
    isPlanningPhase,
    isContentGenerationPhase,
    isPhaseCompleted,

    // stream object
    analysisState$: analysisState$.asObservable(),
    streamEvents$: messageProcessor.getStreamEvents(),

    // actions
    emitEvent,
    setState,
    reset,
    cleanup,
    getStreamEvents: () => messageProcessor.getStreamEvents(),
  };
};
