import { DEFAULT_CUSTOM_API_ENDPOINT } from "../const";

export async function useArtifactReport(reportId: string) {
  const { data: report } = await useAsyncData("report", () =>
    $fetch<{ content: string; title: string }>(
      `${DEFAULT_CUSTOM_API_ENDPOINT}/artifact/${reportId}`
    )
  );

  const htmlContent = computed(() => {
    const customCSS = `
        * {
          scrollbar-width: thin;
          scrollbar-color: #8b8b8b transparent;
        }
  
        *::-webkit-scrollbar {
          width: 8px;
        }
  
        *::-webkit-scrollbar-thumb {
          background: #8b8b8b;
          border-radius: 6px;
          border: 2px solid transparent;
          background-clip: padding-box;
        }
  
        *::-webkit-scrollbar-thumb:hover {
          background: #767676;
        }
  
        *::-webkit-scrollbar-track {
          background: transparent;
        }
      `;

    let htmlContent = report.value?.content || "";

    if (htmlContent) {
      // Check if there's already a <style> tag
      const styleRegex = /<style[^>]*>/i;
      const existingStyleMatch = htmlContent.match(styleRegex);

      if (existingStyleMatch) {
        // If <style> exists, append our CSS to the first <style> tag
        const styleEndRegex = /<\/style>/i;
        htmlContent = htmlContent.replace(
          styleEndRegex,
          `\n${customCSS}\n</style>`
        );
      } else {
        // If no <style> exists, add our CSS in a new <style> tag within <head>
        htmlContent = htmlContent.replace(
          /<head[^>]*>/i,
          `<head>\n  <style>${customCSS}</style>`
        );
      }
    }

    return htmlContent;
  });

  return {
    title: report.value?.title,
    content: htmlContent,
  };
}
