import { ref } from "vue";
import { convertMessagesToStreamEvents } from "~/hooks/agent/message";
import type { Message, TextMessage, ToolCallMessage } from "~/types/message";
import {
  useStreamProcessor,
  type PlanStepWithMessages,
} from "./useStreamProcessor";

export interface HistoryData {
  steps: PlanStepWithMessages[];
  summary: string;
  finalContent: Array<TextMessage | ToolCallMessage>;
}

export const useHistoryProcessor = () => {
  const streamProcessor = useStreamProcessor();
  const isProcessing = ref(false);

  const processHistoryMessages = (messages: Message[]): HistoryData => {
    streamProcessor.reset();

    const events = convertMessagesToStreamEvents(toRaw(messages), "user");

    events.forEach((event) => {
      streamProcessor.emitEvent(event);
    });

    return {
      steps: streamProcessor.steps.value.map((step) => ({
        ...step,
        messages: [...step.messages],
      })),
      summary: streamProcessor.summary.value,
      finalContent: [...streamProcessor.finalContent.value],
    };
  };

  return {
    // state
    isProcessing,
    steps: streamProcessor.steps,
    plans: streamProcessor.plans,
    summary: streamProcessor.summary,
    finalContent: streamProcessor.finalContent,
    analysisStatus: streamProcessor.analysisStatus,

    // actions
    processHistoryMessages,
    reset: streamProcessor.reset,
    cleanup: streamProcessor.cleanup,
  };
};
