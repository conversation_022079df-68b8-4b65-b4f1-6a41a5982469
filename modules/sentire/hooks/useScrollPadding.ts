import { useCurrentThreadStore } from "../stores";

export function useScrollPadding() {
  const scrollPaddingRef = ref(0);
  const currentThreadStore = useCurrentThreadStore();
  const { plans, historyAnalyses } = storeToRefs(currentThreadStore);
  const updateScrollPadding = async () => {
    await nextTick();
    const el = document.querySelector("#bottom-container") as HTMLDivElement;
    if (el) {
      if (el.offsetHeight > 212) {
        scrollPaddingRef.value = el.offsetHeight + 64;
      } else {
        scrollPaddingRef.value = el.offsetHeight + 64;
      }
    } else {
      scrollPaddingRef.value = 202;
    }
  };

  watch(plans, async (newVal, oldVal) => {
    if (newVal.length !== oldVal?.length) {
      await nextTick();
      updateScrollPadding();
    }
  });

  watch(historyAnalyses, async (newVal, oldVal) => {
    if (newVal && newVal?.length !== oldVal?.length && newVal.length > 0) {
      await nextTick();
      updateScrollPadding();
    }
  });

  return {
    scrollPaddingRef,
    updateScrollPadding,
  };
}
