import { scrollToSlugifiedElement } from "@/utils/dom";
import { nextTick, onMounted, onUnmounted, ref, watch } from "vue";

export function useAutoScroll(
  scrollContainerRef: Ref<HTMLElement | undefined>,
  isRunning: Ref<boolean>,
  isContentGenerationPhase: Ref<boolean>
) {
  const isAutoScrollEnabled = ref(false);
  const isProgrammaticScrolling = ref(false);
  const userHasScrolled = ref(false);
  const isAtBottom = ref(false);

  let scrollToBottomId: number | null = null;
  let userScrollCheckId: number | null = null;

  const checkIfAtBottom = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;
    isAtBottom.value = scrollTop + windowHeight >= documentHeight - 100;
  };

  const _scrollToBottom = (behavior: "smooth" | "instant" = "smooth") => {
    if (scrollToBottomId) {
      cancelAnimationFrame(scrollToBottomId);
    }

    scrollToBottomId = requestAnimationFrame(() => {
      window.scrollTo({
        top: document.documentElement.scrollHeight,
        behavior: behavior,
      });
    });
  };

  const handleAutoScroll = () => {
    if (!isAutoScrollEnabled.value || !scrollContainerRef.value) {
      return;
    }

    isProgrammaticScrolling.value = true;
    _scrollToBottom();
  };

  const handleScroll = async () => {
    if (isProgrammaticScrolling.value) {
      //console.log("is programmatic scroll, ignore handleScroll");
      return;
    }

    await nextTick();
    if (userScrollCheckId) {
      cancelAnimationFrame(userScrollCheckId);
    }

    userScrollCheckId = requestAnimationFrame(() => {
      checkIfAtBottom();

      if (isAutoScrollEnabled.value) {
        userHasScrolled.value = true;
        isAutoScrollEnabled.value = false;
        //console.log("user scroll detected, disable auto scroll");
      }

      if (isAtBottom.value && isRunning.value && !isAutoScrollEnabled.value) {
        //console.log("user scroll to bottom, enable auto scroll");
        userHasScrolled.value = false;
        isAutoScrollEnabled.value = true;
      }
    });
  };

  const handleScrollEnd = () => {
    if (isProgrammaticScrolling.value) {
      //console.log(">>> scroll end, reset programmatic scrolling");
      isProgrammaticScrolling.value = false;
    }
  };

  watch(
    () => isContentGenerationPhase.value,
    async (newStage) => {
      await nextTick();

      if (!newStage) {
        if (!userHasScrolled.value) {
          isAutoScrollEnabled.value = true;
          //console.log(">>>> enter understanding phase, enable auto scroll");
          handleAutoScroll();
        }
      } else if (newStage) {
        isAutoScrollEnabled.value = false;
        if (!userHasScrolled.value) {
          //console.log(">>>> enter deep analysis phase, disable auto scroll");
          isProgrammaticScrolling.value = true;
          scrollToSlugifiedElement("deep analysis block", "smooth");
        }
      }
    },
    { immediate: true }
  );

  const triggerScrollToBottom = (smooth: "smooth" | "instant" = "smooth") => {
    isAutoScrollEnabled.value = true;
    userHasScrolled.value = false;
    isProgrammaticScrolling.value = true;
    _scrollToBottom(smooth);
  };

  const resetAutoScroll = () => {
    isAutoScrollEnabled.value = false;
    userHasScrolled.value = false;
    isAtBottom.value = false;
    isProgrammaticScrolling.value = false;
  };

  let mutationObserver: MutationObserver | null = null;
  let resizeObserver: ResizeObserver | null = null;

  const startModernObservers = () => {
    if (!import.meta.client || !scrollContainerRef.value) return;

    try {
      resizeObserver = new ResizeObserver(() => {
        if (!userHasScrolled.value && isAutoScrollEnabled.value) {
          if (isRunning.value) {
            handleAutoScroll();
          }
        }
      });

      resizeObserver.observe(scrollContainerRef.value);
    } catch (error) {
      console.warn("ResizeObserver not supported:", error);
    }
  };

  const stopModernObservers = () => {
    if (resizeObserver) {
      resizeObserver.disconnect();
      resizeObserver = null;
    }

    if (mutationObserver) {
      mutationObserver.disconnect();
      mutationObserver = null;
    }
  };

  onMounted(() => {
    window.addEventListener("scroll", handleScroll, { passive: true });
    window.addEventListener("scrollend", handleScrollEnd);

    checkIfAtBottom();
    startModernObservers();
  });

  onUnmounted(() => {
    window.removeEventListener("scroll", handleScroll);
    window.removeEventListener("scrollend", handleScrollEnd);
    stopModernObservers();

    if (scrollToBottomId) {
      cancelAnimationFrame(scrollToBottomId);
    }

    if (userScrollCheckId) {
      cancelAnimationFrame(userScrollCheckId);
    }
  });

  return {
    isAutoScrollEnabled: readonly(isAutoScrollEnabled),
    triggerScrollToBottom,
    resetAutoScroll,
  };
}
