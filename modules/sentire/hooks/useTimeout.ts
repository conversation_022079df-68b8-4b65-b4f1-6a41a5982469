import { toast } from "vue-sonner";

export function useTimeout() {
  const resetTimeout = (
    ref: ReturnType<typeof setTimeout> | null,
    ms: number,
    onTimeout: () => void
  ) => {
    if (ref !== null) clearTimeout(ref);
    return setTimeout(onTimeout, ms);
  };

  const cancelTimeout = (ref: ReturnType<typeof setTimeout> | null) => {
    if (ref !== null) {
      clearTimeout(ref);
    }
    return null;
  };

  const handleTimeout = (
    type: "text" | "tool",
    timeoutMs: number,
    analysisStatusRef: { value: string },
    abortController?: AbortController | null,
    toolCallId?: string
  ) => {
    analysisStatusRef.value = "done";
    const reason =
      type === "text"
        ? `Connection timeout: No data received for over ${
            timeoutMs / 1000
          } seconds.`
        : `Connection timeout: toolCallId=${toolCallId} no result received for over ${
            timeoutMs / 1000
          } seconds.`;
    if (abortController) abortController.abort(reason);
    toast.error(reason);
  };

  return {
    resetTimeout,
    cancelTimeout,
    handleTimeout,
  };
}
