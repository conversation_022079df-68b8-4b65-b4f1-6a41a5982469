<script setup lang="ts">
import type { ComponentPublicInstance } from "vue";
import { useCurrentThreadStore } from "../stores/currentThreadStore";
import CluePack from "./CluePack.vue";
import type { Clue, LinkId, PackId } from "~/types/sentire/clue";
import { sortBy } from "lodash-es";
import NetworkLink from "./NetworkLink.vue";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-vue-next";
import { DEFAULT_CUSTOM_API_ENDPOINT } from "../const";

const currentThreadStore = useCurrentThreadStore();
const { networkLinks, steps } = storeToRefs(currentThreadStore);

const showStickyContainer = ref(false);

const networkLinkRefs = ref<
  Record<string, HTMLElement | ComponentPublicInstance>
>({});

const originalContainer = ref<HTMLElement | null>(null);
const cluePanelContainer = ref<HTMLElement | null>(null);
const stickyStyle = ref({ left: "0px", width: "100%" });

const haveNextSteps = ref(false);
const autoSelectLink = ref(false);

const setNetworkLinkRef = (
  el: HTMLElement | ComponentPublicInstance | null,
  linkId: string
) => {
  if (el) {
    networkLinkRefs.value[linkId] = el;
  }
};

let throttleTimer: number | null = null;
const props = defineProps<{
  stepIndex: number;
}>();

const handleNetworkLinkPosition = () => {
  if (throttleTimer) return;

  throttleTimer = window.setTimeout(() => {
    if (originalContainer.value) {
      const containerRect = originalContainer.value.getBoundingClientRect();
      const shouldShow = containerRect.top - 64 <= 0;

      if (shouldShow !== showStickyContainer.value) {
        showStickyContainer.value = shouldShow;
      }
      // 判断 cluePanelContainer 的底部距离
      if (cluePanelContainer.value) {
        const cluePanelRect = cluePanelContainer.value.getBoundingClientRect();
        if (cluePanelRect.bottom - containerRect.height - 64 <= 0) {
          showStickyContainer.value = false;
        }
      }
      stickyStyle.value = {
        left: containerRect.left + "px",
        width: containerRect.width + "px",
      };
    }
    throttleTimer = null;
  }, 16);
};

watch(
  () => steps.value.length,
  (newLen) => {
    if (props.stepIndex < newLen) {
      haveNextSteps.value = true;
    }
  }
);

const emit = defineEmits(["clues-loaded", "tool-height-change"]);

const { data: clues } = useAsyncData("getClues", async () => {
  const maxRetries = 3;
  const delays = [2000, 4000, 8000];
  let clues: Clue[] = [];

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    await sleep(delays[attempt]);

    try {
      clues = await $fetch<Clue[]>(`${DEFAULT_CUSTOM_API_ENDPOINT}/clues`, {
        method: "GET",
        params: {
          threadId: currentThreadStore.threadId,
          runId: currentThreadStore.runId,
        },
      });

      if (clues && clues.length > 0) {
        break;
      }
    } catch (err) {
      console.warn(`getClues attempt ${attempt + 1} failed`, err);
    }
  }

  currentThreadStore.initNetworkLinks(clues);

  return clues;
});

watch(
  () => clues.value,
  (newClues) => {
    if (newClues && newClues.length > 0) {
      emit("clues-loaded", newClues);
    }
  },
  { immediate: true }
);

const cluePacks = computed(() => {
  if (!clues.value) {
    return {};
  }

  const cluePacks: Record<
    LinkId,
    {
      packId: PackId;
      clues: Clue[];
    }[]
  > = {};

  for (const clue of clues.value) {
    if (!cluePacks[clue.link_id]) {
      cluePacks[clue.link_id] = [];
    }
    if (!cluePacks[clue.link_id].find((pack) => pack.packId === clue.pack_id)) {
      cluePacks[clue.link_id].push({
        packId: clue.pack_id,
        clues: [],
      });
    }
    const pack = cluePacks[clue.link_id].find(
      (pack) => pack.packId === clue.pack_id
    );
    if (!pack) {
      continue;
    }
    pack.clues.push(clue);

    cluePacks[clue.link_id] = sortBy(
      cluePacks[clue.link_id],
      (pack) => 0 - pack.clues[0].input_json.priority
    );
  }
  return cluePacks;
});
const checkPercentMap = ref<Record<string, number>>({});

const getCheckPercentMap = () => {
  const result: Record<string, number> = {};

  for (const linkId in networkLinks.value) {
    const clues = networkLinks.value[linkId].clues || [];
    const total = clues.length;

    if (total === 0) {
      result[linkId] = 0;
      continue;
    }

    const finished = clues.filter((clue) => clue.status === "done").length;
    result[linkId] = Math.round((finished / total) * 100);
  }

  return result;
};

const currentLinkId = computed(() => {
  return (
    Object.keys(networkLinks.value).find(
      (id) => networkLinks.value[id].selected
    ) || ""
  );
});

const selectLink = (linkId: string) => {
  currentThreadStore.selectNetworkLink(linkId);
  if (networkLinks.value[linkId]) {
    const hasSelected =
      (networkLinks.value[linkId].selectedPackIds?.length ?? 0) > 0;
    networkLinks.value[linkId].expanded = !hasSelected;
  }
};

const isPackVisible = (linkId: string, packId: string) => {
  return (
    networkLinks.value[linkId]?.selectedPackIds?.includes(packId) ||
    networkLinks.value[linkId]?.expanded
  );
};

const isExpanded = computed(
  () => networkLinks.value[currentLinkId.value]?.expanded ?? false
);

watch(isExpanded, () => {
  emit("tool-height-change");
});
const toggleExpanded = (linkId: string) => {
  networkLinks.value[linkId].expanded = !networkLinks.value[linkId].expanded;
};
const hasSelectedClues = computed(
  () =>
    (networkLinks.value[currentLinkId.value].selectedPackIds?.length ?? 0) > 0
);

onMounted(() => {
  window.addEventListener("scroll", handleNetworkLinkPosition);
  window.addEventListener("resize", handleNetworkLinkPosition);
  nextTick(() => {
    if (originalContainer.value) {
      const containerRect = originalContainer.value.getBoundingClientRect();
      stickyStyle.value = {
        left: containerRect.left + "px",
        width: containerRect.width + "px",
      };
    }
  });
  autoSelectLink.value = false;
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleNetworkLinkPosition);
  window.removeEventListener("resize", handleNetworkLinkPosition);
});

watch(
  () => networkLinks.value,
  (links) => {
    if (links) {
      // 跳转到第一个 expanded 为 false 的 link
      const linkId = Object.keys(links).find(
        (id) => links[id] && links[id].expanded === false
      );
      if (linkId && !autoSelectLink.value) {
        selectLink(linkId);
        autoSelectLink.value = true;
      }
    }
    checkPercentMap.value = getCheckPercentMap();
  },
  { deep: true }
);
</script>

<template>
  <!-- 粘性容器 - 使用 Teleport 挂载到 body -->
  <Teleport to="body">
    <div
      v-show="showStickyContainer && !haveNextSteps"
      class="fixed bg-[#fff] text-white z-20"
      :style="{
        top: '64px',
        minHeight: '50px',
        position: 'fixed',
        left: stickyStyle.left,
        width: stickyStyle.width,
        display: showStickyContainer ? 'block' : 'none',
      }"
    >
      <div class="w-full">
        <div class="flex flex-wrap gap-x-2 w-full">
          <NetworkLink
            v-for="linkId in Object.keys(networkLinks)"
            :key="linkId"
            class="flex-1 max-w-full basis-0"
            :link-id="linkId"
            :selected="networkLinks[linkId].selected"
            :check-status="networkLinks[linkId].status"
            :check-result="networkLinks[linkId].result || ''"
            :check-percent="checkPercentMap[linkId]"
            @select="selectLink(linkId)"
          />
        </div>
      </div>
    </div>
  </Teleport>

  <!-- 原始位置的 NetworkLink 组件 -->
  <div ref="originalContainer" class="flex flex-wrap gap-x-2 w-full">
    <NetworkLink
      v-for="linkId in Object.keys(networkLinks)"
      :key="linkId"
      :ref="(el) => setNetworkLinkRef(el as HTMLElement, linkId)"
      class="flex-1 max-w-full basis-0"
      :link-id="linkId"
      :selected="networkLinks[linkId].selected"
      :check-status="networkLinks[linkId].status"
      :check-result="networkLinks[linkId].result || ''"
      :check-percent="checkPercentMap[linkId]"
      @select="selectLink(linkId)"
    />
  </div>

  <div
    v-if="currentLinkId"
    class="text-sm font-medium text-themecolor bg-title-bg h-10 border rounded-t-xl border-b-0 flex items-center px-6 py-2 justify-between"
    :class="{
      'rounded-b-xl border-b-1': !hasSelectedClues && !isExpanded,
    }"
  >
    <span v-if="!hasSelectedClues && !isExpanded">
      There are no selected clues in the current link.
    </span>
    <span v-else>{{ currentLinkId }}</span>
    <component
      :is="isExpanded ? ChevronUpIcon : ChevronDownIcon"
      v-if="(cluePacks[currentLinkId]?.length ?? 0) > 1"
      class="w-4 h-4 cursor-pointer"
      @click="toggleExpanded(currentLinkId)"
    />
  </div>
  <div
    v-if="currentLinkId"
    ref="cluePanelContainer"
    class="border bg-title-bg border-t-0 pt-0 p-2 rounded-b-xl"
    :class="{
      hidden: !hasSelectedClues && !isExpanded,
    }"
  >
    <div
      v-for="linkId in Object.keys(cluePacks)"
      :key="linkId"
      class="flex flex-col gap-y-2"
      :class="{
        hidden: !networkLinks[linkId].selected,
      }"
    >
      <Transition
        v-for="(pack, index) in cluePacks[linkId] || []"
        :key="pack.packId"
        name="pack-expand"
        appear
      >
        <CluePack
          v-show="isPackVisible(linkId, pack.packId)"
          :pack-id="pack.packId"
          :link-id="linkId"
          :clues="pack.clues"
          :selected="
            networkLinks[linkId].selectedPackIds?.includes(pack.packId)
          "
          :style="{
            '--enter-delay': !haveNextSteps ? `${index * 1000}ms` : '0ms',
          }"
        />
      </Transition>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.link-tag {
  cursor: pointer;
  color: #3a7ca5;

  &.active {
    border-color: #e1ebf1;
    background-color: #e1ebf1;
  }
}
</style>
