<script setup lang="ts">
import dayjs from "dayjs";
import { LineChart } from "echarts/charts";
import {
  DataZoomComponent,
  GridComponent,
  LegendComponent,
  MarkAreaComponent,
  TooltipComponent,
} from "echarts/components";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { computed, ref } from "vue";
import VChart from "vue-echarts";
import type { SparklineDataPoint } from "@/types/sentire/clue";
import { formatUnitValue } from "@/utils/format";

export interface ChartRange {
  maxRange: number | undefined;
  minRange: number | undefined;
}

// 注册必要的组件
use([
  CanvasRenderer,
  LineChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  MarkAreaComponent,
]);

interface Props {
  data?: SparklineDataPoint[];
  chartStatus?: string;
  chartRange?: ChartRange | undefined;
  height?: string | number;
  unit?: string;
}

const props = withDefaults(defineProps<Props>(), {
  data: undefined,
  chartStatus: "normal",
  chartRange: undefined,
  height: "40px",
  unit: "",
});

const chartRef = ref();

const chartHeight = computed(() => props.height);

// 获取图表数据
const chartData = computed(() => {
  if (props.data) {
    return props.data;
  }
  return [];
});

const chartOption = computed(() => {
  const data = chartData.value;

  // 按时间分组数据，同时按dimension分组
  const groupedData = data.reduce((acc, item) => {
    const timeKey = item.time;
    if (!acc[timeKey]) {
      acc[timeKey] = {};
    }
    // 使用dimension作为key，metric作为value
    acc[timeKey][item.dimension] = item.metric;
    return acc;
  }, {} as Record<number, Record<string, number>>);

  const timePoints = Object.keys(groupedData).map(Number).sort();

  // 获取所有的dimension（维度）
  const dimensions = Array.from(new Set(data.map((item) => item.dimension))); // 创建markArea数据 - 使用百分比坐标
  const markAreaData: Array<Array<{ x: string; y: string | number }>> = []; // 如果有传入的范围数据，添加对应的背景区域
  if (props.chartRange) {
    const range = props.chartRange;

    if (timePoints.length > 0 && range.minRange && range.maxRange) {
      const totalTimeRange = timePoints[timePoints.length - 1] - timePoints[0];

      if (totalTimeRange > 0) {
        const startPercent = Math.max(
          0,
          Math.min(
            100,
            ((range.minRange - timePoints[0]) / totalTimeRange) * 100
          )
        );
        const endPercent = Math.max(
          0,
          Math.min(
            100,
            ((range.maxRange - timePoints[0]) / totalTimeRange) * 100
          )
        );
        const containerWidth = 180;
        const gridLeft = 45;
        const gridRight = 0;

        const leftOffsetPercent = (gridLeft / containerWidth) * 100;
        const rightOffsetPercent = (gridRight / containerWidth) * 100;
        const chartAreaPercent = 100 - leftOffsetPercent - rightOffsetPercent;

        const adjustedStartPercent =
          leftOffsetPercent + (startPercent * chartAreaPercent) / 100;
        const adjustedEndPercent =
          leftOffsetPercent + (endPercent * chartAreaPercent) / 100;

        const areaConfig = [
          {
            x: `${adjustedStartPercent}%`,
            y: "0%",
          },
          {
            x: `${adjustedEndPercent}%`,
            y: "90%",
          },
        ];

        markAreaData.push(areaConfig);
      }
    }
  }
  return {
    tooltip: {
      trigger: "axis" as const,
      appendToBody: true, // 将 tooltip 添加到 body，避免被父级容器遮挡
      extraCssText: "z-index: 9999; box-shadow: 0 4px 8px rgba(0,0,0,0.15);", // 设置最高层级和阴影
      formatter: (
        params: Array<{ seriesName: string; color: string; dataIndex: number }>
      ) => {
        const timePoint = timePoints[params[0].dataIndex];
        const groupData = groupedData[timePoint];
        let result = `${dayjs(timePoint).format("HH:mm")}<br/>`; // 为每个系列添加带颜色圆点的显示
        params.forEach((param: { seriesName: string; color: string }) => {
          const dimension = param.seriesName;
          const metric = groupData[dimension] || 0;
          const color = param.color;
          result += `<span style="display:inline-block;margin-right:4px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>${dimension}：${formatUnitValue(
            metric,
            props.unit
          )}<br/>`;
        });

        return result;
      },
    },
    grid: {
      top: 5,
      left: 45,
      right: 0,
      bottom: 5,
      containLabel: false,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      axisLabel: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#ddd",
        },
      },
      data: timePoints.map(String),
    },
    yAxis: {
      type: "value",
      splitNumber: 1,
      axisLabel: {
        show: true,
        fontSize: 7,
        color: "#666",
        margin: 5,
        width: 35,
        align: "right",
        formatter: (value: number) => {
          const raw = formatUnitValue(value, props.unit);
          const match = raw.match(/^(\d*\.?\d+(?:[eE][+-]?\d+)?)(.*)$/);
          if (match) {
            let numStr = Number(match[1]).toPrecision(3);
            numStr = Number(numStr).toString();
            return numStr + match[2];
          } else {
            return raw;
          }
        },
        showMinLabel: true,
        showMaxLabel: true,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#f5f5f5",
          type: "solid",
        },
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "#ddd",
        },
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: "#ddd",
        },
      },
    },
    series: dimensions.map((dimension: string, index: number) => ({
      name: dimension,
      type: "line",
      smooth: true,
      symbol: "none",
      clip: true,
      stack: "total",
      areaStyle: {
        opacity: 0.4,
      },
      lineStyle: {
        width: 1,
      },
      data: timePoints.map((time) => {
        return groupedData[time][dimension] || 0;
      }),
      ...(index === 0 && markAreaData.length > 0
        ? {
            markArea: {
              silent: true,
              itemStyle: {
                color: props.chartStatus === "error" ? "#fff1f2" : "#fff3ea",
                borderColor:
                  props.chartStatus === "error" ? "#ff4757" : "#ffa502",
                borderWidth: 1,
                borderType: "solid",
              },
              label: {
                show: false,
              },
              emphasis: {
                disabled: true,
              },
              data: markAreaData,
            },
          }
        : {}),
    })),
  };
});

defineExpose({
  chartRef,
  refreshData: () => {
    if (chartRef.value) {
      chartRef.value.resize();
    }
  },
});
</script>

<template>
  <div v-if="data?.length" class="chart-container">
    <client-only>
      <v-chart
        ref="chartRef"
        :option="chartOption"
        :style="{ width: '100%', height: chartHeight }"
        autoresize
      />
    </client-only>
  </div>
</template>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;

  .vue-echarts {
    width: 100% !important;
    height: 100% !important;
  }
}
</style>
