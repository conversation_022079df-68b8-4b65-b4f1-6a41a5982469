<script setup lang="ts">
import CluePack from "./CluePack.vue";
import type { LinkId } from "~/types/sentire/clue";
import NetworkLink from "../NetworkLink.vue";
import type { NetworkLinks, CluesType } from "../../types";
import { buildCluePacks } from "./util";
import { ChevronDownIcon, ChevronUpIcon } from "lucide-vue-next";

const isExpanded = ref(false);

const props = defineProps<{
  networkLinks: NetworkLinks;
}>();

const cluePacks = ref(buildCluePacks(props.networkLinks));

const currentLinkId = computed(() => {
  return (
    Object.keys(cluePacks.value).find(
      (linkId) => cluePacks.value[linkId].selected
    ) || ""
  );
});

const handleLinkSelect = (linkId: LinkId) => {
  isExpanded.value = false;

  Object.values(cluePacks.value).forEach((link) => {
    link.selected = false;
  });
  cluePacks.value[linkId].selected = true;
};

const isPackSelect = (linkId: string) => {
  if ((cluePacks.value[linkId]?.packs?.length ?? 0) === 0) {
    return false;
  }
  return cluePacks.value[linkId]?.packs?.some((pack) => pack.selected) ?? false;
};

const isPackVisible = (linkId: string, packId: string) => {
  if (!isPackSelect(linkId) || isExpanded.value) {
    return true;
  }
  const pack = cluePacks.value[linkId].packs.find((p) => p.packId === packId);
  return pack?.selected || false;
};

function getCluesHealthResult(
  clues: CluesType[]
): "success" | "warning" | "error" {
  if (!Array.isArray(clues)) return "success";
  if (clues.some((c) => c.clue?.health?.status === "error")) {
    return "error";
  }
  if (clues.some((c) => c.clue?.health?.status === "warning")) {
    return "warning";
  }
  return "success";
}
</script>

<template>
  <div class="flex flex-wrap gap-x-2 w-full">
    <NetworkLink
      v-for="linkId in Object.keys(cluePacks)"
      :key="linkId"
      class="flex-1 max-w-full basis-0"
      :link-id="linkId"
      :selected="cluePacks[linkId]?.selected ?? false"
      :check-status="'done'"
      :check-result="getCluesHealthResult(networkLinks[linkId]?.clues)"
      @select="handleLinkSelect"
    />
  </div>

  <div
    class="flex items-center text-sm text-theme-color bg-[#f5f5f5] h-10 border py-2 px-6 border-[#d9d9d9] rounded-t-xl border-b-0"
  >
    <span>{{ currentLinkId }}</span>
    <span>{{ cluePacks[currentLinkId]?.selected ?? false }}</span>
    <component
      :is="isExpanded ? ChevronUpIcon : ChevronDownIcon"
      v-if="
        isPackSelect(currentLinkId) &&
        (cluePacks[currentLinkId]?.packs?.length ?? 0) > 1
      "
      class="w-4 h-4 cursor-pointer ml-auto"
      @click="isExpanded = !isExpanded"
    />
  </div>
  <div
    class="border-[0.5px] border-[#d9d9d9] rounded-b-xl p-2 pt-0 border-t-0 bg-title-bg"
  >
    <div
      v-for="linkId in Object.keys(cluePacks)"
      :key="linkId"
      class="flex flex-col gap-y-2"
      :class="{
        hidden: !cluePacks[linkId]?.selected,
      }"
    >
      <Transition
        v-for="pack in cluePacks[linkId]?.packs ?? []"
        :key="pack.packId"
        name="pack-expand"
        appear
      >
        <CluePack
          v-show="isPackVisible(linkId, pack.packId)"
          :pack-id="pack.packId"
          :link-id="linkId"
          :selected="pack.selected"
          :clues="pack.clues"
        />
      </Transition>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.link-tag {
  cursor: pointer;
  color: #3a7ca5;

  &.active {
    border-color: #e1ebf1;
    background-color: #e1ebf1;
  }
}
</style>
