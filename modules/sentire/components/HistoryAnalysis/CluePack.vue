<script setup lang="ts">
import Clue from "./Clue.vue";
import type { ClueWithData } from "~/types/sentire/clue";
import Markdown from "@/components/Markdown.vue";

const props = defineProps<{
  packId: string;
  linkId: string;
  clues: ClueWithData[];
  selected?: boolean;
}>();

const packName = computed(() => {
  return props.clues[0]?.input_json.pack_name.en;
});
</script>

<template>
  <div
    class="rounded-xl"
    :class="{
      'border !border-[#b0cbdb]  shadow-pack ': selected,
    }"
  >
    <div class="flex items-center h-9 leading-9 px-4 pack-title">
      <Markdown :source="packName" class="prose prose-sm prose-code:text-sm" />
    </div>
    <div class="p-2 bg-white rounded-b-lg">
      <Clue
        v-for="clue in clues"
        :key="`${clue.thread_id}-${clue.pack_id}-${clue.clue_id}`"
        :clue="clue"
        :link-id="linkId"
        :pack-id="packId"
      />
    </div>
  </div>
</template>
<style scoped>
.pack-title {
  background: #fff;
  color: #595959;
  border-bottom: 1px solid #d9d9d9;
  border-radius: 8px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
</style>
