<script setup lang="ts">
import { computed, ref } from "vue";
import type { HistoryAnalysis, NetworkLink } from "../../types";
import type { ActiveClue } from "./types";
import type { Clue } from "~/types/sentire/clue";
import UserQuestion from "../UserQuestion.vue";
import Understand from "./Understand.vue";
import DeepAnalysis from "./DeepAnalysis.vue";
import ToolBar from "../ToolBar.vue";
import { useHistoryProcessor } from "../../hooks/useHistoryProcessor";
import { hiddenElementId } from "../../helpers";
import { buildCluePacks } from "./util";
import { DEFAULT_CUSTOM_API_ENDPOINT } from "../../const";

defineOptions({
  name: "HistoryAnalysisIndex",
});

const props = defineProps<{
  index: number;
  analysis: HistoryAnalysis;
}>();

const emit = defineEmits<{
  (e: "regenerate"): void;
}>();

const historyProcessor = useHistoryProcessor();

const historyData = computed(() => {
  return historyProcessor.processHistoryMessages(props.analysis.messages);
});

const understandSteps = computed(() => {
  return historyData.value.steps;
});

const finalContent = computed(() => {
  return historyData.value.finalContent;
});

const initNetworkLinks = (clues: Clue[]) => {
  const networkLinks: Record<string, NetworkLink> = {};
  for (const clue of clues) {
    if (!networkLinks[clue.link_id]) {
      networkLinks[clue.link_id] = {
        expanded: true,
        selected: false,
        status: "checking",
        result: clue.output_json.status.status,
        selectedPackIds: [],
        clues: [],
      };
    }
    const rawClue = toRaw(clue);
    if (rawClue.is_selected) {
      networkLinks[clue.link_id].selectedPackIds?.push(clue.pack_id);
    }
    networkLinks[clue.link_id].clues.push({
      id: clue.id,
      clueId: clue.clue_id,
      threadId: clue.thread_id,
      packId: clue.pack_id,
      status: "checking",
      result: null,
      clue: {
        ...rawClue,
        sparkline: rawClue.output_json.sparkline,
        health: rawClue.output_json.status,
      },
    });
  }

  const defaultLink = Object.keys(networkLinks)[0];
  if (defaultLink) {
    networkLinks[defaultLink].selected = true;
  }

  return networkLinks;
};

const activeClues = computed(() => {
  for (const step of historyData.value.steps) {
    for (const message of step.messages) {
      if (
        message.type === "tool-call" &&
        message.toolName === "lookupCluesTool"
      ) {
        return (message.result?.activeClues ?? []) as ActiveClue[];
      }
    }
  }
  return [] as ActiveClue[];
});

const { data: clues } = useAsyncData(
  "clues",
  async () => {
    const ids = activeClues.value.map((clue) => clue.id);
    return await $fetch<Clue[]>(`${DEFAULT_CUSTOM_API_ENDPOINT}/clues-batch`, {
      method: "GET",
      params: {
        ids: ids ?? [],
      },
    });
  },
  {
    watch: [activeClues],
  }
);

const networkLinks = computed(() => {
  return initNetworkLinks(clues.value ?? []);
});

const cluePacks = ref(buildCluePacks(networkLinks.value));
</script>

<template>
  <div>
    <UserQuestion :value="props.analysis.userInput.question" />
    <!-- table of content anchor -->
    <div
      :id="`${slugify(hiddenElementId(props.analysis.userInput.question))}`"
    />
    <Understand
      :steps="understandSteps"
      :clue-packs="cluePacks"
      :network-links="networkLinks"
      :summary-title="props.analysis.summaryTitle"
    />
    <DeepAnalysis :messages="finalContent" />
    <ToolBar
      :copy-data="finalContent"
      class="relative -left-2"
      status="done"
      @regenerate="emit('regenerate')"
    />
  </div>

  <div class="border-b my-8" />
</template>
