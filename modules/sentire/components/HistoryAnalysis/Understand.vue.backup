<script setup lang="ts">
import type { NetworkLinks, CheckingResult } from "../../types";
import type { TextMessage, ToolCallMessage } from "~/types/message";
import { ChevronDownIcon, ChevronUpIcon, Webhook } from "lucide-vue-next";
import { <PERSON><PERSON> } from "@/components/ui/button";

import StandardToolCall from "@/components/StandardToolCall.vue";
import Markdown from "@/components/Markdown.vue";
import NetworkLinkClues from "./NetworkLinkClues.vue";
import { TOOL_CALL_NAMES } from "../../const";
import type { ClueWithData, LinkId, PackId } from "~/types/sentire/clue";
import ArtifactReportToolCall from "../ArtifactReportToolCall.vue";
import type { PlanStepWithMessages } from "../../hooks/useStreamProcessor";
import {
  isText,
  isToolCall,
  isLookupCluesToolCall,
  isArtifactReportToolCall,
} from "@/hooks/agent/message";

const contentRef = ref<HTMLElement | null>(null);
const toolCallMaxHeight = ref(false);
const buttonIsShow = ref(true);

const toolHeightChange = () => {
  if (props.summaryTitle && isOpen.value) {
    toolCallMaxHeight.value = true;
  }
  nextTick(() => {
    requestAnimationFrame(() => {
      buttonIsShow.value = (contentRef.value?.scrollHeight ?? 0) > 400;
    });
  });
};
const props = defineProps<{
  steps: PlanStepWithMessages[];
  networkLinks: NetworkLinks;
  summaryTitle: string | undefined;
  cluePacks: Record<
    LinkId,
    {
      result: CheckingResult;
      selected: boolean;
      packs: {
        packId: PackId;
        selected: boolean;
        clues: ClueWithData[];
      }[];
    }
  >;
}>();

const isOpen = ref(false);

const contentMaxHeight = ref("");

watch([isOpen, toolCallMaxHeight], ([valIsOpen, valToolHeight]) => {
  if (valIsOpen && contentRef.value) {
    nextTick(() => {
      if (contentRef.value || valToolHeight) {
        const actualHeight = contentRef.value?.scrollHeight + "px";
        contentMaxHeight.value = actualHeight;

        if (valToolHeight) {
          toolCallMaxHeight.value = false;
        }
      }
    });
  } else if (!valIsOpen && props.summaryTitle) {
    contentMaxHeight.value = "400px";
    buttonIsShow.value = (contentRef.value?.scrollHeight ?? 0) > 400;
  }
});
</script>

<template>
  <div
    v-if="steps.length"
    class="my-4 border rounded-lg-new border-bordercolor p-3"
  >
    <div
      class="bg-title-bg items-center justify-between rounded-lg py-2 px-2 flex mb-3"
      :class="{
        hidden: !summaryTitle,
      }"
    >
      <div class="flex items-center gap-4">
        <Webhook class="h-4 w-4 text-primary" />
        <div class="text-sm font-medium text-default-text">
          {{ summaryTitle }}
        </div>
      </div>
      <Button
        :style="buttonIsShow ? '' : 'visibility: hidden;'"
        variant="ghost"
        size="sm"
        class="w-9 p-0 cursor-pointer"
        @click="isOpen = !isOpen"
      >
        <ChevronDownIcon v-if="!isOpen" class="h-4 w-4" />
        <ChevronUpIcon v-else class="h-4 w-4" />
      </Button>
    </div>
    <div
      ref="contentRef"
      class="content-container transition-all duration-500 ease-in-out overflow-hidden"
      :class="{
        collapsed: !isOpen,
        expanded: isOpen,
      }"
      :style="{ maxHeight: contentMaxHeight }"
    >
      <div class="flex w-full flex-col justify-start gap-6 my-4">
        <div
          v-for="step in steps"
          :key="step.id"
          class="flex flex-col gap-3 w-full"
        >
          <div
            :class="[step.status === 'running' && 'text-primary']"
            class="text-sm font-medium transition"
          >
            <Markdown
              :source="TOOL_CALL_NAMES[step.title as keyof typeof TOOL_CALL_NAMES] ?? step.title"
              :style="'font-size: 14px'"
              :class="'prose-p:text-default-text prose-code:text-sm'"
            />
          </div>
          <div
            :class="[step.status === 'running' && 'text-primary']"
            class="text-xs text-muted-foreground transition lg:text-sm flex flex-col space-y-2"
          >
            <div
              v-for="(content, contentIndex) in step.messages"
              :key="contentIndex"
            >
              <StandardToolCall
                v-if="isToolCall(content)"
                :name="(content as ToolCallMessage).toolName"
                :input="(content as ToolCallMessage).args"
                :output="(content as ToolCallMessage).result as Record<string, unknown>"
                :elapsed-time="(content as ToolCallMessage).elapsedTime"
                :is-history-analysis="true"
                @tool-height-change="toolHeightChange"
              />
              <NetworkLinkClues
                v-if="
                  isLookupCluesToolCall(content) &&
                  Object.keys(props.networkLinks).length > 0
                "
                :network-links="props.networkLinks"
              />
              <ArtifactReportToolCall
                v-else-if="isArtifactReportToolCall(content)"
                class="mt-2"
                :report-id="(content as ToolCallMessage).result?.id as string"
                :status="(content as ToolCallMessage).result?.status as string"
                :title="(content as ToolCallMessage).result?.title as string"
                :message="(content as ToolCallMessage).result?.message as string"
              />
              <div
                v-if="isText(content) && (content as TextMessage).text.length > 0"
              >
                <Markdown
                  :source="(content as TextMessage).text"
                  :class="'prose-p:text-default-text  prose-code:text-xs'"
                  :style="'font-size: 12px'"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.content-container {
  transition: max-height 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  &.collapsed {
    overflow-y: auto;
    display: flex;
    flex-direction: column-reverse;
    max-height: 400px;
  }
  &.expanded {
    overflow: auto;
    display: flex;
    flex-direction: column;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
