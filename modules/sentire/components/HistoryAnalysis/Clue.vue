<script setup lang="ts">
import type { ClueWithData } from "~/types/sentire/clue";
import MiniChart from "../MiniChart.vue";
import dayjs from "dayjs";

defineProps<{
  linkId: string;
  packId: string;
  clue: ClueWithData;
}>();

type CheckpointStatus = "unknown" | "error" | "success" | "warning";

const getCheckpointClass = (
  type: "status" | "text",
  status: CheckpointStatus
) => {
  const map = {
    status: {
      unknown: "bg-title-bg text-checking-text",
      success: "bg-success-bg text-success-text",
      warning: "bg-warning-bg text-warning-text",
      error: "bg-error-bg text-error-text",
    },
    text: {
      unknown: "text-default-text",
      success: "text-default-text",
      warning: "text-warning-text",
      error: "text-error-text",
    },
  };
  return map[type][status] || "";
};

const getCheckpointStatusText = (status: string) => {
  switch (status) {
    case "checking":
      return "Pending";
    case "success":
      return "CLEAR";
    case "warning":
      return "ATTENTION";
    case "error":
      return "SUSPICIOUS";
    default:
      return "CLEAR";
  }
};
</script>

<template>
  <div class="flex items-center">
    <span
      class="w-[120px] text-sm inline-block pt-[2px] pb-[2px] pr-4 pl-4 rounded text-center flex-shrink-0"
      :class="getCheckpointClass('status', clue?.health?.status)"
    >
      {{ getCheckpointStatusText(clue?.health?.status) }}
    </span>
    <div
      class="flex-1 px-4"
      :class="getCheckpointClass('text', clue?.health?.status)"
    >
      {{ clue?.health?.message }}
    </div>
    <div
      class="w-[180px] h-12 bg-white rounded flex items-center justify-center flex-shrink-0 ml-4"
    >
      <MiniChart
        height="100%"
        :chart-range="
          clue?.health?.status === 'success'
            ? undefined
            : {
                minRange: clue?.health?.clue_start_time
                  ? dayjs(clue?.health?.clue_start_time).valueOf()
                  : undefined,
                maxRange: clue?.health?.clue_end_time
                  ? dayjs(clue?.health?.clue_end_time).valueOf()
                  : undefined,
              }
        "
        :unit="clue?.input_json.checkpoint.sparkline.metric?.unit"
        :data="
          clue?.sparkline?.map((item) => ({
            ...item,
            time: dayjs(item.time).valueOf(),
          }))
        "
        :chart-status="clue?.health?.status as string"
      />
    </div>
  </div>
</template>
