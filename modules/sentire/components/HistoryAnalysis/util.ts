import { sortBy } from "lodash-es";
import type { ClueWithData, LinkId, PackId } from "~/types/sentire/clue";
import type { CheckingResult, NetworkLinks } from "../../types";

/**
 * 生成 cluePacks 数据结构
 * @param networkLinks 网络链路对象
 */

export function buildCluePacks(networkLinks: NetworkLinks): Record<
  LinkId,
  {
    result: CheckingResult;
    selected: boolean;
    packs: {
      packId: PackId;
      selected: boolean;
      clues: ClueWithData[];
    }[];
  }
> {
  if (!networkLinks) return {};
  const cluePacks: Record<
    LinkId,
    {
      result: CheckingResult;
      selected: boolean;
      packs: {
        packId: PackId;
        selected: boolean;
        clues: ClueWithData[];
      }[];
    }
  > = {};

  for (const [linkId, link] of Object.entries(networkLinks)) {
    for (const clue of link.clues) {
      if (!cluePacks[linkId]) {
        cluePacks[linkId] = {
          result: link.result,
          selected: link.selected,
          packs: [],
        };
      }
      if (
        !cluePacks[linkId].packs.find((pack) => pack.packId === clue.packId)
      ) {
        cluePacks[linkId].packs.push({
          selected: link.selectedPackIds?.includes(clue.packId) as boolean,
          packId: clue.packId,
          clues: [],
        });
      }
      const pack = cluePacks[linkId].packs.find(
        (pack) => pack.packId === clue.packId
      );
      if (!pack) continue;
      if (clue.clue) {
        pack.clues.push(clue.clue);
      }
      cluePacks[linkId].packs = sortBy(
        cluePacks[linkId].packs,
        (pack) => 0 - (pack.clues[0]?.input_json?.priority ?? 0)
      );
    }
  }
  return cluePacks;
}

export const getLinkDotClass = (status: string | null = "checking"): string => {
  const map: Record<string, string> = {
    checking: "bg-checking-text",
    success: "bg-success-text",
    warning: "bg-warning-text",
    error: "bg-error-text",
  };
  return map[status ? status : "checking"];
};
