<script setup lang="ts">
const props = defineProps<{
  text: string;
  class?: string;
}>();
</script>
<template>
  <div class="animate-checking-text" :class="props.class">{{ text }}</div>
</template>

<style scoped>
.animate-checking-text {
  background: linear-gradient(90deg, #000, #e3e3e3, #000);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  background-size: 200% 100%;
  animation: gradientMove 1.5s linear infinite;
}

@keyframes gradientMove {
  0% {
    background-position: 100% 80%;
  }
  100% {
    background-position: 0% 80%;
  }
}
</style>
