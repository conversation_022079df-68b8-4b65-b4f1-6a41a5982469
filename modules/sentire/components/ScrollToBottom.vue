<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";

const { isRunning } = defineProps<{
  isRunning?: boolean;
}>();

const isVisible = ref(false);

const emit = defineEmits<{
  (e: "to-bottom"): void;
}>();

const scrollToBottom = () => {
  window.scrollTo({
    top: document.body.scrollHeight,
    behavior: "smooth",
  });
  emit("to-bottom");
};

const handleVisible = () => {
  const isNearBottom =
    window.innerHeight + window.scrollY >= document.body.offsetHeight - 100;
  isVisible.value = !isNearBottom;
};

let resizeObserver: ResizeObserver | null = null;

onMounted(async () => {
  window.addEventListener("scroll", handleVisible);
  handleVisible();
  if (window.ResizeObserver) {
    resizeObserver = new ResizeObserver(() => {
      requestAnimationFrame(() => {
        handleVisible();
      });
    });
    resizeObserver.observe(document.body);
  }
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleVisible);
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});
</script>

<template>
  <div
    class="absolute -top-12 cursor-pointer left-1/2 transform -translate-x-1/2 z-10"
    :class="{
      'opacity-0 top-0 pointer-events-none': !isVisible,
    }"
    @click="scrollToBottom"
  >
    <!-- Button with animated border -->
    <div class="relative">
      <!-- Main circular button with frosted glass effect -->
      <div
        class="w-9 h-9 rounded-full flex items-center justify-center relative overflow-hidden glass-button"
      >
        <!-- Full circle border (when not running) -->
        <div
          v-if="!isRunning || !isVisible"
          class="absolute inset-0 rounded-full border-2 border-white/40"
        />

        <!-- Animated quarter circle border (when running) -->
        <div
          v-if="isRunning && isVisible"
          class="absolute inset-0"
        >
          <div class="quarter-circle-border animate-spin" />
        </div>

        <!-- Stationary downward arrow with vertical line -->
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="text-gray-700 drop-shadow-sm"
        >
          <!-- Vertical line -->
          <line
            x1="8"
            y1="2"
            x2="8"
            y2="12"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
          />
          <!-- Arrow head -->
          <path
            d="M5 9L8 12L11 9"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
            fill="none"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 磨玻璃效果 */
.glass-button {
  /* 半透明白色背景 */
  background: rgba(255, 255, 255, 0.25);

  /* 磨玻璃模糊效果 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);

  /* 边框增强玻璃质感 */
  border: 1px solid rgba(255, 255, 255, 0.3);

  /* 渐变背景增加深度 */
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );

  /* 柔和的浮动立体效果 */
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  /* 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果 */
.glass-button:hover {
  background: rgba(255, 255, 255, 0.35);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  /* 悬停时增强阴影效果 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 激活状态 */
.glass-button:active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.3);
  /* 按下时减少阴影，模拟按下效果 */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.quarter-circle-border {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top-color: #3a7ca5; /* Theme color for the animated segment */
  animation: spin 1s linear infinite;
  will-change: transform;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
