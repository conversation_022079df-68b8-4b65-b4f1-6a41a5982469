<script setup lang="ts">
import { CheckIcon, RefreshCwIcon, CopyIcon } from "lucide-vue-next";
import { useClipboard } from "@vueuse/core";

const props = defineProps<{
  class?: string;
  source: string;
}>();

const { copy, copied } = useClipboard({
  source: props.source,
});

const emit = defineEmits<{
  (e: "regenerate"): void;
}>();
</script>

<template>
  <div class="flex items-center gap-x-1" :class="props.class">
    <Tooltip>
      <TooltipTrigger as-child>
        <Button variant="ghost" size="sm" @click="emit('regenerate')">
          <RefreshCwIcon :size="16" class="text-stone-500" />
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>Regenerate</p>
      </TooltipContent>
    </Tooltip>
    <Tooltip>
      <TooltipTrigger as-child>
        <Button variant="ghost" size="sm" @click="() => copy(props.source)">
          <CopyIcon v-if="!copied" :size="16" class="text-stone-500" />
          <CheckIcon v-if="copied" :size="16" class="text-green-400" />
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>Copy</p>
      </TooltipContent>
    </Tooltip>
  </div>
</template>
