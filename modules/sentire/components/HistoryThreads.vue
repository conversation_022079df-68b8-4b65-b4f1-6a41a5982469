<script setup lang="ts">
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  SearchIcon,
  EllipsisIcon,
  TrashIcon,
  CopyIcon,
  LoaderIcon,
  HistoryIcon,
  XIcon,
} from "lucide-vue-next";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  useCurrentThreadStore,
  useSentireStore,
  useThreadsStore,
} from "../stores";
import { nextTick, watchEffect } from "vue";
import { storeToRefs } from "pinia";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter, useRoute } from "vue-router";
import { watchDebounced, useClipboard } from "@vueuse/core";
import type { StorageThreadType } from "@mastra/core";
import { RecycleScroller } from "vue-virtual-scroller";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";

interface FlattenedItem {
  id: string;
  type: "group" | "thread";
  data?: StorageThreadType;
  groupTitle?: string;
  height: number;
}

const sentireStore = useSentireStore();
const threadsStore = useThreadsStore();
const { historyThreadsSheetOpened } = storeToRefs(sentireStore);
const { timeGroupedThreads, pending: loadingThreads } =
  storeToRefs(threadsStore);
const currentThreadStore = useCurrentThreadStore();
const { threadId: currentThreadId } = storeToRefs(currentThreadStore);
const router = useRouter();
const route = useRoute();
const inputValue = shallowRef<string>("");
const scroller = ref<InstanceType<typeof RecycleScroller>>();

const flattenedThreads = computed<FlattenedItem[]>(() => {
  const result: FlattenedItem[] = [];

  Object.entries(timeGroupedThreads.value).forEach(([date, threads]) => {
    result.push({
      id: `group-${date}`,
      type: "group",
      groupTitle: date,
      height: 40,
    });

    threads.forEach((thread) => {
      result.push({
        id: thread.id,
        type: "thread",
        data: thread,
        height: 50,
      });
    });
  });

  return result;
});

const scrollToCurrentThread = async () => {
  if (!currentThreadId.value) return;

  await nextTick();

  if (scroller.value) {
    const currentThreadIndex = flattenedThreads.value.findIndex(
      (item) =>
        item.type === "thread" && item.data?.id === currentThreadId.value
    );

    if (currentThreadIndex !== -1) {
      scroller.value.scrollToItem(currentThreadIndex);
    }
  }
};

watchEffect(() => {
  if (
    historyThreadsSheetOpened.value &&
    currentThreadId.value &&
    flattenedThreads.value.length > 0
  ) {
    setTimeout(scrollToCurrentThread, 100);
  }
});

const selectThread = async (threadId: string) => {
  await currentThreadStore.initializeWithThreadId(threadId);
  console.time("router.replace");
  await router.replace({
    path: route.path,
    query: { ...route.query, threadId },
  });
  console.timeEnd("router.replace");

  sentireStore.closeHistoryThreadsSheet();
};

const clearSearch = () => {
  threadsStore.updateKeyword([]);
  inputValue.value = "";
};

const historyTopic = (thread: StorageThreadType) => {
  if (
    thread.title?.match(
      /New Thread \d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/
    )
  ) {
    return thread.metadata?.["topic"] ?? thread.title;
  }

  return thread.title;
};

const handleDeleteThread = async (threadId: string) => {
  await threadsStore.deleteThread(threadId);
  window.location.href = `/sentire`;
};

watchDebounced(
  inputValue,
  (value) => {
    threadsStore.updateKeyword([value]);
  },
  { debounce: 100, maxWait: 1000 }
);

const { copy } = useClipboard();
const handleCopy = (threadId: string) => {
  copy(threadId);
};
</script>

<template>
  <Button
    class="cursor-pointer text-stone-700"
    variant="ghost"
    size="icon"
    @click="sentireStore.openHistoryThreadsSheet()"
  >
    <HistoryIcon :size="18" />
  </Button>
  <Sheet v-model:open="historyThreadsSheetOpened">
    <SheetContent>
      <template #header>
        <SheetHeader class="p-0 pl-6">
          <SheetTitle>History</SheetTitle>
        </SheetHeader>
      </template>
      <template #default>
        <div class="p-4 flex-1 flex flex-col h-[calc(100vh-100px)]">
          <div class="relative w-full max-w-sm items-center">
            <Input
              v-model="inputValue"
              type="text"
              placeholder="Search history"
              class="pl-10 rounded-full h-11"
              :class="{
                'pr-10': loadingThreads,
              }"
            />
            <span
              class="absolute start-0 inset-y-0 flex items-center justify-center px-3"
            >
              <SearchIcon class="size-4 text-muted-foreground" />
            </span>
            <span
              v-if="loadingThreads"
              class="size-8 absolute right-2 top-[calc(50%-16px)] flex items-center justify-center rounded-full cursor-pointer"
            >
              <LoaderIcon class="size-4 text-muted-foreground animate-spin" />
            </span>
            <Button
              v-else-if="threadsStore.keyword.length > 0"
              variant="ghost"
              class="size-8 absolute right-2 top-[calc(50%-16px)] flex items-center justify-center rounded-full cursor-pointer"
              @click="clearSearch"
            >
              <XIcon class="size-4 text-muted-foreground rounded-full" />
            </Button>
          </div>

          <div
            class="flex-1 mt-4 overflow-y-auto rounded-lg shadow-[inset_0_0_10px_10px_rgba(255,255,255,0.1)]"
          >
            <RecycleScroller
              ref="scroller"
              v-slot="{ item }"
              class="scroller h-full"
              :items="flattenedThreads"
              :item-size="null"
              size-field="height"
              type-field="type"
              key-field="id"
              :buffer="200"
            >
              <div
                v-if="item.type === 'group'"
                :style="{ height: item.height + 'px' }"
              >
                <p class="text-sm p-2">{{ item.groupTitle }}</p>
              </div>

              <div
                v-else-if="item.type === 'thread' && item.data"
                :data-thread-id="item.data.id"
                class="mb-1"
                :style="{ height: item.height + 'px' }"
                @click="selectThread(item.data.id)"
              >
                <div
                  class="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-stone-200 cursor-pointer transition-colors"
                  :class="{
                    'bg-stone-300': currentThreadId === item.data.id,
                    'bg-stone-100': currentThreadId !== item.data.id,
                  }"
                >
                  <p class="truncate text-sm">
                    {{ historyTopic(item.data) }}
                  </p>

                  <DropdownMenu>
                    <DropdownMenuTrigger @click.stop>
                      <Button
                        variant="ghost"
                        class="rounded-full size-8 cursor-pointer"
                      >
                        <EllipsisIcon :size="16" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem
                        class="flex items-center gap-2 group transition-colors"
                        @click.stop="handleDeleteThread(item.data.id)"
                      >
                        <TrashIcon
                          :size="16"
                          class="group-hover:text-red-500"
                        />
                        <span class="text-sm group-hover:text-red-500">
                          Delete
                        </span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        class="flex items-center gap-2 group transition-colors"
                        @click.stop="handleCopy(item.data.id)"
                      >
                        <CopyIcon :size="16" />
                        CopyId
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </RecycleScroller>
          </div>
        </div>
      </template>
    </SheetContent>
  </Sheet>
</template>

<style scoped>
/* .scroller {
  height: 100%;
}

.scroller .vue-recycle-scroller__item-wrapper {
  box-sizing: border-box;
}

.scroller .vue-recycle-scroller__item-view {
  display: flex;
  flex-direction: column;
  min-height: 0;
} */
</style>
