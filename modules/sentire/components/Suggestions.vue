<script setup lang="ts">
import { h, computed } from "vue";
import { sortBy } from "lodash-es";
import Markdown from "@/components/Markdown.vue";
import { useCurrentThreadStore } from "~/modules/sentire/stores/currentThreadStore";
import type { Suggestion } from "../types";
import Button from "~/components/ui/button/Button.vue";
import { SquareArrowOutUpRightIcon } from "lucide-vue-next";

const currentThreadStore = useCurrentThreadStore();
const { suggestions, waitingForSuggestion } = storeToRefs(currentThreadStore);

const emit = defineEmits<{
  (e: "suggestion-clicked", suggestion: Suggestion): void;
}>();

const sortedSuggestions = computed(() => {
  if (!suggestions.value) return [];

  return sortBy(suggestions.value, (suggestion) =>
    suggestion.type === "link" ? 1 : 0
  );
});

const getComponent = (suggestion: Suggestion) => {
  if (suggestion.type === "text") {
    return () =>
      h(
        Button,
        {
          class:
            "block cursor-pointer bg-title-bg w-fit hover:bg-[#e5e5e5] h-fit flex justify-center items-center prose-p:text-deep-analysis-text px-3 py-1",
          onClick: () => emit("suggestion-clicked", suggestion),
        },
        {
          default: () =>
            h(Markdown, {
              source: suggestion.content,
              class:
                "prose prose-sentire-sm text-left",
            }),
        }
      );
  } else if (suggestion.type === "link") {
    return () =>
      h(
        Button,
        {
          class:
            "cursor-pointer hover:bg-[#e5e5e5] text-stone-700  whitespace-normal w-fit  h-fit px-3 py-1",
          variant: "secondary",
          as: "a",
          href: suggestion.link,
          target: "_blank",
        },
        {
          default: () => [
            h(
              "span",
              {
                class:
                  "whitespace-normal break-words text-deep-analysis-text text-xs leading-snug",
              },
              suggestion.content.replace(/`/g, "")
            ),
            h(SquareArrowOutUpRightIcon, {
              size: 12,
            }),
          ],
        }
      );
  }
};
</script>

<template>
  <div
    v-if="!waitingForSuggestion && (suggestions?.length ?? 0) > 0"
    class="mt-6 text-deep-analysis-text"
  >
    <h3 class="text-lg font-semibold leading-snug mb-4 text-gray-800">
      Suggested Actions
    </h3>
    <div class="text-xs leading-snug">
    <component
      :is="getComponent(suggestion)"
      v-for="(suggestion, suggestionIndex) in sortedSuggestions"
      :key="suggestionIndex"
      v-motion="{
        initial: { opacity: 0 },
        enter: {
          opacity: 1,
          transition: {
            delay: suggestionIndex * 300,
          },
        },
      }"
      class="hover:shadow-lg mb-4"
    />
    </div>
  </div>
</template>
<style scoped lang="scss">
:deep(.prose) {
  max-width: 100%;
}
</style>
