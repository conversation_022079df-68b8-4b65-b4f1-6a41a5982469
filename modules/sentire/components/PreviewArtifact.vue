<script setup lang="ts">
import { useCurrentThreadStore } from "../stores/currentThreadStore";
import { CheckIcon, XIcon, Share2Icon } from "lucide-vue-next";
import { useClipboard } from "@vueuse/core";
import { toast } from "vue-sonner";
import * as jose from "jose";
import { useArtifactReport } from "../hooks/useArtifactReport";

const { reportId } = defineProps<{
  reportId: string;
}>();

const { copy, copied, isSupported } = useClipboard();

const config = useRuntimeConfig();

const currentThreadStore = useCurrentThreadStore();
const report = await useArtifactReport(reportId);

const createShareableLink = async (
  reportId: string,
  exp: number = 60 * 60 * 24 * 7
) => {
  const payload = {
    reportId,
    exp: Math.round(Date.now() / 1000) + exp,
  };
  const jwt = await new jose.SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .sign(new TextEncoder().encode(config.public.jwtSecret));

  return `${window.location.origin}/sentire/artifact/${jwt}`;
};

const url = await createShareableLink(reportId);

const sharePage = async () => {
  await copy(url);
  toast.info("Copied to clipboard", {
    description: "Share this link with your friends",
    style: {
      background: "#fff",
    },
  });
};
</script>

<template>
  <div
    class="h-[calc(100vh-96px)] border border-stone-200 rounded-xl overflow-hidden"
  >
    <div class="flex justify-between items-baseline p-4 font-semibold border-b">
      <span>{{ report?.title }}</span>
      <div class="flex gap-3">
        <Button
          v-if="isSupported"
          variant="secondary"
          class="flex items-center gap-2 cursor-pointer rounded-full bg-primary/20 !px-4"
          @click="() => sharePage()"
        >
          <CheckIcon v-if="copied" :size="16" class="text-green-400" />
          <Share2Icon v-else :size="16" />
          Share
        </Button>

        <Button
          size="icon"
          class="rounded-full z-10 cursor-pointer"
          variant="ghost"
          @click="currentThreadStore.clearArtifactReport()"
        >
          <XIcon :size="16" />
        </Button>
      </div>
    </div>

    <iframe
      sandbox="allow-scripts allow-same-origin"
      :srcdoc="report?.content.value ?? ''"
      class="w-full"
      style="height: calc(100vh - 165px)"
    />
  </div>
</template>

<style scoped>
:deep(.sp-preview-actions) {
  display: none;
}

:deep(.sp-preview) {
  height: 100%;
}
</style>
