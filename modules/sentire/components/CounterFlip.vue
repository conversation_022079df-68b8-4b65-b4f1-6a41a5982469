<script setup lang="ts">
interface Props {
  value: number;
  duration?: number;
}

const props = withDefaults(defineProps<Props>(), {
  duration: 300,
});

const displayValue = computed(() => props.value);
</script>

<template>
  <div class="relative inline-block overflow-hidden h-5 leading-5 font-mono">
    <Transition name="flip" mode="out-in">
      <span :key="displayValue" class="block">
        {{ displayValue }}
      </span>
    </Transition>
  </div>
</template>

<style scoped>
.flip-enter-active,
.flip-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.flip-enter-from {
  transform: translateY(100%) rotateX(-90deg);
  opacity: 0;
}

.flip-leave-to {
  transform: translateY(-100%) rotateX(90deg);
  opacity: 0;
}

.flip-enter-to,
.flip-leave-from {
  transform: translateY(0) rotateX(0deg);
  opacity: 1;
}

/* 添加3D透视效果 */
.flip-enter-active,
.flip-leave-active {
  transform-style: preserve-3d;
  perspective: 100px;
}
</style>
