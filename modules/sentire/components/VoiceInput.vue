<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, nextTick } from "vue";
import dayjs from "dayjs";
import LocalizedFormat from "dayjs/plugin/localizedFormat";

dayjs.extend(LocalizedFormat);

declare global {
  interface Window {
    SpeechRecognition?: new () => SpeechRecognitionInterface;
    webkitSpeechRecognition?: new () => SpeechRecognitionInterface;
  }
}

interface SpeechRecognitionResult {
  [key: number]: {
    transcript: string;
  };
}

interface SpeechRecognitionEvent {
  results: {
    [key: number]: SpeechRecognitionResult;
    length: number;
  };
}

interface SpeechRecognitionErrorEvent {
  error: string;
}

interface SpeechRecognitionInterface {
  continuous: boolean;
  start(): void;
  stop(): void;
  onresult: ((event: SpeechRecognitionEvent) => void) | null;
  onend: (() => void) | null;
  onerror: ((event: SpeechRecognitionErrorEvent) => void) | null;
}

export type VoiceInputResult = {
  text?: string;
  file?: File;
  blob?: Blob;
};

interface AudioConfig {
  stt: {
    engine: string;
  };
}

interface Props {
  recording?: boolean;
  transcribe?: boolean;
  className?: string;
  audioConfig?: AudioConfig;
}

const props = withDefaults(defineProps<Props>(), {
  recording: false,
  transcribe: true,
  className: "p-1 w-full max-w-full",
  audioConfig: () => ({ stt: { engine: "web", language: "en-US" } }),
});

const emit = defineEmits<{
  cancel: [];
  confirm: [data: VoiceInputResult];
}>();

const loading = ref(false);
const confirmed = ref(false);
const durationSeconds = ref(0);
const durationCounter = ref<NodeJS.Timeout | null>(null);
const transcription = ref("");
const containerRef = ref<HTMLElement>();
const containerWidth = ref(0);

const stream = ref<MediaStream | null>(null);
const speechRecognition = ref<SpeechRecognitionInterface | null>(null);
const mediaRecorder = ref<MediaRecorder | null>(null);
const audioChunks = ref<Blob[]>([]);

const MIN_DECIBELS = -45;
let VISUALIZER_BUFFER_LENGTH = 300;
const visualizerData = ref<number[]>(Array(VISUALIZER_BUFFER_LENGTH).fill(0));

const blobToFile = (blob: Blob, fileName: string): File => {
  return new File([blob], fileName, { type: blob.type });
};

const transcribeAudio = async (
  token: string,
  file: File,
  language?: string
) => {
  console.log("Transcribing audio:", { token, file, language });
  // TODO : call agent voice api to transcribe audio
  return { text: "Transcribed text" };
};

const startDurationCounter = () => {
  durationCounter.value = setInterval(() => {
    durationSeconds.value++;
  }, 1000);
};

const stopDurationCounter = () => {
  if (durationCounter.value) {
    clearInterval(durationCounter.value);
    durationCounter.value = null;
  }
  durationSeconds.value = 0;
};

const formatSeconds = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  const formattedSeconds =
    remainingSeconds < 10 ? `0${remainingSeconds}` : remainingSeconds;
  return `${minutes}:${formattedSeconds}`;
};

const calculateRMS = (data: Uint8Array): number => {
  let sumSquares = 0;
  for (let i = 0; i < data.length; i++) {
    const normalizedValue = (data[i] - 128) / 128;
    sumSquares += normalizedValue * normalizedValue;
  }
  return Math.sqrt(sumSquares / data.length);
};

const normalizeRMS = (rms: number): number => {
  rms = rms * 10;
  const exp = 1.5;
  const scaledRMS = Math.pow(rms, exp);
  return Math.min(1.0, Math.max(0.01, scaledRMS));
};

const analyseAudio = (mediaStream: MediaStream) => {
  const audioContext = new AudioContext();
  const audioStreamSource = audioContext.createMediaStreamSource(mediaStream);

  const analyser = audioContext.createAnalyser();
  analyser.minDecibels = MIN_DECIBELS;
  audioStreamSource.connect(analyser);

  const bufferLength = analyser.frequencyBinCount;
  const domainData = new Uint8Array(bufferLength);
  const timeDomainData = new Uint8Array(analyser.fftSize);

  const processFrame = () => {
    if (!props.recording || loading.value) return;

    if (props.recording && !loading.value) {
      analyser.getByteTimeDomainData(timeDomainData);
      analyser.getByteFrequencyData(domainData);

      const rmsLevel = calculateRMS(timeDomainData);
      visualizerData.value.push(normalizeRMS(rmsLevel));

      if (visualizerData.value.length >= VISUALIZER_BUFFER_LENGTH) {
        visualizerData.value.shift();
      }
    }

    window.requestAnimationFrame(processFrame);
  };

  window.requestAnimationFrame(processFrame);
};

const onStopHandler = async (audioBlob: Blob, ext: string = "wav") => {
  await nextTick();
  const file = blobToFile(
    audioBlob,
    `Recording-${dayjs().format("L LT")}.${ext}`
  );

  if (props.transcribe) {
    if (props.audioConfig.stt.engine === "web") {
      return;
    }

    try {
      const res = await transcribeAudio(localStorage.token, file);

      if (res) {
        console.log(res);
        emit("confirm", res);
      }
    } catch (error) {
      console.error(`${error}`);
    }
  } else {
    emit("confirm", {
      file: file,
      blob: audioBlob,
    });
  }
};

const startRecording = async () => {
  loading.value = true;

  console.time("beforeStartRecording");
  try {
    console.time("getUserMedia");
    stream.value = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
    });
  } catch (err) {
    console.error("Error accessing media devices.", err);
    console.error("Error accessing media devices.");
    loading.value = false;
    return;
  }
  console.timeEnd("getUserMedia");

  if (!stream.value) return;

  // 检测浏览器支持的 mimeType
  let mimeType = "audio/webm";
  const supportedTypes = [
    "audio/webm; codecs=opus",
    "audio/webm",
    "audio/mp4",
    "audio/mp4; codecs=mp4a.40.2",
    "audio/ogg; codecs=opus",
    "audio/wav",
  ];

  for (const type of supportedTypes) {
    if (MediaRecorder.isTypeSupported(type)) {
      mimeType = type;
      break;
    }
  }

  console.log("Using mimeType:", mimeType);

  mediaRecorder.value = new MediaRecorder(stream.value, {
    mimeType: mimeType,
  });

  mediaRecorder.value.onstart = () => {
    console.timeEnd("beforeStartRecording");
    console.log("Recording started");
    loading.value = false;
    startDurationCounter();
    audioChunks.value = [];
    analyseAudio(stream.value!);
  };

  mediaRecorder.value.ondataavailable = (event) =>
    audioChunks.value.push(event.data);

  mediaRecorder.value.onstop = async () => {
    console.log("Recording stopped");

    if (confirmed.value) {
      const type =
        audioChunks.value[0]?.type ||
        mediaRecorder.value?.mimeType ||
        "audio/webm";
      let ext = type.split("/")[1].split(";")[0] || "webm";

      if (!type.startsWith("audio/")) {
        ext = "webm";
      }

      const audioBlob = new Blob(audioChunks.value, { type: type });
      await onStopHandler(audioBlob, ext);

      confirmed.value = false;
    }

    audioChunks.value = [];
  };

  try {
    mediaRecorder.value.start();
  } catch (error) {
    console.error("Error starting recording:", error);
    console.error("Error starting recording.");
    loading.value = false;
    return;
  }

  if (props.transcribe) {
    if (props.audioConfig.stt.engine === "web") {
      if (
        "SpeechRecognition" in window ||
        "webkitSpeechRecognition" in window
      ) {
        const SpeechRecognitionClass =
          window.SpeechRecognition || window.webkitSpeechRecognition;
        if (SpeechRecognitionClass) {
          speechRecognition.value = new SpeechRecognitionClass();
        }

        if (!speechRecognition.value) return;

        speechRecognition.value.continuous = true;

        const inactivityTimeout = 200;
        let timeoutId: NodeJS.Timeout;

        speechRecognition.value.start();

        speechRecognition.value.onresult = async (
          event: SpeechRecognitionEvent
        ) => {
          clearTimeout(timeoutId);
          const transcript =
            event.results[Object.keys(event.results).length - 1][0].transcript;
          transcription.value = `${transcription.value}${transcript}`;

          await nextTick();
          document.getElementById("chat-input")?.focus();

          timeoutId = setTimeout(() => {
            console.log("Speech recognition turned off due to inactivity.");
            speechRecognition.value?.stop();
          }, inactivityTimeout);
        };

        speechRecognition.value!.onend = function () {
          console.log("recognition ended");
          confirmRecording();
          emit("confirm", {
            text: transcription.value,
          });
          confirmed.value = false;
          loading.value = false;
        };

        speechRecognition.value!.onerror = function (
          event: SpeechRecognitionErrorEvent
        ) {
          console.log(event);
          console.error(`Speech recognition error: ${event.error}`);
          emit("cancel");
          stopRecording();
        };
      }
    }
  }
};

const stopRecording = async () => {
  if (props.recording && mediaRecorder.value) {
    mediaRecorder.value.stop();
  }

  if (speechRecognition.value) {
    speechRecognition.value.stop();
  }

  stopDurationCounter();
  audioChunks.value = [];

  if (stream.value) {
    const tracks = stream.value.getTracks();
    tracks.forEach((track) => track.stop());
  }

  stream.value = null;
};

const confirmRecording = async () => {
  loading.value = true;
  confirmed.value = true;

  if (props.recording && mediaRecorder.value) {
    mediaRecorder.value.stop();
  }

  if (durationCounter.value) {
    clearInterval(durationCounter.value);
  }

  if (stream.value) {
    const tracks = stream.value.getTracks();
    tracks.forEach((track) => track.stop());
  }

  stream.value = null;
};

const handleCancel = async () => {
  stopRecording();
  emit("cancel");
};

// Watch for recording changes
watch(
  () => props.recording,
  (newVal) => {
    if (newVal) {
      startRecording();
    } else {
      stopRecording();
    }
  }
);

// Resize observer
let resizeObserver: ResizeObserver;

onMounted(() => {
  if (containerRef.value) {
    resizeObserver = new ResizeObserver(() => {
      VISUALIZER_BUFFER_LENGTH = Math.floor(window.innerWidth / 4);
      if (visualizerData.value.length > VISUALIZER_BUFFER_LENGTH) {
        visualizerData.value = visualizerData.value.slice(
          visualizerData.value.length - VISUALIZER_BUFFER_LENGTH
        );
      } else {
        visualizerData.value = Array(
          VISUALIZER_BUFFER_LENGTH - visualizerData.value.length
        )
          .fill(0)
          .concat(visualizerData.value);
      }
      containerWidth.value = containerRef.value?.clientWidth || 0;
    });

    resizeObserver.observe(document.body);
    containerWidth.value = containerRef.value.clientWidth;

    startRecording();
  }
});

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }

  stopRecording();

  if (durationCounter.value) {
    clearInterval(durationCounter.value);
  }
});
</script>

<template>
  <div
    ref="containerRef"
    :class="[
      loading ? 'bg-gray-100/50 dark:bg-gray-850/50' : '',
      'rounded-full flex justify-between',
      className,
    ]"
  >
    <div class="flex items-center mr-1">
      <button
        type="button"
        :class="[
          'p-1.5 rounded-full',
          loading
            ? 'bg-gray-200 dark:bg-gray-700/50'
            : 'bg-[#3a7ca5]/20 text-[#3a7ca5] dark:text-[#3a7ca5]',
        ]"
        @click="handleCancel"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="3"
          stroke="currentColor"
          class="size-4"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M6 18 18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <div
      class="flex flex-1 self-center items-center justify-between ml-2 mx-1 overflow-hidden h-6"
      dir="rtl"
    >
      <div
        class="flex items-center gap-0.5 h-6 w-full max-w-full overflow-hidden overflow-x-hidden flex-wrap"
      >
        <div
          v-for="(rms, index) in visualizerData.slice().reverse()"
          :key="index"
          class="flex items-center h-full"
        >
          <div
            :class="[
              'w-[2px] shrink-0 inline-block h-full',
              loading
                ? 'bg-gray-500 dark:bg-gray-400'
                : 'bg-[#3a7ca5] dark:bg-[#3a7ca5]',
            ]"
            :style="{ height: `${Math.min(100, Math.max(14, rms * 100))}%` }"
          />
        </div>
      </div>
    </div>

    <div class="flex">
      <div class="mx-1.5 pr-1 flex justify-center items-center">
        <div
          :class="[
            'text-sm font-medium flex-1 mx-auto text-center',
            loading ? 'text-gray-500 dark:text-gray-400' : 'text-[#3a7ca5]',
          ]"
        >
          {{ formatSeconds(durationSeconds) }}
        </div>
      </div>

      <div class="flex items-center">
        <div
          v-if="loading"
          class="text-gray-500 rounded-full cursor-not-allowed"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
          >
            <g class="spinner_OSmW">
              <rect x="11" y="1" width="2" height="5" opacity=".14" />
              <rect
                x="11"
                y="1"
                width="2"
                height="5"
                transform="rotate(30 12 12)"
                opacity=".29"
              />
              <rect
                x="11"
                y="1"
                width="2"
                height="5"
                transform="rotate(60 12 12)"
                opacity=".43"
              />
              <rect
                x="11"
                y="1"
                width="2"
                height="5"
                transform="rotate(90 12 12)"
                opacity=".57"
              />
              <rect
                x="11"
                y="1"
                width="2"
                height="5"
                transform="rotate(120 12 12)"
                opacity=".71"
              />
              <rect
                x="11"
                y="1"
                width="2"
                height="5"
                transform="rotate(150 12 12)"
                opacity=".86"
              />
              <rect
                x="11"
                y="1"
                width="2"
                height="5"
                transform="rotate(180 12 12)"
              />
            </g>
          </svg>
        </div>
        <button
          v-else
          type="button"
          class="p-1.5 bg-[#3a7ca5] text-white dark:bg-[#3a7ca5] dark:text-blue-950 rounded-full"
          @click="confirmRecording"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="2.5"
            stroke="currentColor"
            class="size-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="m4.5 12.75 6 6 9-13.5"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.visualizer {
  display: flex;
  height: 100%;
}

.visualizer-bar {
  width: 2px;
  background-color: #3a7ca5;
}

.spinner_OSmW {
  transform-origin: center;
  animation: spinner_T6mA 0.75s step-end infinite;
}
@keyframes spinner_T6mA {
  8.3% {
    transform: rotate(30deg);
  }
  16.6% {
    transform: rotate(60deg);
  }
  25% {
    transform: rotate(90deg);
  }
  33.3% {
    transform: rotate(120deg);
  }
  41.6% {
    transform: rotate(150deg);
  }
  50% {
    transform: rotate(180deg);
  }
  58.3% {
    transform: rotate(210deg);
  }
  66.6% {
    transform: rotate(240deg);
  }
  75% {
    transform: rotate(270deg);
  }
  83.3% {
    transform: rotate(300deg);
  }
  91.6% {
    transform: rotate(330deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
