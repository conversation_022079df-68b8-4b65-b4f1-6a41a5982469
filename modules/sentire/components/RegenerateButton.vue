<script setup lang="ts">
import { RefreshCcwIcon } from "lucide-vue-next";
import { useCurrentThreadStore } from "~/modules/sentire/stores/currentThreadStore";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "~/components/ui/tooltip";

const currentThreadStore = useCurrentThreadStore();
const { status } = storeToRefs(currentThreadStore);

const emit = defineEmits<{
  (e: "regenerate"): void;
}>();
</script>

<template>
  <div class="flex items-center gap-2">
    <Tooltip>
      <TooltipTrigger as-child>
        <RefreshCcwIcon
          :size="16"
          :class="{
            hidden: status !== 'idle' && status !== 'done',
            block: status === 'idle' || status === 'done',
          }"
          class="cursor-pointer text-stone-400 hover:text-stone-500 hover:bg-stone-100 rounded-md p-2 transition-all duration-200 w-8 h-8 focus:outline-none focus:ring-0 focus:border-none"
          @click="emit('regenerate')"
        />
      </TooltipTrigger>
      <TooltipContent>
        <p>Rerun</p>
      </TooltipContent>
    </Tooltip>
  </div>
</template>
