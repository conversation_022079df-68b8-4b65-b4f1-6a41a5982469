<script setup lang="ts">
import Markdown from "@/components/Markdown.vue";
import { useCurrentThreadStore } from "../stores/currentThreadStore";
import InteractiveToolCall from "./InteractiveToolCall.vue";
import StandardToolCall from "@/components/StandardToolCall.vue";
import type { TextMessage, ToolCallMessage } from "~/types/message";
import {
  isText,
  isInteractiveToolCallStart,
  isNonInteractiveToolCall,
  isArtifactReportToolCall,
  isRetrieveProcessTool,
} from "@/hooks/agent/message";
import { extractAndRemoveSummary } from "@/modules/sentire/helpers/message";
import ArtifactReportToolCall from "./ArtifactReportToolCall.vue";
import { useScrollPadding } from "../hooks/useScrollPadding";
import HiddenToolCall from "@/components/HiddenToolCall.vue";

const currentThreadStore = useCurrentThreadStore();
const { finalContent, isContentGenerationPhase } =
  storeToRefs(currentThreadStore);

const { scrollPaddingRef } = useScrollPadding();
const minHeight = computed(() => {
  return isContentGenerationPhase.value
    ? `calc(100vh - ${scrollPaddingRef.value}px)`
    : undefined;
});
</script>

<template>
  <div
    :id="`${slugify('deep analysis block')}`"
    :style="{
      minHeight: minHeight,
    }"
  >
    <div v-for="(message, messageIndex) in finalContent" :key="messageIndex">
      <div v-if="isText(message)">
        <Markdown
          :source="extractAndRemoveSummary((message as TextMessage).text).textWithoutSummary"
          class="prose prose-sentire-base prose-stone"
        />
      </div>
      <InteractiveToolCall
        v-if="isInteractiveToolCallStart(message)"
        :question="(message as ToolCallMessage).args.question as string"
        :type="(message as ToolCallMessage).args.type as string"
        :options="(message as ToolCallMessage).args.options as string[]"
        class="my-6"
        @submit="(value) => currentThreadStore.confirmInteractiveResult((message as ToolCallMessage).toolName, value)"
      />
      <ArtifactReportToolCall
        v-if="isArtifactReportToolCall(message)"
        :report-id="(message as ToolCallMessage).result?.id  as string"
        :status="(message as ToolCallMessage).result?.status as string"
        :title="(message as ToolCallMessage).result?.title as string"
        :message="(message as ToolCallMessage).result?.message as string"
        class="my-6"
      />
      <HiddenToolCall v-else-if="isRetrieveProcessTool(message)" />
      <StandardToolCall
        v-else-if="isNonInteractiveToolCall(message)"
        :name="(message as ToolCallMessage).toolName"
        :input="(message as ToolCallMessage).args"
        :output="(message as ToolCallMessage).result as Record<string, unknown>"
        :elapsed-time="(message as ToolCallMessage).elapsedTime"
        class="my-6"
      />
    </div>
    <slot />
  </div>
</template>
