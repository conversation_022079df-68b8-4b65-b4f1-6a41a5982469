<script setup lang="ts">
import { getLinkDotClass } from "./HistoryAnalysis/util";
defineProps<{
  linkId: string;
  selected: boolean;
  checkStatus: string;
  checkResult: string;
  checkPercent?: number;
}>();

const emit = defineEmits<{
  (e: "select", linkId: string): void;
}>();
</script>

<template>
  <div
    class="progress-bar-container flex items-center border rounded-lg py-1 px-2 cursor-pointer text-theme-color my-2 hover:bg-[#d8e1e6] transition-colors"
    :class="{
      'border-[#ecf2f6] bg-[#ecf2f6]': selected,
      'bg-[#fafafa] border-[#fafafa]': !selected,
    }"
    style="position: relative; overflow: hidden; min-height: 32px"
    @click="() => emit('select', linkId)"
  >
    <div
      class="progress-bar"
      :style="`
        width: ${checkPercent}%;
        background: ${selected ? '#d8e5ed' : '#f0f0f0'};
      `"
    />
    <div
      class="progress-content flex items-center w-full h-full absolute left-0 top-0 px-2"
    >
      <div
        class="relative flex items-center justify-center mr-2"
        style="width: 16px; height: 16px"
      >
        <template v-if="checkStatus === 'checking'">
          <div
            class="absolute animate-pulse-ring bg-[#d9d9d9] rounded-full size-4"
          />
          <div class="size-2 rounded-full animate-spin bg-checking-text" />
        </template>
        <template v-else>
          <div
            class="size-2 rounded-full"
            :class="getLinkDotClass(checkResult)"
          />
        </template>
      </div>
      <span>{{ linkId }}</span>
    </div>
  </div>
</template>

<style scoped>
.progress-bar-container {
  position: relative;
  height: 100%;
  overflow: hidden;
}
.progress-bar {
  border-radius: 6px;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  transition: width 0.3s ease, background 0.3s;
  z-index: 1;
  pointer-events: none;
}
.progress-content {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  display: flex;
  align-items: center;
  pointer-events: auto;
}
</style>
