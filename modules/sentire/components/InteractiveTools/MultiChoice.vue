<script setup lang="ts">
import { Checkbox } from "@/components/ui/checkbox";
import { ref } from "vue";
import { Button } from "@/components/ui/button";

const props = defineProps<{
  options: string[];
}>();

const formData = ref<Record<string, boolean>>(
  props.options.reduce((acc, option) => {
    acc[option] = false;
    return acc;
  }, {} as Record<string, boolean>)
);

const emit = defineEmits<{
  (e: "submit", option: string[]): void;
}>();

const handleSubmit = () => {
  const selectedOptions: string[] = [];
  Object.entries(formData.value).forEach(([option, selected]) => {
    if (selected) {
      selectedOptions.push(option);
    }
  });

  emit("submit", selectedOptions);
};
</script>

<template>
  <form class="flex flex-col gap-y-2" @submit.prevent="handleSubmit">
    <div
      v-for="option in options"
      :key="option"
      class="flex items-center space-x-2"
    >
      <Checkbox :id="option" v-model="formData[option]" />
      <label
        :for="option"
        class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      >
        {{ option }}
      </label>
    </div>
    <Button type="submit" class="w-fit">Submit</Button>
  </form>
</template>
