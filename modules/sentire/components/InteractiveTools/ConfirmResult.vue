<script setup lang="ts">
import { But<PERSON> } from "@/components/ui/button";

const emit = defineEmits<{
  (e: "submit", value: string[]): void;
}>();

const handleSubmit = (value: string) => {
  emit("submit", [value]);
};
</script>

<template>
  <div class="flex gap-x-2">
    <Button class="cursor-pointer" @click="handleSubmit('yes')">Yes</Button>
    <Button
      class="cursor-pointer"
      variant="outline"
      @click="handleSubmit('no')"
    >
      No
    </Button>
  </div>
</template>
