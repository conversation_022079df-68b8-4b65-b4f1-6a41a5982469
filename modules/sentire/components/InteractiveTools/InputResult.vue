<script setup lang="ts">
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ref } from "vue";

const formData = ref<{ userInput: string }>({ userInput: "" });
const emit = defineEmits<{
  (e: "submit", value: string[]): void;
}>();

const handleSubmit = () => {
  emit("submit", [formData.value.userInput.trim()]);
};
</script>

<template>
  <form class="flex items-center gap-x-2" @submit.prevent="handleSubmit">
    <Input v-model="formData.userInput" />
    <Button>Submit</Button>
  </form>
</template>
