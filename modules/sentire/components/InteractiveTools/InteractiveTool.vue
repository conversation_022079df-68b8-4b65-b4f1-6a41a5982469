<script setup lang="ts">
import SingleChoice from "./SingleChoice.vue";
import MultiChoice from "./MultiChoice.vue";
import ConfirmResult from "./ConfirmResult.vue";
import InputResult from "./InputResult.vue";

const props = defineProps<{
  type: string;
  options: string[];
}>();

const emit = defineEmits<{
  (e: "submit", value: string[]): void;
}>();
</script>

<template>
  <SingleChoice
    v-if="props.type === 'single-choice'"
    :options="props.options as string[]"
    @submit="(value) => emit('submit', value)"
  />
  <MultiChoice
    v-if="props.type === 'multi-choice'"
    :options="props.options as string[]"
    @submit="(value) => emit('submit', value)"
  />
  <ConfirmResult
    v-if="props.type === 'confirm'"
    :options="props.options as string[]"
    @submit="(value) => emit('submit', value)"
  />
  <InputResult
    v-if="props.type === 'input'"
    :options="props.options as string[]"
    @submit="(value) => emit('submit', value)"
  />
</template>
