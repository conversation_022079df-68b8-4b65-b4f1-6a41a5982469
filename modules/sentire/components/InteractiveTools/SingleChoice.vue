<script setup lang="ts">
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

defineProps<{
  options: string[];
}>();

defineEmits<{
  (e: "submit", option: string[]): void;
}>();
</script>

<template>
  <RadioGroup>
    <div
      v-for="option in options"
      :key="option"
      class="flex items-center space-x-2 cursor-pointer"
    >
      <RadioGroupItem
        :id="option"
        :value="option"
        class="border-stone-500"
        @click="() => $emit('submit', [option])"
      />
      <Label :for="option">{{ option }}</Label>
    </div>
  </RadioGroup>
</template>
