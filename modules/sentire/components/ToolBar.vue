<script setup lang="ts">
import RegenerateButton from "./RegenerateButton.vue";
import CopyButton from "@/components/CopyButton.vue";
import type { TextMessage, ToolCallMessage } from "@/types/message";

const props = withDefaults(
  defineProps<{
    copyData?: Array<TextMessage | ToolCallMessage>;
    copyShow?: boolean;
    showRegenerate?: boolean;
    onRegenerate?: () => void;
    status: string;
  }>(),
  {
    copyData: () => [],
    copyShow: true,
    showRegenerate: true,
    onRegenerate: undefined,
  }
);

const emit = defineEmits<{
  (e: "regenerate"): void;
}>();

const handleRegenerate = () => {
  if (props.onRegenerate) {
    props.onRegenerate();
  } else {
    emit("regenerate");
  }
};
</script>

<template>
  <div class="flex items-center gap-2">
    <RegenerateButton v-if="showRegenerate" @regenerate="handleRegenerate" />
    <CopyButton
      :copy-data="copyData"
      :copy-show="copyShow"
      :is-tool-bar="true"
      :status="props.status"
    />
  </div>
</template>
