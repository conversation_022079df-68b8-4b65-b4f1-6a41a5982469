import { Charset } from "flexsearch";

export const searchOptios = {
  suggest: true,
  enrich: true,
  highlight: "<span class='completion-highlight'>$1</span>",
  pluck: "content",
};

export const getFlexSearchOptions = (lang: string) => {
  if (lang === "zh") {
    return {
      cache: true,
      document: {
        store: true,
        index: [
          {
            field: "content",
            tokenize: "full",
            encoder: Charset.Exact,
          },
        ],
      },
    };
  }
  return {
    cache: true,
    document: {
      store: true,
      index: [
        {
          field: "content",
          preset: "match",
          tokenize: "forward",
        },
      ],
    },
  };
};
