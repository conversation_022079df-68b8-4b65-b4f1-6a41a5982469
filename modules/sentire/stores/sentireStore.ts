import { defineStore } from "pinia";
import { ref } from "vue";
import { DEFAULT_CUSTOM_API_ENDPOINT } from "../const";
import type { Metadata } from "../types";

export const useSentireStore = defineStore("sentire", () => {
  const historyThreadsSheetOpened = ref(false);
  const { data: metadata } = useAsyncData("metadata", () =>
    $fetch<Metadata[]>(`${DEFAULT_CUSTOM_API_ENDPOINT}/metadata`)
  );
  const allApps = computed(() =>
    (metadata.value ?? [])
      .filter((item) => item.entity_type === "app")
      .map((item) => item.name)
  );
  const allSites = computed(() =>
    (metadata.value ?? [])
      .filter((item) => item.entity_type === "site")
      .map((item) => item.name)
  );
  const allLinks = computed(() =>
    (metadata.value ?? [])
      .filter((item) => item.entity_type === "link")
      .map((item) => item.name)
  );

  const openHistoryThreadsSheet = () => {
    historyThreadsSheetOpened.value = true;
  };
  const closeHistoryThreadsSheet = () => {
    historyThreadsSheetOpened.value = false;
  };

  const $reset = () => {};

  return {
    // State
    allApps,
    allLinks,
    allSites,
    historyThreadsSheetOpened,

    // Actions
    openHistoryThreadsSheet,
    closeHistoryThreadsSheet,
    $reset,
  };
});
