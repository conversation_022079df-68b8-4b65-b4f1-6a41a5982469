import type { StorageThreadType } from "@mastra/core";
import dayjs from "dayjs";
import localizedFormat from "dayjs/plugin/localizedFormat";
import { toast } from "vue-sonner";
import { DIAGNOSTIC_AGENT } from "../const";
import { Threads } from "../models/threads";
import { useSentireStore } from "./sentireStore";

dayjs.extend(localizedFormat);

export const useThreadsStore = defineStore("threads", () => {
  const keyword = ref<string[]>([]);
  const sentireStore = useSentireStore();
  const { historyThreadsSheetOpened } = storeToRefs(sentireStore);
  const resourceId = useCookie("resourceId");

  const {
    data: threads,
    refresh,
    pending,
  } = useAsyncData(
    "threads",
    async () => {
      const threads = new Threads();
      if (!resourceId.value) {
        throw new Error("resourceId not found");
      }
      return await threads.listThreads(
        DIAGNOSTIC_AGENT,
        resourceId.value,
        keyword.value
      );
    },
    {
      server: false,
      watch: [historyThreadsSheetOpened, keyword],
      deep: false,
      dedupe: "cancel",
    }
  );

  const deleteThread = async (threadId: string) => {
    try {
      const threads = new Threads();
      await threads.deleteThread(threadId);
      await refresh();
    } catch (e: unknown) {
      toast.error("Error", {
        description: `Failed to delete thread: ${e}`,
      });
    }
  };

  const timeGroupedThreads = computed(() => {
    const result: Record<string, StorageThreadType[]> = {};

    const sortedThreads =
      threads.value?.sort((a, b) => {
        return -(
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
      }) ?? [];

    for (const thread of sortedThreads) {
      const ts = dayjs(thread.createdAt);
      let groupKey = "";

      if (ts.isSame(dayjs(), "day")) {
        groupKey = "Today";
      } else if (ts.isSame(dayjs().subtract(1, "day"), "day")) {
        groupKey = "Yesterday";
      } else {
        groupKey = dayjs().format("L");
      }

      if (!result[groupKey]) {
        result[groupKey] = [];
      }

      result[groupKey].push(thread);
    }
    return result;
  });

  const createThread = async () => {
    const resourceId = useCookie("resourceId");
    if (!resourceId.value) {
      throw new Error("resourceId not found");
    }
    const threads = new Threads();
    return await threads.createThread(undefined, undefined, resourceId.value);
  };

  const updateThreadTitle = async (threadId: string, title: string) => {
    const threads = new Threads();
    await threads.updateThreadTopic(threadId, title);
  };

  const updateKeyword = (_keyword: string[]) => {
    keyword.value = _keyword;
  };

  const $reset = () => {};

  return {
    // State
    timeGroupedThreads,
    pending,
    keyword,

    // Actions
    $reset,
    createThread,
    deleteThread,
    updateThreadTitle,
    updateKeyword,
  };
});
