import { defineStore } from "pinia";
import type { Subscription } from "rxjs";
import { toast } from "vue-sonner";
import { coreMessageToClientMessage } from "~/hooks/agent/message";
import { getDefaultRuntimeContext, useAgent } from "~/hooks/agent/useAgent";
import type { Message } from "~/types/message";
import type { Clue, ClueWithData } from "~/types/sentire/clue";
import { DIAGNOSTIC_AGENT } from "../const";
import { getMastraClient, getSuggestionAgent } from "../helpers";
import { clientMessageToHistoryAnalysis } from "../helpers/message";
import { useStreamProcessor } from "../hooks/useStreamProcessor";
import type { NetworkLinks, Suggestions, UserInput } from "../types";
import { suggestionsSchema } from "../types";

export const useCurrentThreadStore = defineStore("currentThread", () => {
  const isNewTurn = ref(true);
  const isFirstLoad = ref(true);
  const networkLinks = ref<NetworkLinks>({});
  const waitingForSuggestion = ref(false);
  const enableDiagnostic = ref(false);
  const lastUserQuestion = ref<UserInput | null>(null);
  const isRunning = ref(false);
  const startSubscription = ref(false);
  const doingDeepAnalysis = ref(false);
  const suggestions = ref<Suggestions>([]);
  const artifactReport = ref<{ reportId: string } | null>(null);
  const streamProcessor = useStreamProcessor();
  const diagnosticAgent = useAgent(
    DIAGNOSTIC_AGENT,
    () => streamProcessor,
    undefined,
    async () => {
      startSubscription.value = true;
    }
  );
  const understandingStepsCollapsed = ref(false);
  const historyMessages = ref<Message[]>([]);

  const { data: threadMessages, pending: isHistoryAnalysesPending } =
    useAsyncData<Message[]>(
      "thread-messages",
      async () => {
        const client = getMastraClient();
        const thread = await client.getMemoryThread(
          diagnosticAgent.threadId.value,
          DIAGNOSTIC_AGENT
        );
        const { messages } = await thread.getMessages();
        return coreMessageToClientMessage(messages);
      },
      {
        server: false,
        watch: [diagnosticAgent.threadId],
      }
    );

  watch(threadMessages, (newThreadMessages) => {
    if (newThreadMessages) {
      historyMessages.value = newThreadMessages;
    }
  });

  const historyAnalyses = computed(() => {
    return clientMessageToHistoryAnalysis(historyMessages.value);
  });

  const initializeWithThreadId = async (threadId: string) => {
    await $reset(threadId);
  };

  const startAnswerUserQuestion = async (
    userInput: UserInput,
    beforeAgentStart?: () => Promise<void>
  ) => {
    const mergedMessages = [
      ...toRaw(historyMessages.value),
      ...toRaw(streamProcessor.messages.value),
    ];

    historyMessages.value = mergedMessages as Message[];

    await $reset();
    await nextTick();

    lastUserQuestion.value = userInput;
    if (isNewTurn.value) {
      isNewTurn.value = false;
    }
    if (isFirstLoad.value) {
      isFirstLoad.value = false;
    }
    try {
      isRunning.value = true;

      await beforeAgentStart?.();

      await diagnosticAgent.start(userInput.question, {
        enableDiagnostic: enableDiagnostic.value,
        entities: userInput.entities,
      });

      waitingForSuggestion.value = true;
      await startSuggestion();
    } catch (e) {
      toast.error("Error", {
        description: `${e}`,
      });
    } finally {
      isRunning.value = false;
      startSubscription.value = false;
      waitingForSuggestion.value = false;
    }
  };

  const cancelAnswerUserQuestion = () => {
    isRunning.value = false;
    diagnosticAgent.cancel();
  };

  const startSuggestion = async (): Promise<Suggestions | undefined> => {
    try {
      console.log("analysisStatus", streamProcessor.analysisStatus.value);
      if (streamProcessor.analysisStatus.value === "canceled") {
        console.warn("analysis is canceled, skip suggestion");
        return;
      }

      waitingForSuggestion.value = true;
      const agent = getSuggestionAgent();
      const context = getDefaultRuntimeContext();
      context.set("chatThreadId", diagnosticAgent.threadId.value);
      const response = await agent.generate({
        messages: [
          {
            role: "user",
            content: [{ type: "text", text: "give me some suggestions" }],
          },
        ],
        runtimeContext: context,
        experimental_output: suggestionsSchema,
      });
      suggestions.value = response.object;
      return suggestions.value;
    } catch (e) {
      toast.error("Error", {
        description: `${e}`,
      });
    } finally {
      waitingForSuggestion.value = false;
    }
  };

  const confirmInteractiveResult = async (
    toolName: string,
    value: string[]
  ) => {
    await diagnosticAgent.confirmInteractiveResult(toolName, value);
  };

  const $reset = async (newThreadId?: string) => {
    //status.value = "idle";
    lastUserQuestion.value = null;
    isNewTurn.value = true;
    artifactReport.value = null;
    networkLinks.value = {};
    doingDeepAnalysis.value = false;
    suggestions.value = [];
    waitingForSuggestion.value = false;
    diagnosticAgent.reset(newThreadId);
    await nextTick();
  };

  const setEnableDiagnostic = (value: boolean) => {
    enableDiagnostic.value = value;
  };

  const initNetworkLinks = (clues: Clue[]) => {
    networkLinks.value = {};
    for (const clue of clues) {
      if (!networkLinks.value[clue.link_id]) {
        networkLinks.value[clue.link_id] = {
          expanded: true,
          selected: false,
          status: "checking",
          result: null,
          clues: [],
        };
      }
      networkLinks.value[clue.link_id].clues.push({
        id: clue.id,
        clueId: clue.clue_id,
        threadId: clue.thread_id,
        packId: clue.pack_id,
        status: "checking",
        result: null,
      });
    }

    const defaultLink = Object.keys(networkLinks.value)[0];
    networkLinks.value[defaultLink].selected = true;
  };
  const updateNetworkLinkPackSelected = (clueTaskIds: number[]) => {
    for (const linkId in networkLinks.value) {
      networkLinks.value[linkId].selectedPackIds = [];
      for (const clue of networkLinks.value[linkId].clues) {
        if (clueTaskIds.find((x) => x === clue.id)) {
          networkLinks.value[linkId].selectedPackIds?.push(clue.packId);
          networkLinks.value[linkId].expanded = false;
        }
      }
    }
  };

  const updateNetworkLinkCheckStatus = (clue: ClueWithData) => {
    if (!networkLinks.value[clue.link_id]) {
      return;
    }

    for (const _clue of networkLinks.value[clue.link_id].clues) {
      if (_clue.clueId === clue.clue_id) {
        _clue.status = "done";
        _clue.result = clue.health.status;
        _clue.clue = clue;
      }
    }

    const allClues = networkLinks.value[clue.link_id].clues;
    if (allClues.some((c) => c.result === "error")) {
      networkLinks.value[clue.link_id].result = "error";
    } else if (allClues.some((c) => c.result === "warning")) {
      networkLinks.value[clue.link_id].result = "warning";
    } else {
      networkLinks.value[clue.link_id].result = "success";
    }

    if (allClues.every((c) => c.status === "done")) {
      networkLinks.value[clue.link_id].status = "done";
    }
  };

  const selectNetworkLink = (linkId: string) => {
    for (const linkId in networkLinks.value) {
      networkLinks.value[linkId].selected = false;
    }
    networkLinks.value[linkId].selected = true;
    networkLinks.value[linkId].expanded = false;
  };

  const doDeepAnalyze = () => {
    doingDeepAnalysis.value = true;
  };

  const setArtifactReport = (reportId: string) => {
    artifactReport.value = { reportId };
  };

  const clearArtifactReport = () => {
    artifactReport.value = null;
  };

  const toggleUnderstandingStepsCollapsed = () => {
    understandingStepsCollapsed.value = !understandingStepsCollapsed.value;
  };

  const resetUnderstandingStepsCollapsed = () => {
    understandingStepsCollapsed.value = false;
  };

  const cleanHistoryMessagesAfter = (index: number) => {
    console.log(
      "cleanHistoryMessagesAfter",
      historyMessages.value.slice(0, index)
    );
    historyMessages.value = historyMessages.value.slice(0, index);
  };

  const isFirstPendingHistoryAnalyses = computed(() => {
    return isFirstLoad.value && isHistoryAnalysesPending.value;
  });

  onMounted(() => {
    isFirstLoad.value = false;
  });

  let subscription: Subscription | null = null;
  watch(startSubscription, (newStartSubscription) => {
    if (newStartSubscription) {
      console.log("start subscription");
      subscription = streamProcessor.getStreamEvents().subscribe((event) => {
        if (event.type === "tool-call") {
          if (event.payload.toolName === "clueDetailTool") {
            const { clueTaskIds } = event.payload.args as {
              clueTaskIds: number[];
            };
            if (clueTaskIds.length > 0) {
              updateNetworkLinkPackSelected(clueTaskIds);
            }
          }
        }
      });
    } else {
      if (subscription) {
        console.log("unsubscribe");
        subscription.unsubscribe();
      }
    }
  });

  return {
    // State
    isRunning,
    waitingForSuggestion,
    suggestions,
    networkLinks,
    userQuestion: lastUserQuestion,
    doingDeepAnalysis,
    enableDiagnostic,
    artifactReport,
    isFirstRun: isNewTurn,
    isFirstPendingHistoryAnalyses,
    understandingStepsCollapsed,
    historyMessages,
    historyAnalyses,

    status: streamProcessor.analysisStatus,
    steps: streamProcessor.steps,
    summary: streamProcessor.summary,
    finalContent: streamProcessor.finalContent,
    plans: streamProcessor.plans,
    isAgentAnalyzing: streamProcessor.isAnalyzing,
    threadId: diagnosticAgent.threadId,
    runId: diagnosticAgent.runId,

    isPlanningPhase: streamProcessor.isPlanningPhase,
    isContentGenerationPhase: streamProcessor.isContentGenerationPhase,

    // Actions
    startSuggestion,
    startAnswerUserQuestion,
    cancelAnswerUserQuestion,
    confirmInteractiveResult,
    initNetworkLinks,
    updateNetworkLinkCheckStatus,
    updateNetworkLinkPackSelected,
    selectNetworkLink,
    doDeepAnalyze,
    initializeWithThreadId,
    setEnableDiagnostic,
    setArtifactReport,
    clearArtifactReport,
    resetUnderstandingStepsCollapsed,
    toggleUnderstandingStepsCollapsed,
    cleanHistoryMessagesAfter,
    $reset,
  };
});
