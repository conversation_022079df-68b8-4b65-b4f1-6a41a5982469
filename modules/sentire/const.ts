export const TOOL_CALL_NAMES = {
  listEntitiesTool:
    "Identifying relevant applications, sites, or links based on your input...",
  askUserTool: "Clarifying intent to ensure accurate guidance...",
  searchLinksTool:
    "Searching for available data sources and telemetry links...",
  lookupCluesTool: "Analyzing collected links to identify relevant clues...",
  drillDownTool:
    "Drilling down into the selected clue for detailed investigation...",
  comparisonTool:
    "Comparing current metrics with the same time window from 24 hours ago...",
  clueDetailTool:
    "Analyzing detailed clue insights and generating diagnostics...",
  inspectEntityTool:
    "Retrieving detailed metadata to better understand the selected entity...",
};

export const DIAGNOSTIC_AGENT = "masterAgent";
export const DEFAULT_RESOURCE_ID = "sentire";
export const DEFAULT_AGENT_URL = "/agent";
export const DEFAULT_CUSTOM_API_ENDPOINT = "/agent/capehorn/api";
