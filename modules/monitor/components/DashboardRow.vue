<script setup lang="ts">
import { COLOR_PALATTE, BORDER_COLOR_PALATTE } from "~/const";

interface Props {
  title: string;
  colorIndex?: number;
}

const props = withDefaults(defineProps<Props>(), {
  colorIndex: () => Math.floor(Math.random() * COLOR_PALATTE.length),
});

const backgroundColor = computed(() => {
  const color = COLOR_PALATTE[props.colorIndex % COLOR_PALATTE.length];
  // 将透明度改为 0.1
  return color.replace(/0\.\d+\)$/, "0.05)");
});
const borderColor = computed(() => {
  const color =
    BORDER_COLOR_PALATTE[props.colorIndex % BORDER_COLOR_PALATTE.length];
  return color.replace(/1\)$/, "0.6");
});
</script>

<template>
  <div
    class="border-1 rounded-lg mb-5 px-3 py-7 transition-all duration-200"
    :style="{
      backgroundColor: backgroundColor,
      borderColor: borderColor,
    }"
  >
    <div class="text-lg font-semibold px-2 py-1 drop-shadow-sm">
      {{ title }}
    </div>
    <slot />
  </div>
</template>
