<script setup lang="ts">
import { Line } from "vue-chartjs";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
} from "chart.js";
import type { Query } from "@cubejs-client/core";
import { useQueryData } from "../hooks/chart/useQueryData";
import { useSparklineOption } from "../hooks/chart/useSparklineOption";
import { RELATIVE_TIME_OPTIONS } from "~/const";

// Define component name to fix linter warning
defineOptions({
  name: "SparklineChart",
});

// Register Chart.js components needed for line charts with area fill and tooltip
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip
);

interface SparklineProps {
  query: Query | null;
  height?: string;
  width?: string;
  timeRange?: string;
}

const props = withDefaults(defineProps<SparklineProps>(), {
  height: "90px",
  width: "100%",
});

const queryRef = computed(() => props.query);
const { resultSet } = useQueryData(queryRef);
const option = useSparklineOption(resultSet);

const timeRangeLabel = computed(() => {
  if (props.timeRange) {
    return RELATIVE_TIME_OPTIONS.find(
      (option) => option.value === props.timeRange
    )?.text;
  }
  return "";
});
</script>

<template>
  <div
    class="sparkline-container"
    :style="{ height: props.height, width: props.width }"
  >
    <Line
      v-if="option.chartData.datasets.length > 0"
      :data="option.chartData"
      :options="option.chartOption"
      :update-mode="'default'"
    />
    <div
      v-else
      class="flex items-center justify-center h-full text-gray-400 text-sm"
    >
      No data
    </div>
    <div class="flex justify-between w-full mt-5">
      <span class="text-xs font-semibold text-muted-foreground">
        {{ option.measureLabel }}</span
      >
      <span class="text-xs font-semibold text-muted-foreground">{{
        timeRangeLabel
      }}</span>
    </div>
  </div>
</template>

<style scoped></style>
