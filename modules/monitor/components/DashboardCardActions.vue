<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child @click.stop>
      <Button
        variant="ghost"
        size="sm"
        class="h-8 w-8 p-0 rounded-full hover:bg-gray-100 data-[state=open]:bg-gray-100 opacity-0 group-hover:opacity-100 data-[state=open]:opacity-100 transition-opacity duration-200"
      >
        <EllipsisVerticalIcon class="h-4 w-4" />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end" class="w-32">
      <DropdownMenuItem
        class="flex items-center gap-2 group transition-colors"
        @click.stop="onEdit"
      >
        <SquarePenIcon class="h-4 w-4" />
        <span class="text-sm">Edit</span>
      </DropdownMenuItem>
      <DropdownMenuItem
        class="flex items-center gap-2 group transition-colors"
        @click.stop="onDelete"
      >
        <Trash2Icon class="h-4 w-4 text-red-500 group-hover:text-red-600" />
        <span class="text-sm text-red-500 group-hover:text-red-600"
          >Delete</span
        >
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>

<script setup lang="ts">
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  SquarePenIcon,
  Trash2Icon,
  EllipsisVerticalIcon,
} from "lucide-vue-next";

defineProps<{ onEdit: () => void; onDelete: () => void }>();
</script>
