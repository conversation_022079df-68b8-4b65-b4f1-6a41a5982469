<script setup lang="ts">
import { useAutoScroll } from "~/hooks/useAutoScroll";
import { useMonitorEditStore } from "../stores/monitorEditStore";
import MessageInput from "./MessageInput.vue";
import Messages from "./Messages.vue";

const monitorStore = useMonitorEditStore();
const { isRunning } = storeToRefs(monitorStore);
const messagesContainerRef = ref();

const { triggerScrollToBottom } = useAutoScroll(
  messagesContainerRef,
  isRunning
);

const sendMessageToAgent = (message: string) => {
  monitorStore.sendMessageToAgent(message);
  nextTick(() => {
    triggerScrollToBottom("instant");
  });
};

onMounted(() => {
  nextTick(() => {
    triggerScrollToBottom("instant");
  });
});
</script>

<template>
  <div class="min-w-96 xl:min-w-[650px] h-full border-r flex flex-col">
    <div
      ref="messagesContainerRef"
      class="flex-1 w-full h-full overflow-y-auto my-2"
    >
      <Messages />
    </div>
    <div class="px-2 pb-2">
      <MessageInput @submit="sendMessageToAgent" />
    </div>
  </div>
</template>
