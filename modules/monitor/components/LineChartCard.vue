<script setup lang="ts">
import { Line } from "vue-chartjs";
import type { ChartData, TooltipItem } from "chart.js";
import type { BarLineData } from "../types";
import { computed, ref, watchEffect } from "vue";
import { getLineChartData } from "../helpers/chartData";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { getValueFormatter } from "~/utils/unit";
import type { UnitNames } from "~/utils/unit";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const props = withDefaults(
  defineProps<{
    data: BarLineData[];
    unit: string;
    title: string;
    chartHeight?: string;
    queryTimeZone?: string;
  }>(),
  {
    chartHeight: "",
    queryTimeZone: "",
  }
);

const chartRef = ref<InstanceType<typeof Line>>();

const chartData = computed(
  () =>
    getLineChartData(props.data, props.queryTimeZone) as ChartData<
      "line",
      number[],
      unknown
    >
);

watchEffect(() => {
  if (chartRef.value?.chart && props.data?.length > 0) {
    const chart = chartRef.value.chart as ChartJS<"line">;
    const newChartData = getLineChartData(
      props.data,
      props.queryTimeZone
    ) as ChartData<"line", number[], unknown>;

    if (JSON.stringify(chart.data) !== JSON.stringify(newChartData)) {
      chart.data.labels = newChartData.labels;
      chart.data.datasets = newChartData.datasets;
      chart.update("none");
    }
  }
});

// 图表配置选项
const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    title: {
      display: true,
      text: props.title,
      align: "start" as const,
      padding: { bottom: 40, top: 10 },
      font: { weight: "bold" as const, size: 16 },
    },
    legend: {
      position: "bottom" as const,
      labels: {
        usePointStyle: true,
        pointStyle: "circle",
        pointStyleWidth: 8,
        boxHeight: 6,
        padding: 20,
        color: "black",
      },
    },
    tooltip: {
      mode: "index" as const,
      intersect: false,
      backgroundColor: "#fff",
      titleColor: "black",
      bodyColor: "black",
      callbacks: {
        labelTextColor: (ctx: TooltipItem<"line">) => {
          const dataset = ctx.dataset;
          return Array.isArray(dataset.borderColor)
            ? dataset.borderColor[0]
            : dataset.borderColor;
        },
        label: (ctx: TooltipItem<"line">) => {
          const formatter = getValueFormatter(props.unit as UnitNames);
          const result = formatter(Number(ctx.parsed.y));

          const value =
            typeof result.value === "number"
              ? result.value.toFixed(2)
              : result.value;
          return ctx.dataset.label
            ? `${ctx.dataset.label}: ${value} ${result.unit}`
            : "";
        },
      },
      borderColor: "rgba(156, 163, 175, 0.5)",
      borderWidth: 1,
      cornerRadius: 8,
      displayColors: false,
    },
  },
  scales: {
    x: {
      display: true,
      grid: {
        color: "rgba(156, 163, 175, 0.2)",
        lineWidth: 1,
      },
      ticks: {
        color: "black",
        maxTicksLimit: 4,
      },
    },
    y: {
      display: true,
      beginAtZero: true,
      grid: {
        color: "rgba(156, 163, 175, 0.2)",
        lineWidth: 1,
      },
      ticks: {
        color: "black",
        maxTicksLimit: 6,
        callback: (tickValue: string | number) => {
          const formatter = getValueFormatter(props.unit as UnitNames);
          const result = formatter(Number(tickValue));
          const label = `${result.value} ${result.unit}`;
          return label.length > 10 ? label.slice(0, 10) + "..." : label;
        },
      },
    },
  },
  interaction: {
    mode: "nearest" as const,
    axis: "x" as const,
    intersect: false,
  },
  elements: {
    point: {
      hoverBackgroundColor: "#fff",
      hoverBorderWidth: 2,
    },
    line: {
      tension: 0.4,
    },
  },
}));
</script>

<template>
  <div
    :style="
      props.chartHeight ? { height: props.chartHeight } : { height: '100%' }
    "
    class="w-full"
  >
    <Line ref="chartRef" :data="chartData" :options="chartOptions" />
  </div>
</template>
