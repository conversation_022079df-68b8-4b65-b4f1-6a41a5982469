<script setup lang="ts">
import { ref } from "vue";
import { RefreshCcw } from "lucide-vue-next";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useRefreshService } from "../services/refreshService";

const refreshOptions = [
  { label: "Off", value: -1 },
  { label: "5s", value: 5 },
  { label: "10s", value: 10 },
  { label: "30s", value: 30 },
  { label: "1m", value: 60 },
];

const props = withDefaults(
  defineProps<{
    value?: number;
  }>(),
  {
    value: 60,
  }
);

const emit = defineEmits<{
  (e: "update:value", value: number): void;
  (e: "manual-refresh" | "interval-refresh"): void;
}>();

const interval = ref(props.value);

const refreshInterval = computed({
  get: () => interval.value,
  set: (value) => {
    interval.value = value;
    emit("update:value", value);
  },
});

const handleRefresh = () => {
  if (allQueriesCompleted.value) {
    triggerManualRefresh("dashboard-manual-button");
  } else {
    triggerCancel("dashboard-manual-button");
  }
};

const timer = ref<NodeJS.Timeout | null>(null);
onMounted(() => {
  timer.value = setInterval(
    () => triggerIntervalRefresh("dashboard-interval-timer"),
    refreshInterval.value * 1000
  );
});

onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});

watch(refreshInterval, async (newInterval, oldInterval) => {
  if (newInterval === -1) {
    if (timer.value) {
      clearInterval(timer.value);
      timer.value = null;
    }
  } else if (newInterval !== oldInterval) {
    if (timer.value) {
      clearInterval(timer.value);
      timer.value = null;
    }
    refreshInterval.value = newInterval;
    timer.value = setInterval(
      () => triggerIntervalRefresh("dashboard-interval-timer"),
      refreshInterval.value * 1000
    );
  }
});

const {
  triggerManualRefresh,
  triggerIntervalRefresh,
  triggerCancel,
  queryStatus$,
} = useRefreshService();

const allQueriesCompleted = ref(true);
const activeQueries = ref(new Set<string>());

let queryStatusSubscription: ReturnType<typeof queryStatus$.subscribe> | null =
  null;

onMounted(() => {
  queryStatusSubscription = queryStatus$.subscribe((statusEvent) => {
    if (statusEvent.isLoading) {
      activeQueries.value.add(statusEvent.componentId);
    } else {
      activeQueries.value.delete(statusEvent.componentId);
    }
    allQueriesCompleted.value = activeQueries.value.size === 0;
  });
});

onUnmounted(() => {
  if (queryStatusSubscription) {
    queryStatusSubscription.unsubscribe();
  }
});
</script>

<template>
  <div class="flex items-center gap-2">
    <Button
      variant="ghost"
      class="px-2 py-1 border rounded flex items-center w-[110px] justify-between"
      @click="handleRefresh"
    >
      <RefreshCcw
        :size="18"
        :class="{ 'animate-spin': !allQueriesCompleted }"
      />
      <span class="ml-1">
        {{ allQueriesCompleted ? "Refresh" : "Cancel" }}
      </span>
    </Button>
    <Select v-model="refreshInterval">
      <SelectTrigger class="w-20">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectItem
            v-for="opt in refreshOptions"
            :key="opt.value"
            :value="opt.value"
          >
            {{ opt.label }}
          </SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  </div>
</template>
