<script setup lang="ts">
import { computed } from "vue";
import { Line } from "vue-chartjs";
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  LineElement,
  PointElement,
  CategoryScale,
  LinearScale,
  Filler,
} from "chart.js";
import type { CommonChartProps } from "./types";
import { useQueryData } from "../../hooks/chart/useQueryData";
import { useStackedLineChartOption } from "../../hooks/chart/useStackedLineChartOption";
import ChartWrapper from "./ChartWrapper.vue";
import { useCubeQueryOption } from "../../hooks/chart/useCubeQueryOption";

ChartJS.register(
  Title,
  Tooltip,
  Legend,
  LineElement,
  PointElement,
  CategoryScale,
  LinearScale,
  Filler
);

const props = defineProps<CommonChartProps>();
const query = computed(() => props.query);
const cubeQuery = useCubeQueryOption(query, "timeseries", props.dateRange);
const { resultSet, loading } = useQueryData(cubeQuery, props.chartId);
const option = useStackedLineChartOption(resultSet);
const chartRef = ref();
watch(loading, () => {
  nextTick(() => {
    chartRef.value?.chart.resize();
  });
});
</script>

<template>
  <ChartWrapper
    :icon="icon"
    :icon-props="iconProps"
    :title="title"
    :loading="loading"
    :tips="tips"
    :color-index="colorIndex"
  >
    <div class="flex-1 min-h-0 w-full">
      <Line
        ref="chartRef"
        :data="option.chartData"
        :options="option.chartOption"
        :update-mode="'default'"
      />
    </div>
  </ChartWrapper>
</template>
