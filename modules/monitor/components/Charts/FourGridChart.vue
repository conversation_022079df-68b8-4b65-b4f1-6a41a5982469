<script setup lang="ts">
import type { CommonChartProps } from "./types";
import { useQueryData } from "../../hooks/chart/useQueryData";
import { useFourGridOption } from "../../hooks/chart/useFourGridOption";
import ChartWrapper from "./ChartWrapper.vue";

const props = defineProps<CommonChartProps>();
const query = computed(() => {
  const query = props.query ?? null;

  if (query) {
    const tableName = (query.dimensions?.[0] ?? query.measures?.[0])?.split(
      "."
    )[0];
    if (query.timeDimensions && !query.timeDimensions[0].dimension) {
      return {
        ...query,
        timeDimensions: [
          {
            ...query.timeDimensions[0],
            dimension: `${tableName}.timestamp`,
          },
        ],
      };
    } else if (!query.timeDimensions) {
      return {
        ...(query ?? {}),
        timeDimensions: [
          {
            dimension: `${tableName}.timestamp`,
            dateRange: props.dateRange,
          },
        ],
      };
    }

    return query;
  }
  return null;
});
const { resultSet, loading } = useQueryData(query, props.chartId);
const fourGridData = useFourGridOption(resultSet);
</script>

<template>
  <ChartWrapper
    :icon="icon"
    :icon-props="iconProps"
    :title="title"
    :loading="loading"
    :tips="tips"
    :color-index="colorIndex"
  >
    <div
      class="mb-4"
      :class="{
        'grid grid-cols-2 gap-4': fourGridData.length === 4,
        'grid grid-cols-1 gap-4': fourGridData.length === 1,
      }"
    >
      <div v-for="item in fourGridData" :key="item.title" class="text-center">
        <div class="text-2xl font-bold text-foreground">
          {{ item.value }} {{ item.unit }}
        </div>
        <div class="text-sm text-muted-foreground">
          {{ item.title }}
        </div>
      </div>
    </div>
  </ChartWrapper>
</template>
