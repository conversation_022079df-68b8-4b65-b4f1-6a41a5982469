<script setup lang="ts">
import type { CommonChartProps } from "./types";
import { useSingleValueOption } from "../../hooks/chart/useSingleValueOption";
import { useQueryData } from "../../hooks/chart/useQueryData";
import ChartWrapper from "./ChartWrapper.vue";

const props = defineProps<CommonChartProps>();
const query = computed(() => props.query);

const { resultSet, loading } = useQueryData(query, props.chartId);
const singleValueData = useSingleValueOption(resultSet);
</script>

<template>
  <ChartWrapper
    :icon="icon"
    :icon-props="iconProps"
    :title="title"
    :loading="loading"
    :tips="tips"
    :color-index="colorIndex"
  >
    <div class="flex items-center justify-center flex-1">
      <div class="text-center">
        <div class="text-2xl font-bold">
          {{ singleValueData.value }} {{ singleValueData.unit }}
        </div>
        <div class="text-sm text-muted-foreground">
          {{ singleValueData.title }}
        </div>
      </div>
    </div>
  </ChartWrapper>
</template>
