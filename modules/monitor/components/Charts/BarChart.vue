<script setup lang="ts">
import { computed } from "vue";
import { Bar } from "vue-chartjs";
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
} from "chart.js";
import type { CommonChartProps } from "./types";
import { useQueryData } from "../../hooks/chart/useQueryData";
import { useBarChartOption } from "../../hooks/chart/useBarChartOption";
import ChartWrapper from "./ChartWrapper.vue";
import { useCubeQueryOption } from "../../hooks/chart/useCubeQueryOption";

ChartJS.register(
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale
);

const props = defineProps<CommonChartProps>();
const query = computed(() => props.query);
const cubeQuery = useCubeQueryOption(query, "timeseries", props.dateRange);
const { resultSet, loading } = useQueryData(cubeQuery, props.chartId);
const option = useBarChartOption(resultSet);
const chartRef = ref();
watch(loading, () => {
  nextTick(() => {
    chartRef.value?.chart.resize();
  });
});
</script>

<template>
  <ChartWrapper
    :icon="icon"
    :icon-props="iconProps"
    :title="title"
    :loading="loading"
    :tips="tips"
    :color-index="colorIndex"
  >
    <div class="flex-1 min-h-0 w-full">
      <Bar
        ref="chartRef"
        :data="option.chartData"
        :options="option.chartOption"
        :update-mode="'default'"
      />
    </div>
  </ChartWrapper>
</template>
