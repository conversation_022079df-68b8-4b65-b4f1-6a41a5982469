<script setup lang="ts">
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RELATIVE_TIME_OPTIONS } from "~/const";

const props = defineProps<{
  value: string;
}>();
const emits = defineEmits<{
  (e: "update:value", value: string): void;
}>();

const shortcut = computed({
  get: () => props.value,
  set: (value) => {
    emits("update:value", value);
  },
});
</script>

<template>
  <div class="gap-2 flex items-center">
    <Select v-model="shortcut">
      <SelectTrigger class="w-40">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectItem
            v-for="sc in RELATIVE_TIME_OPTIONS"
            :key="sc.value"
            :value="sc.value"
          >
            {{ sc.text }}
          </SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  </div>
</template>
