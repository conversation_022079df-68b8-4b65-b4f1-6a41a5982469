<script setup lang="ts">
import MiniCard from "./MiniCard.vue";
import type { Dashboard } from "../types";

const props = defineProps<{
  dashboard: Dashboard;
  timeRange: string;
}>();

const emit = defineEmits<{
  (e: "click"): void;
}>();

const miniCardConfig = computed(() => {
  if (!props.dashboard.miniCardConfig) {
    return null;
  }

  // const singleValue = props.dashboard.miniCardConfig.singleValue;

  // const query = singleValue?.query ?? null;
  // if (query) {
  //   if (query.timeDimensions && !query.timeDimensions[0].dimension) {
  //     const tableName = (query.dimensions?.[0] ?? query.measures?.[0])?.split(
  //       "."
  //     )[0];
  //     singleValue.query = {
  //       ...query,
  //       timeDimensions: [
  //         {
  //           ...query.timeDimensions[0],
  //           dimension: `${tableName}.timestamp`,
  //           dateRange: props.timeRange,
  //         },
  //       ],
  //     };
  //   }
  // }

  // const timeSeries = props.dashboard.miniCardConfig.timeSeries;
  // if (timeSeries && timeSeries.query.timeDimensions) {
  //   timeSeries.query = {
  //     ...timeSeries.query,
  //     timeDimensions: [
  //       {
  //         ...timeSeries.query.timeDimensions[0],
  //         dateRange: props.timeRange,
  //       },
  //     ],
  //   };
  // }

  return {
    title: props.dashboard.name,
    singleValue: props.dashboard.miniCardConfig.singleValue,
    timeSeries: props.dashboard.miniCardConfig.timeSeries,
  };
});
</script>

<template>
  <MiniCard
    :title="dashboard.name"
    :config="miniCardConfig"
    :time-range="timeRange"
    @click="emit('click')"
  >
    <template #actions>
      <slot name="actions" />
    </template>
  </MiniCard>
</template>
