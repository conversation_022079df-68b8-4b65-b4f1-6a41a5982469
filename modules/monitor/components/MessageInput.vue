<script setup lang="ts">
import { useEditor, EditorContent } from "@tiptap/vue-3";
import { StarterKit } from "@tiptap/starter-kit";
import { ref, computed } from "vue";
import {
  ArrowUpIcon,
  XIcon as Close,
  SquareIcon as Square,
} from "lucide-vue-next";
import { useMonitorEditStore } from "../stores/monitorEditStore";

const emit = defineEmits<{
  (e: "submit", value: string): void;
}>();

const props = defineProps<{
  placeholder?: string;
}>();

const monitorEditStore = useMonitorEditStore();
const { isProcessing } = storeToRefs(monitorEditStore);

const isFocused = ref(false);
const isSearch = ref(false);
const activeIndex = ref(-1);

const editor = useEditor({
  content: "",
  extensions: [
    StarterKit.configure({
      bold: false,
      italic: false,
      bulletList: false,
      orderedList: false,
      heading: false,
      listItem: false,
      strike: false,
      code: false,
      codeBlock: false,
    }),
  ],
  editorProps: {
    attributes: {
      class: "w-full min-h-[48px] outline-none",
    },
    handleKeyDown: (view, event) => {
      if (event.key === "Enter") {
        event.preventDefault();
        handleSearch();
        return true;
      }

      return false;
    },
  },
  onFocus: () => {
    isFocused.value = true;
  },
  onBlur: () => {
    isFocused.value = false;
  },
});

const isEmpty = computed(() => {
  if (!editor.value) return true;

  return editor.value.isEmpty;
});

const getUserInput = () => {
  if (!editor.value) {
    return {
      question: "",
      entities: [],
    };
  }

  const userInput = { question: "" };
  editor.value.state.doc.descendants((node) => {
    if (node.type.name === "mention") {
      userInput.question += node.attrs.label;
    } else if (node.type.name === "text") {
      userInput.question += node.text;
    }
  });

  return userInput;
};

const handleSearch = () => {
  if (editor.value && !isEmpty.value) {
    emit("submit", getUserInput().question);
    try {
      editor.value.commands.clearContent();
    } catch (error) {
      console.error("Error clearing content:", error);
    }
  }
};

const handleClear = () => {
  editor.value?.commands.clearContent();
  editor.value?.commands.focus();
  activeIndex.value = -1;
};

const setContent = (content: string) => {
  if (editor.value) {
    editor.value.commands.clearContent();
    editor.value.commands.insertContent(content);
  }
};

const handleGlobalKeydown = (event: KeyboardEvent) => {
  if (["ArrowUp", "ArrowDown", "Enter", "Tab", "Escape"].includes(event.key)) {
    event.preventDefault();
    event.stopPropagation();
  }

  if (event.ctrlKey && (event.key === "k" || event.key === "j")) {
    event.preventDefault();
    event.stopPropagation();
  }

  if (event.key === "Escape") {
    return;
  }

  if (event.key === "Enter" || event.key === "Tab") {
    return;
  }
};

const handleCancel = () => {
  monitorEditStore.cancelAgentResponse();
};

onMounted(() => {
  document.addEventListener("keydown", handleGlobalKeydown);

  editor.value?.commands.focus();
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleGlobalKeydown);
});

defineExpose({
  setContent,
});
</script>

<template>
  <div
    class="relative min-h-12 border rounded-3xl p-2"
    :class="{
      'is-focused': isFocused,
    }"
  >
    <div class="w-full editor-container max-h-64 overflow-y-auto">
      <EditorContent
        :editor="editor"
        :data-placeholder="props.placeholder ?? 'Describe your problem'"
        class="first:before:text-[#bfbfbf] first:before:float-left first:before:pointer-events-none first:before:h-0 w-full min-h-[48px] rounded-2xl p-2"
        :class="{
          'first:before:content-[attr(data-placeholder)]': isEmpty,
        }"
      />
    </div>

    <div class="flex justify-end items-center mt-3">
      <div class="flex gap-2 justify-center">
        <Button
          v-if="!isEmpty && !isSearch"
          color="gray"
          variant="ghost"
          size="sm"
          class="w-8 h-8 text-[#bfbfbf] text-center flex items-center justify-center cursor-pointer hover:bg-[#d9f0f0] hover:text-[#3a7ca5] active:text-[#3a7ca5] transition-colors duration-200 active:bg-[transparent] rounded-full"
          :style="{ visibility: isEmpty ? 'hidden' : 'visible' }"
          @click="handleClear"
        >
          <Close
            theme="outline"
            :size="16"
            class="transition-all duration-300 hover:scale-110 flex items-center"
          />
        </Button>
        <Button
          v-if="!isProcessing"
          color="primary"
          size="sm"
          class="send-button cursor-pointer w-8 h-8 flex items-center justify-center bg-[#bfbfbf] hover:bg-[#8fb2c8] active:bg-[#8fb2c8] transition-all duration-300 rounded-full"
          :class="{
            'is-active': isFocused,
            'is-not-empty': !isEmpty,
          }"
          @click="handleSearch()"
        >
          <ArrowUpIcon
            theme="outline"
            :size="16"
            color="#ffffff"
            class="transition-all duration-300 hover:scale-110 flex items-center"
          />
        </Button>
        <Button
          v-else
          color="primary"
          size="sm"
          class="send-button cursor-pointer w-8 h-8 flex items-center justify-center bg-[#3a7ca5] transition-all duration-300 rounded-full"
          @click="handleCancel"
        >
          <Square
            theme="outline"
            :size="16"
            color="#ffffff"
            class="transition-all duration-300 flex items-center"
          />
        </Button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
$primary-color: #3a7ca5;
$secondary-color: #bfbfbf;
$hover-color: #dcecfa;

.is-focused {
  border: 1px solid $primary-color;
}

.is-not-empty {
  background-color: $primary-color;
}
.is-active {
  color: $primary-color;
}
.search-icon {
  width: 24px;
  height: 24px;
}


.editor-disabled {
  opacity: 0.6 !important;
  pointer-events: none !important;
  cursor: not-allowed !important;
}

:deep(.ProseMirror) {
  outline: none !important;
  border: none !important;
  min-height: 48px;
  line-height: 1.5;

  .ProseMirror-trailingBreak {
    display: none;
  }
}

:deep(.ProseMirror > p) {
  min-height: 48px;
  margin: 0;
}

:deep(.ProseMirror[data-placeholder]:empty:before) {
  content: attr(data-placeholder);
  color: #bfbfbf;
  float: left;
  height: 0;
}
</style>
