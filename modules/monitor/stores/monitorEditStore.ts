import { defineStore } from "pinia";
import type { Subscription } from "rxjs";
import { RELATIVE_TIME_OPTIONS } from "~/const";
import { convertMessagesToStreamEvents } from "~/hooks/agent/message";
import { useAgent } from "~/hooks/agent/useAgent";
import { MONITOR_AGENT } from "~/modules/monitor/const";
import { useMonitorDashboard } from "../hooks/useMonitorDashboard";
import { useStreamProcessor } from "../hooks/useStreamProcessor";
import { useThreadMessages } from "../hooks/useThreadMessages";
import type { DashboardConfig, MiniCardConfig } from "../types";

export const useMonitorEditStore = defineStore("monitorEdit", () => {
  const config = useRuntimeConfig();
  const { dashboard, pending: loadingDashboard } = useMonitorDashboard();
  const streamProcessor = useStreamProcessor();
  const agent = useAgent(
    MONITOR_AGENT,
    () => streamProcessor,
    dashboard.value?.threadId
  );
  const timeRange = ref<string>(RELATIVE_TIME_OPTIONS[7].value);
  const messages = useThreadMessages();
  const isRunning = ref(false);
  const previewDashboardConfig = ref<DashboardConfig | null>(null);
  const previewBoardOpened = ref(false);
  const debugOpened = ref(false);
  const debugCubeQuery = computed(
    () => config.public.debugCubeQuery.toLowerCase() === "true"
  );

  watch(
    dashboard,
    (newDashboard) => {
      if (newDashboard) {
        agent.setThreadId(newDashboard?.threadId ?? "");
        previewDashboardConfig.value = newDashboard.dashConfig;
        previewBoardOpened.value = newDashboard.dashConfig ? true : false;
      }
    },
    {
      immediate: true,
    }
  );

  const sendMessageToAgent = async (message: string) => {
    try {
      isRunning.value = true;
      await agent.start(message, {
        dashboardId: dashboard.value?.id,
        threadId: dashboard.value?.threadId,
        resourceId: dashboard.value?.owner,
      });
    } catch (error: unknown) {
      console.error(error);
    } finally {
      isRunning.value = false;
    }
  };

  const cancelAgentResponse = () => {
    isRunning.value = false;
    agent.cancel();
  };

  watch(messages, async (newMessages) => {
    if (newMessages && (newMessages?.length ?? 0) > 0) {
      streamProcessor.reset({}, { resetMessages: true });

      const events = convertMessagesToStreamEvents(newMessages, undefined);
      events.forEach((event) => {
        streamProcessor.emitEvent(event);
      });
    }
  });

  const $reset = () => {
    previewDashboardConfig.value = null;
    streamProcessor.reset({}, { resetMessages: true });
  };

  let subscription: Subscription | null = null;
  watch(isRunning, (newIsRunning) => {
    if (newIsRunning) {
      console.log("start subscription");
      subscription = streamProcessor.getStreamEvents().subscribe((event) => {
        if (event.type === "tool-call") {
          if (event.payload.toolName === "updateMonitorBoardTool") {
            previewBoardOpened.value = true;
          }
        } else if (event.type === "tool-result") {
          if (event.payload.toolCallId.endsWith("updateMonitorBoardTool")) {
            previewDashboardConfig.value = event.payload
              .result as DashboardConfig;
          } else if (
            event.payload.toolCallId.endsWith("updateMonitorMiniCardTool")
          ) {
            if (dashboard.value) {
              dashboard.value.name = (
                event.payload.result as MiniCardConfig
              ).title;
            }
          }
        }
      });
    } else {
      if (subscription) {
        console.log("unsubscribe");
        subscription.unsubscribe();
      }
    }
  });

  return {
    dashboard,
    messages: streamProcessor.messages,
    previewDashboardConfig,
    previewBoardOpened,
    debugOpened,
    debugCubeQuery,
    timeRange,

    loadingDashboard,
    isProcessing: streamProcessor.isProcessing,
    isDone: streamProcessor.isDone,
    isCanceled: streamProcessor.isCanceled,
    isPending: streamProcessor.isPending,
    isRunning,

    sendMessageToAgent,
    cancelAgentResponse,
    $reset,
  };
});
