import type { Observable } from "rxjs";
import { Subject, merge } from "rxjs";
import {
  debounceTime,
  distinctUntilChanged,
  filter,
  shareReplay,
} from "rxjs/operators";

export type RefreshTriggerType = "manual" | "interval" | "time-range-change";

export interface RefreshEvent {
  type: RefreshTriggerType;
  timestamp: number;
  source?: string;
  eventId: string;
}

export interface CancelEvent {
  timestamp: number;
  source?: string;
}

export interface QueryStatusEvent {
  componentId: string;
  isLoading: boolean;
  timestamp: number;
  refreshEventId?: string;
}

class RefreshService {
  private manualRefreshSubject = new Subject<RefreshEvent>();

  private intervalRefreshSubject = new Subject<RefreshEvent>();

  private timeRangeChangeSubject = new Subject<RefreshEvent>();

  private cancelSubject = new Subject<CancelEvent>();

  private queryStatusSubject = new Subject<QueryStatusEvent>();

  public readonly refreshTrigger$: Observable<RefreshEvent> = merge(
    this.manualRefreshSubject.asObservable(),

    this.intervalRefreshSubject.asObservable(),

    this.timeRangeChangeSubject.asObservable().pipe(
      debounceTime(300),
      distinctUntilChanged((prev, curr) => prev.timestamp === curr.timestamp)
    )
  ).pipe(shareReplay(1));

  public readonly cancelTrigger$: Observable<CancelEvent> = this.cancelSubject
    .asObservable()
    .pipe(shareReplay(1));

  public readonly queryStatus$: Observable<QueryStatusEvent> =
    this.queryStatusSubject.asObservable().pipe(shareReplay(1));

  triggerManualRefresh(source?: string): void {
    const eventId = `manual_${Date.now()}_${Math.random()
      .toString(36)
      .slice(2)}`;
    this.manualRefreshSubject.next({
      type: "manual",
      timestamp: Date.now(),
      source,
      eventId,
    });
  }

  triggerIntervalRefresh(source?: string): void {
    const eventId = `interval_${Date.now()}_${Math.random()
      .toString(36)
      .slice(2)}`;
    this.intervalRefreshSubject.next({
      type: "interval",
      timestamp: Date.now(),
      source,
      eventId,
    });
  }

  triggerTimeRangeChange(source?: string): void {
    const eventId = `timerange_${Date.now()}_${Math.random()
      .toString(36)
      .slice(2)}`;
    this.timeRangeChangeSubject.next({
      type: "time-range-change",
      timestamp: Date.now(),
      source,
      eventId,
    });
  }

  triggerCancel(source?: string): void {
    this.cancelSubject.next({
      timestamp: Date.now(),
      source,
    });
  }

  reportQueryStatus(
    componentId: string,
    isLoading: boolean,
    refreshEventId?: string
  ): void {
    this.queryStatusSubject.next({
      componentId,
      isLoading,
      timestamp: Date.now(),
      refreshEventId,
    });
  }

  getRefreshStream(types: RefreshTriggerType[]): Observable<RefreshEvent> {
    return this.refreshTrigger$.pipe(
      filter((event) => types.includes(event.type))
    );
  }

  destroy(): void {
    this.manualRefreshSubject.complete();
    this.intervalRefreshSubject.complete();
    this.timeRangeChangeSubject.complete();
    this.cancelSubject.complete();
    this.queryStatusSubject.complete();
  }
}

export const refreshService = new RefreshService();

export function useRefreshService() {
  return {
    refreshService,
    refreshTrigger$: refreshService.refreshTrigger$,
    cancelTrigger$: refreshService.cancelTrigger$,
    queryStatus$: refreshService.queryStatus$,
    triggerManualRefresh: (source?: string) =>
      refreshService.triggerManualRefresh(source),
    triggerIntervalRefresh: (source?: string) =>
      refreshService.triggerIntervalRefresh(source),
    triggerTimeRangeChange: (source?: string) =>
      refreshService.triggerTimeRangeChange(source),
    triggerCancel: (source?: string) => refreshService.triggerCancel(source),
    reportQueryStatus: (
      componentId: string,
      isLoading: boolean,
      refreshEventId?: string
    ) =>
      refreshService.reportQueryStatus(componentId, isLoading, refreshEventId),
    getRefreshStream: (types: RefreshTriggerType[]) =>
      refreshService.getRefreshStream(types),
  };
}
