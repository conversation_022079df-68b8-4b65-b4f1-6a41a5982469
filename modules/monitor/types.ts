import type { Query, ResultSet } from "@cubejs-client/core";

export interface TableDataItem {
  [key: string]: string | number | string[] | undefined;
}

export interface CancellableRequest {
  promise: Promise<ResultSet>;
  cancel: () => void;
}

export type ChartType =
  | "line"
  | "bar"
  | "stacked-bar"
  | "pie"
  | "table"
  | "single-value"
  | "four-grid"
  | "multi-axis-line"
  | "stacked-line";

export interface ChartResult {
  type: ChartType;
  data: ChartDataType;
  unit?: string;
  measuresData?: string[];
  measureUnits?: Record<string, string>;
}

export type BarLineData = {
  label: string | number;
  value: string | number;
  category: string;
};

export type ChartDataType = BarLineData[] | TableDataItem[];

export type Layout = {
  x: number;
  y: number;
  w: number;
  h: number;
  i: string;
};

export type Row = {
  title: string;
  layout: Layout[];
};

export type Chart = {
  type: ChartType;
  query: Query;
};

export type Panel = {
  id: string;
  title: string;
  chart: Chart;
  tips?: string;
};

export type DashboardLayout = Panel[];

export type DashboardConfig = {
  title: string;
  rows: Row[];
  panels: Panel[];
};

export type MiniCardConfig = {
  title: string;
  singleValue: {
    query: Query;
  };
  timeSeries: {
    query: Query;
  };
};

export type Dashboard = {
  id: number;
  name: string;
  description?: string;
  miniCardConfig: MiniCardConfig;
  dashConfig: DashboardConfig;
  createdAt: Date;
  updatedAt: Date;
  owner: string;
  threadId: string;
  version: number;
};
