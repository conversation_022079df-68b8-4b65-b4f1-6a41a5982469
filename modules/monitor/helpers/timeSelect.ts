import dayjs from "dayjs";
import { RELATIVE_TIME_OPTIONS } from "~/const";

export function intervalToMs(interval: string): number {
  if (!interval || interval === "" || interval === "off") return 0;
  if (interval === "auto") return 5000;
  const unit = interval.slice(-1);
  const value = parseInt(interval.slice(0, -1));
  switch (unit) {
    case "s":
      return value * 1000;
    case "m":
      return value * 60 * 1000;
    case "h":
      return value * 60 * 60 * 1000;
    case "d":
      return value * 24 * 60 * 60 * 1000;
    default: {
      const numValue = parseInt(interval);
      return isNaN(numValue) ? 0 : numValue * 1000;
    }
  }
}

export function getTimeRange(
  unit: "minute" | "hour" | "day",
  amount: number
): [Date, Date] {
  const end = dayjs().toDate();
  const start = dayjs().subtract(amount, unit).toDate();
  return [start, end];
}

export function getGranularity(
  timeRange: string | [Date, Date] | undefined
): string {
  if (typeof timeRange === "string") {
    const granularity = RELATIVE_TIME_OPTIONS.find(
      (option) => option.value === timeRange
    )?.granularity;
    return granularity ?? "";
  } else if (Array.isArray(timeRange)) {
    const diff = dayjs(timeRange[1]).diff(dayjs(timeRange[0]), "minute");
    if (diff < 60) {
      return "minute";
    } else if (diff < 60 * 24) {
      return "hour";
    } else {
      return "day";
    }
  }
  return "minute";
}
