// import { processDataWithClue } from "./chartTypeHelper";
import cube from "@cubejs-client/core";
import { intervalToMs } from "./timeSelect";
import type { CancellableRequest } from "../types";
import type { Query as DashboardQuery, ResultSet } from "@cubejs-client/core";

class MonitorDataFetcher {
  private baseUrl: string;
  private cubeApi: ReturnType<typeof cube>;
  private activeControllers: Map<string, { abort: () => void }> = new Map();
  private activeSubscriptions: Map<string, { unsubscribe: () => void }> =
    new Map();
  private lastDataCache: Map<string, ResultSet | null> = new Map();
  private refreshIntervals: Map<string, NodeJS.Timeout> = new Map();
  private refreshInterval: string = ""; // 全局刷新间隔设置
  private refreshConfigs: Map<
    string,
    { query?: DashboardQuery; onUpdate?: (data: ResultSet) => void }
  > = new Map(); // 保存每个自动刷新请求的参数

  constructor(apiUrl: string = "/monitor-api") {
    this.baseUrl = apiUrl;
    this.cubeApi = cube("CUBE-API-TOKEN", {
      apiUrl: `${apiUrl}/cubejs-api/v1`,
    });
  }

  // 设置全局刷新间隔
  setRefreshInterval(interval: string) {
    this.refreshInterval = interval;
    this.clearAllIntervals();
    this.restartAllRefreshRequests();
  }

  // 时间间隔字符串转毫秒
  private intervalToMs(interval: string): number {
    return intervalToMs(interval);
  }

  // 清除所有定时器
  private clearAllIntervals() {
    this.refreshIntervals.forEach((timer) => clearInterval(timer));
    this.refreshIntervals.clear();
  }

  // 更新定时器间隔
  private restartAllRefreshRequests() {
    const intervalMs = this.intervalToMs(this.refreshInterval);

    if (intervalMs <= 0) return;

    this.refreshConfigs.forEach((config, requestKey) => {
      if (config.query && config.onUpdate) {
        const timer = setInterval(() => {
          if (this.activeControllers.has(requestKey)) {
            this.performFetch(
              requestKey.replace("dashboard-", ""),
              () => false,
              config.query
            )
              .then((result) => {
                if (config.onUpdate) {
                  config.onUpdate(result);
                }
              })
              .catch((error) => {
                console.error(`Refresh error for ${requestKey}:`, error);
              });
          }
        }, intervalMs);

        this.refreshIntervals.set(requestKey, timer);
      }
    });
  }

  // 获取仪表板数据（可取消）
  fetchDashboardData(id: string, query?: DashboardQuery): CancellableRequest {
    const requestKey = `dashboard-${id}`;
    this.cancelRequest(requestKey);
    let cancelled = false;
    const promise = this.performFetch(id, () => cancelled, query);
    const cancel = () => {
      cancelled = true;
      this.activeControllers.delete(requestKey);
    };
    this.activeControllers.set(requestKey, { abort: cancel });
    return { promise, cancel };
  }

  // 获取仪表板数据并支持自动刷新
  fetchDashboardDataWithRefresh(
    id: string,
    query?: DashboardQuery,
    onUpdate?: (data: ResultSet) => void
  ): CancellableRequest {
    const requestKey = `dashboard-${id}`;
    this.cancelRequest(requestKey);
    this.clearInterval(requestKey);

    let cancelled = false;

    // 保存刷新配置
    if (query && onUpdate) {
      this.refreshConfigs.set(requestKey, { query, onUpdate });
    }

    const fetchOnce = async (): Promise<ResultSet> => {
      if (cancelled) {
        throw new Error("Request cancelled");
      }
      try {
        const result = await this.performFetch(id, () => cancelled, query);
        if (!cancelled && onUpdate) {
          onUpdate(result);
        }
        return result;
      } catch (error) {
        if (!cancelled) {
          console.error(`Error fetching data for ${id}:`, error);
        }
        throw error;
      }
    };

    // 立即执行一次
    const promise = fetchOnce();

    // 如果设置了刷新间隔，启动定时器
    const intervalMs = this.intervalToMs(this.refreshInterval);
    if (intervalMs > 0 && onUpdate) {
      const timer = setInterval(() => {
        if (!cancelled) {
          fetchOnce().catch((error) => {
            console.error(`Refresh error for ${id}:`, error);
          });
        }
      }, intervalMs);

      this.refreshIntervals.set(requestKey, timer);
    }

    const cancel = () => {
      cancelled = true;
      this.activeControllers.delete(requestKey);
      this.clearInterval(requestKey);
      this.refreshConfigs.delete(requestKey);
    };

    this.activeControllers.set(requestKey, { abort: cancel });
    return { promise, cancel };
  }

  // 清除特定的定时器
  private clearInterval(requestKey: string) {
    const timer = this.refreshIntervals.get(requestKey);
    if (timer) {
      clearInterval(timer);
      this.refreshIntervals.delete(requestKey);
    }
  }

  // 执行实际的数据获取
  private async performFetch(
    id: string,
    isCancelled: () => boolean,
    query?: DashboardQuery
  ): Promise<ResultSet> {
    try {
      const cubeQuery = {
        ...query,
      };
      const result = await this.cubeApi.load(cubeQuery);
      if (isCancelled()) throw new Error("请求已取消");
      return result;
    } catch (error) {
      if (isCancelled()) throw new Error("请求已取消");
      throw error;
    }
  }

  // 取消特定请求
  cancelRequest(requestKey: string) {
    const controller = this.activeControllers.get(requestKey);
    if (controller) {
      controller.abort();
      this.activeControllers.delete(requestKey);
    }
  }

  // 取消特定订阅
  cancelSubscription(subscriptionKey: string) {
    const subscription = this.activeSubscriptions.get(subscriptionKey);
    if (subscription) {
      subscription.unsubscribe();
      this.activeSubscriptions.delete(subscriptionKey);
      this.lastDataCache.delete(subscriptionKey);
    }
  }
}

export const monitorDataFetcher = new MonitorDataFetcher();

// 获取仪表板数据，支持自定义查询
export function getClueDashboardData(
  id: string,
  query?: DashboardQuery
): CancellableRequest {
  return monitorDataFetcher.fetchDashboardData(id, query);
}

// 获取仪表板数据并支持自动刷新
export function getClueDashboardDataWithRefresh(
  id: string,
  query?: DashboardQuery,
  onUpdate?: (data: ResultSet) => void
): CancellableRequest {
  return monitorDataFetcher.fetchDashboardDataWithRefresh(id, query, onUpdate);
}

// 取消请求
export function cancelClueRequest(id: string) {
  monitorDataFetcher.cancelRequest(`dashboard-${id}`);
}
