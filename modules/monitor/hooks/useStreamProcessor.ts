import {
  Subject,
  merge,
  of,
  shareReplay,
  startWith,
  switchMap,
  takeUntil,
  tap,
  timer,
} from "rxjs";
import { computed, onUnmounted, readonly, ref } from "vue";
import { toast } from "vue-sonner";
import type { StreamEvent } from "~/hooks/agent/types";
import { useMessageStreamProcessor } from "~/hooks/agent/useMessageProcessor";
import type { Message } from "~/types/message";

const AGENT_TEXT_TIMEOUT_MS = 30000;
const AGENT_TOOL_TIMEOUT_MS = 120000;

export interface ChatState {
  messages: Array<Message>;
  status: "idle" | "pending" | "processing" | "done" | "canceled";
}

export interface StreamProcessorOptions {
  onAbort?: (reason: string) => void;
  initialState?: Partial<ChatState>;
}

export const useStreamProcessor = (options: StreamProcessorOptions = {}) => {
  const { onAbort, initialState } = options;
  const destroy$ = new Subject<void>();
  const messageProcessor = useMessageStreamProcessor();
  const chatStatus = ref<ChatState["status"]>(initialState?.status || "idle");

  const getTimeoutConfig = (eventType: string) => {
    switch (eventType) {
      case "text":
        return { duration: AGENT_TEXT_TIMEOUT_MS, type: "text" as const };
      case "tool-call":
        return { duration: AGENT_TOOL_TIMEOUT_MS, type: "tool" as const };
      default:
        return null;
    }
  };

  const createStreamPipeline = () => {
    const baseEvents$ = messageProcessor.getStreamEvents();
    return baseEvents$.pipe(
      switchMap((event) => {
        const currentEvent$ = of(event);
        const timeoutConfig = getTimeoutConfig(event.type);

        if (!timeoutConfig) {
          return currentEvent$;
        }

        const timeoutEvent$ = timer(timeoutConfig.duration).pipe(
          switchMap(() =>
            of({
              type: "timeout" as const,
              payload: {
                timeoutType: timeoutConfig.type,
                error: `${timeoutConfig.type} operation timeout after ${timeoutConfig.duration}ms`,
              },
              timestamp: Date.now(),
            })
          ),
          takeUntil(
            baseEvents$.pipe(
              startWith(null) // 当有新事件时取消超时
            )
          )
        );

        return merge(currentEvent$, timeoutEvent$);
      }),
      takeUntil(destroy$),
      shareReplay(1)
    );
  };

  let streamEventsWithTimeout$ = createStreamPipeline();

  const handleTimeoutEvent = (event: StreamEvent<unknown>) => {
    if (event.type !== "timeout") {
      return;
    }

    const { timeoutType, error } = event.payload;

    console.warn(`${timeoutType} timeout occurred:`, error);

    if (onAbort) {
      onAbort(error || `${timeoutType} timeout`);
    }

    chatStatus.value = "done";

    toast.error("Request timeout", {
      description:
        error || `${timeoutType} operation timeout, please try again`,
    });
  };

  const createProcessedStatePipeline = () => {
    return streamEventsWithTimeout$.pipe(
      tap((event) => {
        switch (event.type) {
          case "step-start":
            chatStatus.value = "processing";
            break;
          case "step-finish":
            break;
          case "timeout":
            handleTimeoutEvent(event);
            break;
          case "error":
            chatStatus.value = "done";
            break;
          case "update-process-status":
            chatStatus.value = event.payload.status;
            break;
        }
      }),
      takeUntil(destroy$)
    );
  };

  let processedState$ = createProcessedStatePipeline();
  let processedStateSubscription = processedState$.subscribe();

  const isProcessing = computed(() => {
    return chatStatus.value === "processing";
  });

  const isPending = computed(() => {
    return chatStatus.value === "pending";
  });

  const isDone = computed(() => {
    return chatStatus.value === "done";
  });

  const isCanceled = computed(() => {
    return chatStatus.value === "canceled";
  });

  const emitEvent = (event: StreamEvent<unknown>) => {
    messageProcessor.emitEvent(event);
  };

  const setState = (state: Partial<ChatState>) => {
    if (state.status !== undefined) {
      chatStatus.value = state.status;
    }
  };

  const reset = (
    newState?: Partial<ChatState>,
    { resetMessages = false }: { resetMessages?: boolean } = {}
  ) => {
    messageProcessor.reset(undefined, { resetMessages });

    processedStateSubscription.unsubscribe();
    streamEventsWithTimeout$ = createStreamPipeline();
    processedState$ = createProcessedStatePipeline();
    processedStateSubscription = processedState$.subscribe();

    chatStatus.value = newState?.status || "idle";
  };

  onUnmounted(() => {
    cleanup();
  });

  const cleanup = () => {
    destroy$.next();
    destroy$.complete();
    messageProcessor.cleanup();
    processedStateSubscription.unsubscribe();
  };

  return {
    // state
    messages: messageProcessor.messages,
    chatStatus: readonly(chatStatus),

    // status computed properties
    isProcessing,
    isPending,
    isDone,
    isCanceled,

    // stream object
    streamEvents$: messageProcessor.getStreamEvents(),

    // actions
    emitEvent,
    setState,
    reset,
    cleanup,
    getStreamEvents: () => messageProcessor.getStreamEvents(),
  };
};
