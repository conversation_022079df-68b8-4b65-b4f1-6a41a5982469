import type { ResultSet } from "@cubejs-client/core";
import type {
  ChartData,
  ChartOptions,
  ScriptableContext,
  TooltipItem,
} from "chart.js";
import { getValueFormatter, type UnitNames } from "~/utils/unit";
import { formatTimeLabel, getMeasureLabel } from "./helpers";

export function useSparklineOption(resultSet: Ref<ResultSet | null>) {
  return computed(() => {
    if (!resultSet.value) {
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: {} as ChartOptions<"line">,
      };
    }

    try {
      const chartPivot = resultSet.value.chartPivot({
        fillMissingDates: false,
      });
      const query = resultSet.value.query();

      if (!chartPivot || chartPivot.length === 0) {
        return {
          chartData: { labels: [], datasets: [] } as ChartData<"line">,
          chartOption: {} as ChartOptions<"line">,
        };
      }

      // Sparkline-specific options - minimal UI, no axes, no legend
      const chartOptions: ChartOptions<"line"> = {
        responsive: true,
        maintainAspectRatio: false,
        resizeDelay: 0,
        animation: {
          duration: 0, // Disable animations for performance
        },
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            enabled: true,
            mode: "nearest",
            intersect: false,
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            titleColor: "#ffffff",
            bodyColor: "#ffffff",
            borderColor: "rgba(255, 255, 255, 0.2)",
            borderWidth: 1,
            cornerRadius: 6,
            displayColors: false,
            padding: 8,
            position: "nearest",
            filter: (tooltipItem: TooltipItem<"line">) => {
              // Only show tooltip if we have valid data
              return (
                tooltipItem.parsed.y !== null &&
                tooltipItem.parsed.y !== undefined
              );
            },
            callbacks: {
              title: (tooltipItems: TooltipItem<"line">[]) => {
                if (tooltipItems.length > 0) {
                  return tooltipItems[0].label || "";
                }
                return "";
              },
              label: (context: TooltipItem<"line">) => {
                const value =
                  typeof context.parsed.y === "number" ? context.parsed.y : 0;

                // Try to get unit from measure metadata
                try {
                  const measures = resultSet.value!.annotation().measures;
                  const query = resultSet.value!.query();
                  if (query.measures && query.measures.length > 0) {
                    const measureKey = query.measures[0];
                    const measureUnit = measures[measureKey]?.meta?.unit;

                    if (measureUnit) {
                      const formatter = getValueFormatter(
                        measureUnit as UnitNames
                      );
                      const result = formatter(Number(value));
                      return `${result.value} ${result.unit}`;
                    }
                  }
                } catch (error) {
                  console.warn("Failed to format tooltip value:", error);
                }

                // Fallback to simple number formatting
                return value.toLocaleString();
              },
            },
          },
        },
        scales: {
          x: {
            display: false, // Hide x-axis
          },
          y: {
            display: false, // Hide y-axis
          },
        },
        elements: {
          point: {
            radius: 5, // Hide data points normally
            hoverRadius: 6, // Show larger point on hover for better visibility
            hoverBorderColor: "#ffffff",
            hoverBorderWidth: 2,
            hoverBackgroundColor: "transparent", // Will be set dynamically in dataset
          },
          line: {
            borderWidth: 2,
            tension: 0.4, // Increased tension for much smoother curves
            hoverBorderWidth: 3, // Thicker line on hover
          },
        },
        // Improve hover interaction - follow points on the curve
        interaction: {
          mode: "point",
          intersect: false,
        },
      };

      if (query.measures?.length === 0) {
        console.warn("No measures found in query");
        return {
          chartData: { labels: [], datasets: [] } as ChartData<"line">,
          chartOption: chartOptions,
        };
      }

      if (
        query.timeDimensions &&
        query.timeDimensions.length > 0 &&
        query.dimensions?.length === 0 &&
        query.measures?.length === 1
      ) {
        const timeDimension = query.timeDimensions[0];
        const measure = query.measures![0];

        const labels: string[] = [];
        const data: number[] = [];

        chartPivot.forEach((item: Record<string, unknown>) => {
          const timeValue = item.x;
          const measureValue = (item[measure] as number) || 0;

          if (timeValue) {
            const formattedTime = formatTimeLabel(
              String(timeValue),
              timeDimension.granularity,
              query.timezone
            );
            labels.push(formattedTime);
            data.push(measureValue);
          }
        });

        // Determine line color based on trend (optional enhancement)
        const firstValue = data[0] || 0;
        const lastValue = data[data.length - 1] || 0;
        const isPositiveTrend = lastValue >= firstValue;
        const lineColor = isPositiveTrend
          ? "rgba(34, 197, 94, 1)"
          : "rgba(239, 68, 68, 1)"; // green-500 : red-500

        // Create canvas gradient for area fill
        const createGradient = (
          ctx: CanvasRenderingContext2D,
          area: { bottom: number; top: number }
        ) => {
          const gradient = ctx.createLinearGradient(
            0,
            area.bottom,
            0,
            area.top
          );
          if (isPositiveTrend) {
            gradient.addColorStop(0, "rgba(34, 197, 94, 0)"); // Transparent at bottom
            gradient.addColorStop(0.5, "rgba(34, 197, 94, 0.1)"); // Light green in middle
            gradient.addColorStop(1, "rgba(34, 197, 94, 0.2)"); // More opaque at top
          } else {
            gradient.addColorStop(0, "rgba(239, 68, 68, 0)"); // Transparent at bottom
            gradient.addColorStop(0.5, "rgba(239, 68, 68, 0.1)"); // Light red in middle
            gradient.addColorStop(1, "rgba(239, 68, 68, 0.2)"); // More opaque at top
          }
          return gradient;
        };

        const chartData: ChartData<"line"> = {
          labels,
          datasets: [
            {
              label: "", // No label needed for sparklines
              data,
              borderColor: lineColor,
              backgroundColor: (context: ScriptableContext<"line">) => {
                const chart = context.chart;
                const { ctx, chartArea } = chart;
                if (!chartArea) {
                  return "transparent";
                }
                return createGradient(ctx, chartArea);
              },
              fill: true, // Enable area fill
              tension: 0.4, // Dataset-level tension for smoother curves
              borderWidth: 2,
              pointRadius: 0, // Ensure no points are shown normally
              pointHoverRadius: 6, // Show point on hover for tooltip
              pointHoverBackgroundColor: lineColor,
              pointHoverBorderColor: "#ffffff",
              pointHoverBorderWidth: 2,
              pointBackgroundColor: lineColor,
              pointBorderColor: "#ffffff",
              pointBorderWidth: 0,
              cubicInterpolationMode: "monotone" as const, // Use monotone cubic interpolation for natural curves
              // Enhance hover effect
              hoverBorderWidth: 3,
              hoverBackgroundColor: lineColor,
            },
          ],
        };

        return {
          measureLabel: getMeasureLabel(resultSet.value, measure),
          chartData,
          chartOption: chartOptions,
        };
      }

      return {
        measure: "",
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: chartOptions,
      };
    } catch (error) {
      console.error("Error processing sparkline data:", error);
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: {} as ChartOptions<"line">,
      };
    }
  });
}
