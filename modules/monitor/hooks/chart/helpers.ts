import type { ResultSet } from "@cubejs-client/core";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);
dayjs.extend(timezone);

export function formatTimeLabel(
  timeValue: string,
  granularity?: string,
  tz?: string
): string {
  const date = tz
    ? dayjs.tz(timeValue, tz).tz(dayjs.tz.guess())
    : dayjs.utc(timeValue).tz(dayjs.tz.guess());

  switch (granularity) {
    case "minute":
      return date.format("MM/D HH:mm");
    case "hour":
      return date.format("MM/D HH:mm");
    case "day":
      return date.format("MMM D");
    case "week":
      return `Week ${date.format("MMM D")}`;
    case "month":
      return date.format("YYYY MMM");
    case "year":
      return date.format("YYYY");
    default:
      return date.format("MM/DD/YYYY");
  }
}

export function getMeasureLabel(resultSet: ResultSet, measure: string): string {
  try {
    const annotation = resultSet.annotation();
    return annotation.measures[measure]?.shortTitle || measure;
  } catch {
    return measure;
  }
}
