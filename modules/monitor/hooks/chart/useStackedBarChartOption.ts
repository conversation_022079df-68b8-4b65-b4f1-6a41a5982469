import type { ResultSet } from "@cubejs-client/core";
import type { ChartData, ChartOptions, TooltipItem } from "chart.js";
import { BORDER_COLOR_PALATTE, COLOR_PALATTE } from "~/const";
import { getValueFormatter, type UnitNames } from "~/utils/unit";
import { formatTimeLabel } from "./helpers";

export function useStackedBarChartOption(resultSet: Ref<ResultSet | null>) {
  return computed(() => {
    if (!resultSet.value) {
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"bar">,
        chartOption: {} as ChartOptions<"bar">,
      };
    }

    try {
      const chartPivot = resultSet.value.chartPivot({
        fillMissingDates: false,
      });
      const query = resultSet.value.query();

      if (!chartPivot || chartPivot.length === 0) {
        return {
          chartData: { labels: [], datasets: [] } as ChartData<"bar">,
          chartOption: {} as ChartOptions<"bar">,
        };
      }

      const chartOptions: ChartOptions<"bar"> = {
        responsive: true,
        maintainAspectRatio: false,
        resizeDelay: 0,
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            titleColor: "white",
            bodyColor: "white",
            borderColor: "rgba(255, 255, 255, 0.1)",
            borderWidth: 1,
            mode: "index",
            intersect: false,
            cornerRadius: 8,
            displayColors: false,
            callbacks: {
              labelTextColor: (ctx: TooltipItem<"bar">) =>
                Array.isArray(ctx.dataset.borderColor)
                  ? ctx.dataset.borderColor[0]
                  : ctx.dataset.borderColor,
            },
          },
        },
        scales: {
          y: {
            beginAtZero: true,
            stacked: true,
            grid: {
              color: "rgba(0, 0, 0, 0.1)",
            },
            ticks: {
              color: "rgba(0, 0, 0, 0.7)",
            },
          },
          x: {
            stacked: true,
            grid: {
              display: false,
            },
            ticks: {
              color: "rgba(0, 0, 0, 0.7)",
            },
          },
        },
      };

      if (query.measures?.length === 0) {
        console.warn("No measures found in query");
        return {
          chartData: { labels: [], datasets: [] } as ChartData<"bar">,
          chartOption: chartOptions,
        };
      }

      // Handle stacked bar chart for time dimension + one dimension + one measure
      if (
        query.timeDimensions &&
        query.timeDimensions.length > 0 &&
        query.dimensions?.length === 1 &&
        query.measures?.length === 1
      ) {
        const timeDimension = query.timeDimensions[0];
        const measure = query.measures![0];

        const timeLabelsSet = new Set<string>();
        const dimensionValuesSet = new Set<string>();

        chartPivot.forEach((item) => {
          // Extract time and dimension values from xValues array
          if (
            item.xValues &&
            Array.isArray(item.xValues) &&
            item.xValues.length >= 2
          ) {
            const timeValue = item.xValues[0];
            const dimensionValue = item.xValues[1];

            if (timeValue) {
              const formattedTime = formatTimeLabel(
                String(timeValue),
                timeDimension.granularity,
                query.timezone
              );
              timeLabelsSet.add(formattedTime);
            }

            if (dimensionValue) {
              dimensionValuesSet.add(String(dimensionValue));
            }
          }
        });

        const timeLabels = Array.from(timeLabelsSet).sort();
        const dimensionValues = Array.from(dimensionValuesSet);

        const datasets = dimensionValues.map((dimValue, index) => {
          const data: number[] = [];

          timeLabels.forEach((timeLabel) => {
            const dataPoint = chartPivot.find(
              (item: Record<string, unknown>) => {
                if (
                  item.xValues &&
                  Array.isArray(item.xValues) &&
                  item.xValues.length >= 2
                ) {
                  const timeValue = item.xValues[0];
                  const dimensionValue = item.xValues[1];

                  const formattedTime = formatTimeLabel(
                    String(timeValue),
                    timeDimension.granularity,
                    query.timezone
                  );

                  return (
                    formattedTime === timeLabel &&
                    String(dimensionValue) === dimValue
                  );
                }
                return false;
              }
            );

            // Get the measure value directly from the item
            const value = dataPoint ? (dataPoint[measure] as number) || 0 : 0;
            data.push(value);
          });

          return {
            label: String(dimValue),
            data,
            backgroundColor: getStackedBarColor(index, dimensionValues.length),
            borderColor: getStackedBarBorderColor(
              index,
              dimensionValues.length
            ),
            borderWidth: 1,
          };
        });

        const chartData: ChartData<"bar"> = {
          labels: timeLabels,
          datasets,
        };

        // Add y-axis label formatting
        if (chartOptions.scales?.y?.ticks) {
          try {
            const measures = resultSet.value.annotation().measures;
            const measureUnit = measures[measure].meta.unit;

            chartOptions.scales!.y.ticks.callback = (
              tickValue: string | number
            ) => {
              const formatter = getValueFormatter(measureUnit as UnitNames);
              const result = formatter(Number(tickValue));
              return `${result.value} ${result.unit}`;
            };
          } catch (error) {
            console.warn("Failed to set y-axis formatter:", error);
          }
        }

        if (chartOptions.plugins?.tooltip?.callbacks) {
          const measures = resultSet.value.annotation().measures;
          const measureUnit = measures[query.measures[0]].meta.unit;

          chartOptions.plugins.tooltip.callbacks.label = (
            ctx: TooltipItem<"bar">
          ) => {
            const value = typeof ctx.parsed.y === "number" ? ctx.parsed.y : 0;
            const label = ctx.dataset.label || "";
            const formatter = getValueFormatter(measureUnit as UnitNames);
            const result = formatter(Number(value));
            return `${label}: ${result.value} ${result.unit}`;
          };
        }

        return {
          chartData,
          chartOption: chartOptions,
        };
      }

      return {
        chartData: { labels: [], datasets: [] } as ChartData<"bar">,
        chartOption: chartOptions,
      };
    } catch (error) {
      console.error("Error processing stacked bar chart data:", error);
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"bar">,
        chartOption: {} as ChartOptions<"bar">,
      };
    }
  });
}

function getStackedBarColor(index: number, _total: number): string {
  return COLOR_PALATTE[index % COLOR_PALATTE.length];
}

function getStackedBarBorderColor(index: number, _total: number): string {
  return BORDER_COLOR_PALATTE[index % BORDER_COLOR_PALATTE.length];
}
