import type { Query, ResultSet } from "@cubejs-client/core";
import type { ChartData, ChartOptions } from "chart.js";
import type { ShallowRef } from "vue";
import { BORDER_COLOR_PALATTE, COLOR_PALATTE } from "~/const";
//import { formatTimeLabel } from "~/modules/monitor/helpers/chartData";
import {
  extractMetaDataTitles,
  getChartData,
} from "~/modules/monitor/helpers/chartTypeHelper";
import type { BarLineData } from "~/modules/monitor/types";
import { getValueFormatter } from "~/utils/unit";
import { formatTimeLabel } from "./helpers";

export function groupMeasuresByUnit(
  measures: Record<string, { shortTitle: string; meta?: { unit?: string } }>
) {
  const result: Record<string, string[]> = {};
  Object.values(measures).forEach((value) => {
    const unit = value?.meta?.unit || "unknown";
    if (!result[unit]) {
      result[unit] = [];
    }
    result[unit].push(value.shortTitle);
  });
  return result;
}

function getMultiAxisLineColor(index: number, _total: number): string {
  return COLOR_PALATTE[index % COLOR_PALATTE.length];
}

function getMultiAxisLineBorderColor(index: number, _total: number): string {
  return BORDER_COLOR_PALATTE[index % BORDER_COLOR_PALATTE.length];
}
interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string;
  borderColor?: string;
  yAxisID?: string;
  borderWidth?: number;
  fill?: boolean;
  tension?: number;
  pointRadius?: number;
  pointHoverRadius?: number;
  pointBackgroundColor?: string;
  pointBorderColor?: string;
  pointBorderWidth?: number;
}
const transformDataForDualAxis = (
  transformedData: BarLineData[],
  groupedMeasures: Record<string, string[]>,
  query: Query
): ChartData & {
  y1TickLabelFormatter: ((tickValue: string | number) => string) | null;
  y2TickLabelFormatter: ((tickValue: string | number) => string) | null;
} => {
  const timeDimension = query.timeDimensions?.[0];
  const labels = [
    ...new Set(
      transformedData.map((item) =>
        formatTimeLabel(
          String(item.label),
          timeDimension?.granularity,
          query.timezone
        )
      )
    ),
  ].sort();
  const datasets: ChartDataset[] = [];
  const categories = [...new Set(transformedData.map((item) => item.category))];
  let y1TickLabelFormatter = null;
  let y2TickLabelFormatter = null;
  categories.forEach((category, index) => {
    let unit = "unknown";
    for (const [u, categoryList] of Object.entries(groupedMeasures)) {
      if (categoryList.includes(category)) {
        unit = u;
        break;
      }
    }
    let dataUnit;
    const formatter = getValueFormatter(unit as UnitNames);
    const data = labels.map((label) => {
      const dataPoint = transformedData.find(
        (item) =>
          formatTimeLabel(
            String(item.label),
            timeDimension?.granularity,
            query.timezone
          ) === label && item.category === category
      );
      const value = dataPoint ? Number(dataPoint.value) : 0;
      dataUnit = formatter(value).unit;
      return formatter(value).value;
    });

    datasets.push({
      label: dataUnit ? `${category} (${dataUnit})` : category,
      data: data as number[],
      backgroundColor: getMultiAxisLineColor(index, categories.length),
      borderColor: getMultiAxisLineBorderColor(index, categories.length),
      borderWidth: 2,
      yAxisID: index === 0 ? "y1" : "y2",
      fill: false,
      tension: 0.4,
      pointRadius: 0,
      pointHoverRadius: 0,
      pointBorderWidth: 0,
      pointBackgroundColor: "transparent",
    });

    if (index === 0) {
      y1TickLabelFormatter = (tickValue: string | number) => {
        const formatter = getValueFormatter(unit as UnitNames);
        const result = formatter(Number(tickValue));
        return `${result.value} ${result.unit}`;
      };
    } else if (index === 1) {
      y2TickLabelFormatter = (tickValue: string | number) => {
        const formatter = getValueFormatter(unit as UnitNames);
        const result = formatter(Number(tickValue));
        return `${result.value} ${result.unit}`;
      };
    }
  });
  return {
    labels,
    datasets,
    y1TickLabelFormatter,
    y2TickLabelFormatter,
  };
};

export function useMultiAxisLineOption(
  resultSet: ShallowRef<ResultSet | null>
) {
  return computed(() => {
    if (!resultSet.value) {
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: {} as ChartOptions<"line">,
      };
    }

    try {
      const metaData = resultSet.value?.annotation();

      const chartOptions: ChartOptions<"line"> = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            titleColor: "white",
            bodyColor: "white",
            borderColor: "rgba(255, 255, 255, 0.1)",
            borderWidth: 1,
            mode: "index",
            intersect: false,
          },
        },
        scales: {
          x: {
            grid: {
              display: false,
            },
            ticks: {
              color: "black",
              maxTicksLimit: 4,
            },
          },
          y1: {
            type: "linear",
            display: true,
            position: "left",
            grid: {
              color: "rgba(0, 0, 0, 0.1)",
            },
            ticks: {
              color: "black",
            },
            beginAtZero: true,
          },
          y2: {
            type: "linear",
            display: true,
            position: "right",
            grid: {
              drawOnChartArea: false,
            },
            ticks: {
              color: "rgba(139, 92, 246, 0.7)",
            },
            beginAtZero: true,
          },
        },
        interaction: {
          mode: "index",
          intersect: false,
        },
      };

      if (metaData) {
        const allTitles = extractMetaDataTitles(metaData);
        const transformedData = getChartData(
          resultSet.value?.chartPivot({
            fillMissingDates: false,
          }) || [],
          allTitles
        );
        const groupedMeasures = groupMeasuresByUnit(metaData?.measures || {});

        if (transformedData) {
          const chartData = transformDataForDualAxis(
            transformedData as BarLineData[],
            groupedMeasures,
            resultSet.value?.query()
          );

          if (chartOptions.scales?.y1?.ticks) {
            if (chartData.y1TickLabelFormatter) {
              chartOptions.scales!.y1.ticks.callback =
                chartData.y1TickLabelFormatter;
            }
          }
          if (chartOptions.scales?.y2?.ticks) {
            if (chartData.y2TickLabelFormatter) {
              chartOptions.scales!.y2.ticks.callback =
                chartData.y2TickLabelFormatter;
            }
          }

          return {
            chartData,
            chartOption: chartOptions,
          };
        }
      }

      return {
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: chartOptions,
      };
    } catch (error) {
      console.error("Error processing dual axis line chart data:", error);
      return {
        chartData: { labels: [], datasets: [] } as ChartData<"line">,
        chartOption: {} as ChartOptions<"line">,
      };
    }
  });
}
