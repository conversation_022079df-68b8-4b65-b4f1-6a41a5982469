import type { ResultSet } from "@cubejs-client/core";
import type { ShallowRef } from "vue";

export function useSingleValueOption(resultSet: ShallowRef<ResultSet | null>) {
  return computed(() => {
    if (!resultSet.value) {
      return { value: 0, unit: "", title: "" };
    }

    try {
      const pivotData = resultSet.value.chartPivot({
        fillMissingDates: false,
      });
      if (!pivotData || pivotData.length === 0) {
        return { value: 0, unit: "", title: "" };
      }

      const query = resultSet.value.query();
      const measure = query.measures?.[0];

      let value = 0;
      if (measure) {
        value = pivotData[0]?.[measure] || 0;
      }

      const { measures } = resultSet.value.annotation();
      const unit = measures[measure ?? ""]?.meta?.unit ?? "";
      const title = measures[measure ?? ""]?.shortTitle ?? "";

      const format = getValueFormatter(unit);
      return { ...format(value), title };
    } catch (error) {
      console.error("Error processing chart data:", error);
      return { value: 0, unit: "", title: "" };
    }
  });
}
