import type { ResultSet } from "@cubejs-client/core";
import type { ChartData, ChartOptions, TooltipItem } from "chart.js";
import { BORDER_COLOR_PALATTE, COLOR_PALATTE } from "~/const";
import { getValueFormatter } from "~/utils/unit";
import { formatTimeLabel, getMeasureLabel } from "./helpers";

// 默认数据集样式
const DEFAULT_DATASET_STYLE = {
  borderWidth: 2,
  fill: false,
  tension: 0.4,
  pointRadius: 0,
  pointHoverRadius: 0,
  pointBorderWidth: 0,
  pointBackgroundColor: "transparent",
};

// 创建空的图表数据
const createEmptyChartData = (): {
  chartData: ChartData<"line">;
  chartOption: ChartOptions<"line">;
} => ({
  chartData: { labels: [], datasets: [] },
  chartOption: {},
});

export function useLineChartOption(resultSet: Ref<ResultSet | null>) {
  return computed(() => {
    if (!resultSet.value) return createEmptyChartData();

    try {
      const chartPivot = resultSet.value.chartPivot({
        fillMissingDates: false,
      });
      const query = resultSet.value.query();
      const metaData = resultSet.value.annotation();

      if (!chartPivot?.length || !query.measures?.length) {
        return createEmptyChartData();
      }

      // 创建图表配置
      const chartOptions: ChartOptions<"line"> = {
        responsive: true,
        maintainAspectRatio: false,
        resizeDelay: 0,
        plugins: {
          legend: {
            display: false,
          },
          tooltip: {
            mode: "index",
            intersect: false,
            titleColor: "white",
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            bodyColor: "black",
            borderColor: "rgba(156, 163, 175, 0.5)",
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: false,
            callbacks: {
              labelTextColor: (ctx: TooltipItem<"line">) =>
                Array.isArray(ctx.dataset.borderColor)
                  ? ctx.dataset.borderColor[0]
                  : ctx.dataset.borderColor,
              // label: (ctx: TooltipItem<"line">) => {
              //   const value =
              //     typeof ctx.parsed.y === "number" ? ctx.parsed.y : 0;
              //   const label = ctx.dataset.label || "";
              //   return `${label}: ${value}`;
              // },
            },
          },
        },
        scales: {
          x: {
            display: true,
            grid: {
              display: false,
              color: "rgba(156, 163, 175, 0.2)",
              lineWidth: 1,
            },
            ticks: { color: "black", maxTicksLimit: 4 },
          },
          y: {
            display: true,
            beginAtZero: true,
            grid: { color: "rgba(156, 163, 175, 0.2)", lineWidth: 1 },
            ticks: { color: "black", maxTicksLimit: 6 },
          },
        },
        interaction: { mode: "nearest", axis: "x", intersect: false },
        elements: {
          point: {
            hoverBackgroundColor: "#fff",
            hoverBorderWidth: 2,
            radius: 0,
            hoverRadius: 0,
          },
          line: { tension: 0.4, borderWidth: 2 },
        },
      };

      const timeDimension = query.timeDimensions?.[0];

      // 获取时间标签
      const getTimeLabels = (): string[] => {
        const labels = chartPivot
          .map((item: Record<string, unknown>) => {
            // Use xValues array if available (index 0 for time), fallback to x field
            let timeValue: string | unknown;
            if (Array.isArray(item.xValues) && item.xValues.length > 0) {
              timeValue = item.xValues[0];
            } else {
              timeValue = item.x;
            }

            return timeValue
              ? formatTimeLabel(
                  String(timeValue),
                  timeDimension?.granularity,
                  query.timezone
                )
              : "";
          })
          .filter(Boolean);
        return [...new Set(labels)].sort();
      };

      const createDataset = (
        label: string,
        data: number[],
        colorIndex: number
      ) => ({
        label: label,
        data,
        backgroundColor: COLOR_PALATTE[colorIndex % COLOR_PALATTE.length],
        borderColor:
          BORDER_COLOR_PALATTE[colorIndex % BORDER_COLOR_PALATTE.length],
        ...DEFAULT_DATASET_STYLE,
        spanGaps: false,
      });

      const findDataPoint = (
        labels: string[],
        measure: string,
        unit: string,
        dimensionKey?: string,
        dimensionValue?: string
      ): { data: number[] } => {
        const data = labels.map((label) => {
          const item = chartPivot.find((pivot: Record<string, unknown>) => {
            // Use xValues array if available (index 0 for time), fallback to x field
            let timeValue: string | unknown;
            if (Array.isArray(pivot.xValues) && pivot.xValues.length > 0) {
              timeValue = pivot.xValues[0];
            } else {
              timeValue = pivot.x;
            }

            const formattedTime = timeValue
              ? formatTimeLabel(String(timeValue), timeDimension?.granularity)
              : "";
            const matchesTime = formattedTime === label;

            let matchesDimension = true;
            if (dimensionKey && dimensionValue) {
              // Check xValues array first, then fallback to direct dimension access
              if (Array.isArray(pivot.xValues) && pivot.xValues.length > 1) {
                matchesDimension = pivot.xValues[1] === dimensionValue;
              } else {
                matchesDimension = pivot[dimensionKey] === dimensionValue;
              }
            }

            return matchesTime && matchesDimension;
          });

          return item ? (item[measure] as number) || 0 : 0;
        });

        return { data };
      };

      const labels = getTimeLabels();
      const datasets: ChartData<"line">["datasets"] = [];

      if (
        query.timeDimensions &&
        query.timeDimensions.length > 0 &&
        query.dimensions?.length === 1 &&
        query.measures.length === 1
      ) {
        const measure = query.measures[0];
        const dimension = query.dimensions[0];
        const measureMeta = metaData?.measures?.[measure];
        const unit = measureMeta?.meta?.unit || "unknown";

        // Extract dimension values from xValues array (index 1 for dimension, index 0 for time)
        const dimensionValues = [
          ...new Set(
            chartPivot
              .map((item) => {
                // Use xValues array if available, fallback to direct dimension access
                if (Array.isArray(item.xValues) && item.xValues.length > 1) {
                  return item.xValues[1] as string;
                }
                return item[dimension] as string;
              })
              .filter(Boolean)
          ),
        ];

        dimensionValues.forEach((dimValue, index) => {
          const result = findDataPoint(
            labels,
            measure,
            unit,
            dimension,
            dimValue
          );
          datasets.push(createDataset(String(dimValue), result.data, index));
        });
      } else {
        query.measures.forEach((measure, index) => {
          const measureMeta = metaData?.measures?.[measure];
          const unit = measureMeta?.meta?.unit || "unknown";
          const result = findDataPoint(labels, measure, unit);
          const label = getMeasureLabel(resultSet.value!, measure);
          datasets.push(createDataset(label, result.data, index));
        });
      }

      if (chartOptions.scales?.y?.ticks) {
        const measures = resultSet.value.annotation().measures;
        const measureUnit = measures[query.measures[0]].meta.unit;

        chartOptions.scales!.y.ticks.callback = (
          tickValue: string | number
        ) => {
          const formatter = getValueFormatter(measureUnit as UnitNames);
          const result = formatter(Number(tickValue));
          return `${result.value} ${result.unit}`;
        };
      }

      if (chartOptions.plugins?.tooltip?.callbacks) {
        const measures = resultSet.value.annotation().measures;
        const measureUnit = measures[query.measures[0]].meta.unit;
        chartOptions.plugins.tooltip.callbacks.label = (
          ctx: TooltipItem<"line">
        ) => {
          const value = typeof ctx.parsed.y === "number" ? ctx.parsed.y : 0;
          const label = ctx.dataset.label || "";
          const formatter = getValueFormatter(measureUnit as UnitNames);
          const result = formatter(Number(value));
          return `${label}: ${result.value} ${result.unit}`;
        };
      }

      return {
        chartData: { labels, datasets },
        chartOption: chartOptions,
      };
    } catch (error) {
      console.error("Error processing line chart data:", error);
      return createEmptyChartData();
    }
  });
}
