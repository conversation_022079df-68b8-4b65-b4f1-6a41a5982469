import type { Query, ResultSet } from "@cubejs-client/core";
import cube from "@cubejs-client/core";
import { isEmpty } from "lodash-es";
import hash from "object-hash";
import { useRefreshService } from "../../services/refreshService";

export function useQueryData(query: Ref<Query | null>, componentId?: string) {
  const config = useRuntimeConfig();
  const resultSet = shallowRef<ResultSet | null>(null);
  const loading = ref(true);
  const error = ref<Error | null>(null);

  const cubeConfig = {
    apiUrl: "/monitor-api/cubejs-api/v1",
    token: (config.public.cubejsToken ?? "sk-empty") as string,
  };

  let currentRequestId: string | null = null;
  let currentAbortController: AbortController | null = null;

  const getQueryId = (query: Query): string => hash(query);

  const loadData = async (queryToLoad: Query) => {
    if (!queryToLoad) return;

    const queryId = getQueryId(queryToLoad);

    if (currentRequestId === queryId && loading.value) {
      console.log("useQueryData is loading, skip this request", queryToLoad);
      return;
    }

    if (currentAbortController && currentRequestId !== queryId) {
      console.log(
        "useQueryData is loading, cancel the previous request",
        queryToLoad
      );
      currentAbortController.abort();
    }

    currentRequestId = queryId;
    currentAbortController = new AbortController();

    loading.value = true;
    error.value = null;

    try {
      const cubeApi = cube(cubeConfig.token, {
        ...cubeConfig,
        signal: currentAbortController.signal,
      });
      const res = await cubeApi.load(queryToLoad);

      if (
        currentRequestId === queryId &&
        !currentAbortController.signal.aborted
      ) {
        resultSet.value = res;
      }
    } catch (err: unknown) {
      if (currentRequestId === queryId) {
        if (err instanceof Error && err.name === "AbortError") {
          console.log("useQueryData is aborted, skip this error", queryToLoad);
          return;
        }
        error.value = err instanceof Error ? err : new Error("load data error");
        //resultSet.value = null;
      }
    } finally {
      if (currentRequestId === queryId) {
        loading.value = false;
        currentAbortController = null;
      }
    }
  };

  let debounceTimer: NodeJS.Timeout | null = null;
  const debouncedLoadData = (query: Query) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    debounceTimer = setTimeout(() => {
      loadData(query);
    }, 300);
  };

  const stopWatcher = watch(
    query,
    (newQuery) => {
      if (newQuery && !isEmpty(newQuery)) {
        debouncedLoadData(newQuery);
      } else {
        resultSet.value = null;
        error.value = null;
      }
    },
    { immediate: true }
  );

  const refresh = async () => {
    if (query.value) {
      currentRequestId = null;
      await loadData(query.value);
    }
  };

  const abort = () => {
    if (currentAbortController) {
      currentAbortController.abort();
    }
  };

  // Try to inject refreshService, fallback to useRefreshService if not provided
  const injectedRefreshService =
    inject<ReturnType<typeof useRefreshService>>("refreshService");
  const refreshService = injectedRefreshService || useRefreshService();
  const { refreshTrigger$, cancelTrigger$, reportQueryStatus } = refreshService;
  let refreshSubscription: ReturnType<typeof refreshTrigger$.subscribe> | null =
    null;
  let cancelSubscription: ReturnType<typeof cancelTrigger$.subscribe> | null =
    null;

  let currentRefreshEventId: string | null = null;

  onMounted(() => {
    refreshSubscription = refreshTrigger$.subscribe((event) => {
      currentRefreshEventId = event.eventId;
      refresh();
    });

    cancelSubscription = cancelTrigger$.subscribe(() => {
      abort();
    });
  });

  watch(loading, (isLoading) => {
    if (componentId) {
      reportQueryStatus(
        componentId,
        isLoading,
        currentRefreshEventId || undefined
      );
    }
  });

  onUnmounted(() => {
    stopWatcher();
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    if (currentAbortController) {
      currentAbortController.abort();
    }
    if (refreshSubscription) {
      refreshSubscription.unsubscribe();
    }
    if (cancelSubscription) {
      cancelSubscription.unsubscribe();
    }
  });

  return {
    resultSet,
    loading,
    error,
    refresh,
    abort,
  };
}
