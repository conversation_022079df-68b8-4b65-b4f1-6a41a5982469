import type { Query } from "@cubejs-client/core";
import { getGranularity } from "../../helpers/timeSelect";

export function useCubeQueryOption(
  query: Ref<Query | null>,
  chartKind: "timeseries" | "stats",
  defaultDateRange?: string
) {
  return computed(() => {
    const _query = query.value;
    if (_query) {
      if (chartKind === "timeseries") {
        if (_query.timeDimensions) {
          return {
            ..._query,
            timeDimensions: [
              {
                ..._query.timeDimensions[0],
                granularity: getGranularity(
                  _query.timeDimensions[0]?.dateRange as unknown as
                    | string
                    | [Date, Date]
                ),
              },
            ],
          };
        } else {
          if (!defaultDateRange) {
            console.warn("no defaultDateRange, skip.");
            return _query;
          }
          console.log("no found timeDimension, set default.");
          // ensure timeDimension is set
          const tableName =
            _query.dimensions?.[0] ?? _query.measures?.[0]?.split(".")[0];
          return {
            ..._query,
            timeDimensions: [
              {
                dimension: `${tableName}.timestamp`,
                dateRange: defaultDateRange,
                granularity: getGranularity(defaultDateRange),
              },
            ],
          };
        }
      }
      return _query;
    }
    return null;
  });
}
