import { getGranularity } from "../helpers/timeSelect";
import type { DashboardConfig, Panel } from "../types";

type TimeRangeType = { start: Date; end: Date };

export const usePanels = (
  dashboardConfig: Ref<DashboardConfig | null>,
  timeRange: Ref<TimeRangeType | string>
) => {
  return computed(() => {
    const newPanels: Record<string, Panel> = {};

    dashboardConfig.value?.panels.forEach((panel) => {
      if (panel.chart.query.timeDimensions) {
        newPanels[panel.id] = {
          ...panel,
          chart: {
            ...panel.chart,
            query: {
              ...panel.chart.query,
              timeDimensions: [
                {
                  ...panel.chart.query.timeDimensions[0],
                  granularity: getGranularity(
                    timeRange.value as unknown as string | [Date, Date]
                  ),
                  dateRange:
                    typeof timeRange.value === "string"
                      ? timeRange.value
                      : [
                          (
                            timeRange.value as TimeRangeType
                          ).start.toISOString(),
                          (timeRange.value as TimeRangeType).end.toISOString(),
                        ],
                },
              ],
            },
          },
        };
      } else {
        const tableName = (
          panel.chart.query.dimensions?.[0] ?? panel.chart.query.measures?.[0]
        )?.split(".")[0];
        if (tableName) {
          newPanels[panel.id] = {
            ...panel,
            chart: {
              ...panel.chart,
              query: {
                ...panel.chart.query,
                timeDimensions: [
                  {
                    dimension: `${tableName}.timestamp`,
                    dateRange:
                      typeof timeRange.value === "string"
                        ? timeRange.value
                        : [
                            (
                              timeRange.value as TimeRangeType
                            ).start.toISOString(),
                            (
                              timeRange.value as TimeRangeType
                            ).end.toISOString(),
                          ],
                  },
                ],
              },
            },
          };
        } else {
          newPanels[panel.id] = { ...panel };
        }
      }
    });

    return newPanels;
  });
};
