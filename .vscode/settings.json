{
    "files.associations": {
        "*.css": "tailwindcss"
    },
    "editor.quickSuggestions": {
        "strings": "on"
    },
    "tailwindCSS.classAttributes": [
        "class",
        "ui"
    ],
    "tailwindCSS.experimental.classRegex": [
        [
            "ui:\\s*{([^)]*)\\s*}",
            "(?:'|\"|`)([^']*)(?:'|\"|`)"
        ]
    ],
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
    },
    "[vue]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
}