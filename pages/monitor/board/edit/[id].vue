<script setup lang="ts">
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useMonitorEditStore } from "~/modules/monitor/stores/monitorEditStore";
import MonitorSideChat from "~/modules/monitor/components/MonitorSideChat.vue";
import DashboardCanvas from "~/modules/monitor/components/DashboardCanvas.vue";
import MonitorChat from "~/modules/monitor/components/MonitorChat.vue";
import { useRefreshService } from "~/modules/monitor/services/refreshService";

const refreshService = useRefreshService();
provide("refreshService", refreshService);

const monitorEditStore = useMonitorEditStore();
const { dashboard, previewBoardOpened, previewDashboardConfig } =
  storeToRefs(monitorEditStore);

useHead({
  title: `${dashboard?.value?.name ?? ""} - Monitor`,
});

definePageMeta({
  layout: "monitor",
});
</script>

<template>
  <ClientOnly>
    <Teleport to="#page-header-left">
      <Breadcrumb class="px-3">
        <BreadcrumbList>
          <BreadcrumbItem>
            <NuxtLink to="/monitor" as-child>
              <BreadcrumbLink> Monitor </BreadcrumbLink>
            </NuxtLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{{ dashboard?.name }}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </Teleport>
  </ClientOnly>

  <div
    v-if="!previewBoardOpened && !previewDashboardConfig"
    class="mx-auto h-[calc(100vh-64px)] overflow-auto"
  >
    <MonitorChat />
  </div>

  <div v-else class="flex h-[calc(100vh-64px)]">
    <MonitorSideChat />
    <DashboardCanvas />
  </div>
</template>
