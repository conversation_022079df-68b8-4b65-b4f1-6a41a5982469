<script setup lang="ts">
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  Bread<PERSON><PERSON>bLink,
  BreadcrumbList,
  B<PERSON><PERSON>rumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { GridLayout } from "grid-layout-plus";
import TimeSelect from "@/modules/monitor/components/TimeSelect.vue";
import RefreshChart from "@/modules/monitor/components/RefreshChart.vue";
import { useMonitorDashboard } from "~/modules/monitor/hooks/useMonitorDashboard";
import DashboardPanel from "~/modules/monitor/components/DashboardPanel.vue";
import PlaceholderPanel from "~/modules/monitor/components/PlaceholderPanel.vue";
import { usePanels } from "~/modules/monitor/hooks/usePanels";
import { Button } from "@/components/ui/button";
import { BugIcon } from "lucide-vue-next";
import DashboardRow from "~/modules/monitor/components/DashboardRow.vue";
import { useRefreshService } from "~/modules/monitor/services/refreshService";
import { RELATIVE_TIME_OPTIONS } from "~/const";

definePageMeta({
  layout: "monitor",
});

const refreshService = useRefreshService();
provide("refreshService", refreshService);

const config = useRuntimeConfig();
const { dashboard } = useMonitorDashboard();
const enableDebug = ref(false);
const debugCubeQuery = computed(
  () => config.public.debugCubeQuery.toLowerCase() === "true"
);

const _timeRange = ref<string>(RELATIVE_TIME_OPTIONS[7].value);
const timeRange = computed(() => {
  if (typeof _timeRange.value === "string") {
    return _timeRange.value;
  }
  return {
    start: (_timeRange.value as [Date, Date])[0],
    end: (_timeRange.value as [Date, Date])[1],
  };
});

const dashConfig = computed(() => dashboard.value?.dashConfig ?? null);
const panels = usePanels(dashConfig, timeRange);

useHead({
  title: `${dashboard?.value?.name ?? ""} - Monitor`,
});
</script>

<template>
  <div class="p-3 w-full h-full">
    <ClientOnly>
      <Teleport to="#page-header-left">
        <Breadcrumb class="px-3">
          <BreadcrumbList>
            <BreadcrumbItem>
              <NuxtLink to="/monitor" as-child>
                <BreadcrumbLink> Monitor </BreadcrumbLink>
              </NuxtLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{{ dashboard?.name }}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </Teleport>
      <Teleport to="#page-header-right">
        <div class="flex justify-end gap-2">
          <Button
            v-if="debugCubeQuery"
            class="rounded-full"
            variant="ghost"
            @click="enableDebug = !enableDebug"
          >
            <BugIcon :size="18" />
          </Button>
          <TimeSelect v-model:value="_timeRange" />
          <RefreshChart />
        </div>
      </Teleport>
    </ClientOnly>

    <div class="px-10 py-5">
      <DashboardRow
        v-for="(row, index) in dashboard?.dashConfig?.rows"
        :key="row.title"
        :title="row.title"
        :color-index="index"
      >
        <GridLayout
          v-model:layout="row.layout"
          :col-num="12"
          :row-height="80"
          :is-draggable="false"
          :is-resizable="false"
          vertical-compact
          use-css-transforms
        >
          <template #item="{ item }">
            <DashboardPanel
              v-if="panels[item.i]"
              :config="panels[item.i]"
              :debug="enableDebug"
              :color-index="index"
              :date-range="_timeRange"
            />
            <PlaceholderPanel v-else />
          </template>
        </GridLayout>
      </DashboardRow>
    </div>
  </div>
</template>
