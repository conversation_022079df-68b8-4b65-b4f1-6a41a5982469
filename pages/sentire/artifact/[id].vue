<script setup lang="ts">
import { useRoute } from "vue-router";
import * as jose from "jose";
import { useArtifactReport } from "~/modules/sentire/hooks/useArtifactReport";

useHead({
  title: "Sentire Artifact",
});

definePageMeta({
  layout: "artifact",
});

const route = useRoute();
const config = useRuntimeConfig();

const decodeShareableLink = async (
  token: string,
  errors?: { shareTokenExpired?: string; invalidShareToken?: string }
) => {
  try {
    const { payload } = await jose.jwtVerify(
      token,
      new TextEncoder().encode(config.public.jwtSecret)
    );
    console.log("payload", payload);
    const p = payload as { reportId: string; exp: number };
    if (p.exp < Math.round(Date.now() / 1000)) {
      throw new Error(errors?.shareTokenExpired ?? "share link expired");
    }
    return p;
  } catch (e) {
    console.error(e);
    throw new Error(errors?.invalidShareToken ?? "invalid token");
  }
};

const { reportId } = await decodeShareableLink(route.params.id as string);
const report = await useArtifactReport(reportId);
</script>

<template>
  <div class="w-screen h-screen">
    <iframe
      sandbox="allow-scripts allow-same-origin"
      :srcdoc="report?.content.value ?? ''"
      class="w-full h-full"
    />
  </div>
</template>

<style scoped>
:deep(.sp-preview-actions) {
  display: none;
}

:deep(.sp-preview) {
  height: 100%;
}
</style>
