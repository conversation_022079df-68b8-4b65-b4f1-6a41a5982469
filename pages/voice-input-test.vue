<script setup lang="ts">
import { ref } from "vue";
import VoiceInput from "@/modules/sentire/components/VoiceInput.vue";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { toast } from "vue-sonner";
import {
  MicIcon,
  PlayIcon,
  TrashIcon,
  SettingsIcon,
  InfoIcon,
} from "lucide-vue-next";

// Page meta
definePageMeta({
  title: "VoiceInput Component Test",
  layout: "default",
});

// Types
interface VoiceInputResult {
  text?: string;
  file?: File;
  blob?: Blob;
}

interface TestResult {
  timestamp: string;
  data: VoiceInputResult;
}

interface AudioConfig {
  stt: {
    engine: string;
  };
}

const recording = ref(false);
const result = ref<VoiceInputResult | null>(null);

// Audio configurations for testing
const audioConfigs: Record<string, AudioConfig> = {
  web_en: { stt: { engine: "web" } },
  api_en: { stt: { engine: "api" } },
};

const selectedConfig = ref("web_en");
const testResults = ref<TestResult[]>([]);

// Event handlers
const handleConfirm = (data: VoiceInputResult) => {
  recording.value = false;
  result.value = data;
  // Add to results history
  testResults.value.unshift({
    timestamp: new Date().toLocaleString(),
    data,
  });
};

const handleCancel = () => {
  recording.value = false;
  result.value = null;
};

const startTest = () => {
  recording.value = true;
};

const clearResults = () => {
  testResults.value = [];
  result.value = null;
  toast.success("Results cleared");
};

// Utility functions
const formatResultData = (data: VoiceInputResult | null) => {
  if (!data) return "No data";

  if (data.text) return `Text: "${data.text}"`;
  if (data.file)
    return `File: ${data.file.name} (${(data.file.size / 1024).toFixed(2)}KB)`;
  if (data.blob) return `Blob: ${(data.blob.size / 1024).toFixed(2)}KB`;

  return JSON.stringify(data, null, 2);
};

const getResultType = (data: VoiceInputResult | null) => {
  if (!data) return "none";
  if (data.text) return "text";
  if (data.file) return "file";
  if (data.blob) return "blob";
  return "unknown";
};
</script>

<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
    <div class="max-w-6xl mx-auto space-y-6">
      <!-- Header -->
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          VoiceInput Component Test Page
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
          Test different configurations and features of the VoiceInput component
        </p>
      </div>

      <!-- Control Panel -->
      <Card>
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <SettingsIcon class="size-5" />
            Control Panel
          </CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-2">
              <span class="text-sm font-medium">Audio Config:</span>
              <select
                v-model="selectedConfig"
                class="px-3 py-1 border rounded-md bg-white dark:bg-gray-800 text-sm"
              >
                <option value="web_en">Web API (English)</option>
                <option value="web_zh">Web API (Chinese)</option>
                <option value="api_en">Custom API (English)</option>
                <option value="api_zh">Custom API (Chinese)</option>
              </select>
            </div>
            <Button
              variant="outline"
              size="sm"
              class="flex items-center gap-1"
              @click="clearResults"
            >
              <TrashIcon class="size-4" />
              Clear All Results
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Test Cases Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-1 gap-6">
        <!-- Transcription Test -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <MicIcon class="size-5" />
              Transcription
            </CardTitle>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Audio recording with speech-to-text transcription
            </p>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="flex items-center gap-2">
              <Button :disabled="recording" size="sm" @click="startTest()">
                <PlayIcon class="size-4 mr-1" />
                Test
              </Button>
              <Button
                v-if="result"
                variant="ghost"
                size="sm"
                @click="clearResults()"
              >
                <TrashIcon class="size-4" />
              </Button>
            </div>

            <div v-if="recording" class="space-y-2">
              <VoiceInput
                :recording="recording"
                :transcribe="true"
                :audio-config="audioConfigs[selectedConfig]"
                @confirm="handleConfirm"
                @cancel="handleCancel"
              />
            </div>

            <div v-if="result" class="space-y-2">
              <div class="flex items-center gap-2">
                <span class="text-sm font-medium">Result:</span>
                <Badge
                  :variant="
                    getResultType(result) === 'text' ? 'default' : 'secondary'
                  "
                >
                  {{ getResultType(result) }}
                </Badge>
              </div>
              <div class="p-3 bg-gray-100 dark:bg-gray-800 rounded-md text-sm">
                {{ formatResultData(result) }}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Test Results History -->
      <Card v-if="testResults.length > 0">
        <CardHeader>
          <CardTitle class="flex items-center gap-2">
            <InfoIcon class="size-5" />
            Test Results History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="space-y-3 max-h-96 overflow-y-auto">
            <div
              v-for="(result, index) in testResults"
              :key="index"
              class="p-3 border rounded-md bg-gray-50 dark:bg-gray-800"
            >
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2">
                  <Badge
                    :variant="
                      getResultType(result.data) === 'text'
                        ? 'default'
                        : 'secondary'
                    "
                  >
                    {{ getResultType(result.data) }}
                  </Badge>
                </div>
                <span class="text-xs text-gray-500">{{
                  result.timestamp
                }}</span>
              </div>
              <div
                class="text-sm font-mono bg-white dark:bg-gray-900 p-2 rounded border"
              >
                {{ formatResultData(result.data) }}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<style scoped lang="scss">
/* Additional component styles if needed */
</style>
