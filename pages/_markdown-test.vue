<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Markdown from "@/components/Markdown.vue";

// Set page title
useHead({
  title: "Markdown Streaming Test - Capehorn",
});

// Reactive markdown content
const markdownContent = ref("");
const isStreaming = ref(false);
const isMermaidTesting = ref(false);

// Mixed Content Streaming test with images
const testContent = `

![Capehorn Logo](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)

# Complete Feature Test

This test demonstrates **all features** working together in a streaming environment.

## Process Flow

\`\`\`mermaid
graph LR
    A[Source Text] --> B[Stream Characters]
    B --> C[Update Content]
    C --> D[Parse Markdown]
    D --> E[Apply Highlighting]
    E --> F[Render Diagrams]
    F --> G[Initialize Images]
    G --> H[Display Result]
\`\`\`

## Overview

The markdown component supports:

1. **Text formatting** - bold, italic, code
2. **Code blocks** with syntax highlighting
3. **Lists** - ordered, unordered, and task lists
4. **Tables** with proper formatting
5. **Mermaid diagrams** for visualization
6. **Images** with viewer integration
7. **Blockquotes** and horizontal rules

---

## Link Test

* [Google](https://www.google.com)
* [Youtube](https://www.youtube.com)
* [Facebook](https://www.facebook.com)
* [Twitter](https://www.twitter.com)
* [Instagram](https://www.instagram.com)
* [LinkedIn](https://www.linkedin.com)
* [GitHub](https://www.github.com)
* [Stack Overflow](https://www.stackoverflow.com)
* [Reddit](https://www.reddit.com)

## Images Test

Here are some test images to verify the image viewer functionality:

### Logo Images

![Capehorn Logo](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)

The main logo should be displayed above with proper viewer integration.

### Multiple Images

![Sample Image 1](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)

![Sample Image 2](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)

![Sample Image 3](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)

> **Note**: Click on any image to open it in the viewer for a better look.

---

## Code Example

Here's how the streaming works:

\`\`\`typescript
interface StreamingMarkdownProps {
  source: string;
  class?: string;
  style?: string;
}

const StreamingMarkdown = ({ source }: StreamingMarkdownProps) => {
  const [content, setContent] = useState('');
  
  useEffect(() => {
    // Simulate streaming
    let index = 0;
    const timer = setInterval(() => {
      if (index < source.length) {
        setContent(source.substring(0, index + 1));
        index++;
      } else {
        clearInterval(timer);
      }
    }, 10);
    
    return () => clearInterval(timer);
  }, [source]);
  
  return <Markdown source={content} />;
};
\`\`\`


## Feature Matrix

| Feature | Supported | Notes |
|---------|-----------|-------|
| Headers | ✅ | H1-H6 supported |
| Paragraphs | ✅ | With line breaks |
| **Bold** | ✅ | Strong emphasis |
| *Italic* | ✅ | Emphasis |
| \`Code\` | ✅ | Inline code |
| Code blocks | ✅ | With syntax highlighting |
| Lists | ✅ | Ordered/unordered/tasks |
| Tables | ✅ | Full table support |
| Blockquotes | ✅ | > Quote syntax |
| HR | ✅ | Horizontal rules |
| Links | ✅ | [Link text](url) |
| Images | ✅ | With viewer integration |
| Mermaid | ✅ | Diagram rendering |

## Architecture Diagram

\`\`\`mermaid
sequenceDiagram
    participant User
    participant Component
    participant Markdown
    participant Prism
    participant Mermaid
    participant Viewer
    
    User->>Component: Start streaming
    Component->>Markdown: Update content
    Markdown->>Markdown: Parse markdown
    Markdown->>Prism: Apply syntax highlighting
    Markdown->>Mermaid: Render diagrams
    Markdown->>Viewer: Initialize image viewer
    Prism-->>Markdown: Highlighted content
    Mermaid-->>Markdown: Rendered diagrams
    Viewer-->>Markdown: Viewer initialized
    Markdown-->>Component: Rendered content
    Component-->>User: Display update
\`\`\`

## Task Progress

- [x] Implement basic streaming
- [x] Add syntax highlighting
- [x] Support mermaid diagrams
- [x] Add image viewer
- [x] Implement fade-in animations
- [x] Test with complex content
- [x] Verify all features work together
- [ ] Add more diagram types
- [ ] Optimize performance for large content

## Sample Lists

### Unordered List
- First item with **bold** text
- Second item with *italic* text
- Third item with \`inline code\`
  - Nested item 1
  - Nested item 2
    - Deeply nested item

### Ordered List
1. First step
2. Second step with [link](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)
3. Third step with image below:

   ![Step Image](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)

4. Fourth step

### Task List
- [x] Test basic markdown
- [x] Test code blocks
- [x] Test images
- [x] Test mermaid diagrams
- [x] Test tables
- [ ] Add more test cases
- [ ] Performance optimization

## Image Gallery

Below is a gallery of test images:

![Gallery Image 1](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)
![Gallery Image 2](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)
![Gallery Image 3](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)
![Gallery Image 4](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80)

> **Gallery Note**: These images test the viewer functionality with multiple images on the same page.

## Complex Code Block

\`\`\`javascript
// Advanced streaming markdown renderer
class AdvancedMarkdownRenderer {
  constructor(options = {}) {
    this.options = {
      speed: 20,
      enableImages: true,
      enableMermaid: true,
      enableSyntaxHighlighting: true,
      ...options
    };
    
    this.listeners = [];
    this.currentIndex = 0;
  }
  
  async render(content, target) {
    this.currentIndex = 0;
    
    while (this.currentIndex < content.length) {
      const currentContent = content.substring(0, this.currentIndex + 1);
      
      // Update target with current content
      await this.updateTarget(target, currentContent);
      
      // Apply enhancements
      if (this.options.enableSyntaxHighlighting) {
        await this.applySyntaxHighlighting(target);
      }
      
      if (this.options.enableMermaid) {
        await this.renderMermaidDiagrams(target);
      }
      
      if (this.options.enableImages) {
        await this.initializeImageViewer(target);
      }
      
      // Wait before next character
      await this.sleep(this.options.speed);
      this.currentIndex++;
    }
    
    // Notify completion
    this.notifyListeners('complete');
  }
  
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  notifyListeners(event) {
    this.listeners.forEach(listener => listener(event));
  }
}

// Usage example
const renderer = new AdvancedMarkdownRenderer({
  speed: 15,
  enableImages: true
});

renderer.render(markdownContent, document.getElementById('target'));
\`\`\`

## Final Notes

> **Test completed successfully!** 🎉
>
> This comprehensive test validates:
> - Streaming text rendering
> - Syntax highlighting in code blocks
> - Mermaid diagram generation
> - Image viewer integration
> - Table formatting
> - List rendering (all types)
> - Mixed content handling
>
> All features work seamlessly together during the streaming process.

---

**End of test content** - The streaming markdown renderer handles all these features correctly!`;

// Streaming simulation
const simulateStreaming = async (content: string, speed: number = 15) => {
  markdownContent.value = "";
  isStreaming.value = true;

  for (let i = 0; i <= content.length; i++) {
    markdownContent.value = content.substring(0, i);
    await new Promise((resolve) => setTimeout(resolve, speed));
  }

  isStreaming.value = false;
};

// 专门的 Mermaid 完整性测试
const testMermaidIncomplete = async () => {
  if (isMermaidTesting.value || isStreaming.value) return;

  isMermaidTesting.value = true;
  markdownContent.value = "";

  const mermaidTestCases = [
    {
      content: "# Mermaid 完整性测试\n\n测试不完整的 Mermaid 语法：\n\n",
      description: "基础内容",
    },
    {
      content: "# Mermaid 完整性测试\n\n测试不完整的 Mermaid 语法：\n\n```",
      description: "开始代码块（不完整）",
    },
    {
      content: "# Mermaid 完整性测试\n\n测试不完整的 Mermaid 语法：\n\n```m",
      description: "不完整的 mermaid 关键字",
    },
    {
      content:
        "# Mermaid 完整性测试\n\n测试不完整的 Mermaid 语法：\n\n```mermaid",
      description: "完整的 mermaid 关键字但没有内容",
    },
    {
      content:
        "# Mermaid 完整性测试\n\n测试不完整的 Mermaid 语法：\n\n```mermaid\n",
      description: "mermaid 代码块开始但没有内容",
    },
    {
      content:
        "# Mermaid 完整性测试\n\n测试不完整的 Mermaid 语法：\n\n```mermaid\ngraph TD\n    A[开始]",
      description: "部分 mermaid 内容（不完整）",
    },
    {
      content:
        "# Mermaid 完整性测试\n\n测试不完整的 Mermaid 语法：\n\n```mermaid\ngraph TD\n    A[开始] --> B[处理]\n    B --> C[结束]",
      description: "完整的 mermaid 内容但没有结束标记",
    },
    {
      content:
        "# Mermaid 完整性测试\n\n测试不完整的 Mermaid 语法：\n\n```mermaid\ngraph TD\n    A[开始] --> B[处理]\n    B --> C[结束]\n```",
      description: "完整的 mermaid 代码块",
    },
    {
      content:
        "# Mermaid 完整性测试\n\n测试不完整的 Mermaid 语法：\n\n```mermaid\ngraph TD\n    A[开始] --> B[处理]\n    B --> C[结束]\n```\n\n✅ 完整的 Mermaid 图表渲染成功！\n\n> 观察：只有当 Mermaid 语法完整时，图表才会显示。",
      description: "最终完整结果",
    },
  ];

  for (let i = 0; i < mermaidTestCases.length; i++) {
    const testCase = mermaidTestCases[i];
    console.log(`测试步骤 ${i + 1}: ${testCase.description}`);
    markdownContent.value = testCase.content;
    await new Promise((resolve) => setTimeout(resolve, 2000)); // 增加等待时间以便观察
  }

  isMermaidTesting.value = false;
};

// 专门的链接和图片完整性测试
const testLinksAndImagesIncomplete = async () => {
  if (isStreaming.value || isMermaidTesting.value) return;

  isStreaming.value = true;
  markdownContent.value = "";

  const linkImageTestCases = [
    {
      content: "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n",
      description: "基础内容",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[",
      description: "不完整的链接开始",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本",
      description: "不完整的链接文本",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本]",
      description: "链接文本完整但没有URL",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本](",
      description: "链接URL开始但不完整",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本](https://example.com)",
      description: "完整的链接",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本](https://example.com)\n\n现在测试图片：\n\n!",
      description: "不完整的图片开始",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本](https://example.com)\n\n现在测试图片：\n\n![",
      description: "图片语法开始",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本](https://example.com)\n\n现在测试图片：\n\n![图片描述",
      description: "不完整的图片描述",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本](https://example.com)\n\n现在测试图片：\n\n![图片描述]",
      description: "图片描述完整但没有URL",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本](https://example.com)\n\n现在测试图片：\n\n![图片描述](",
      description: "图片URL开始但不完整",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本](https://example.com)\n\n现在测试图片：\n\n![图片描述](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?w=720&q=80)",
      description: "完整的图片",
    },
    {
      content:
        "# 超链接和图片完整性测试\n\n测试不完整的链接和图片语法：\n\n这是一个普通的段落，接下来测试链接。\n\n[链接文本](https://example.com)\n\n现在测试图片：\n\n![图片描述](https://images.unsplash.com/photo-1598928506311-c55ded91a20c?w=720&q=80)\n\n✅ 测试完成！\n\n> 观察：只有当链接和图片语法完整时，才会显示完整内容。不完整的语法会被暂时隐藏并显示 'loading...'。",
      description: "最终完整结果",
    },
  ];

  for (let i = 0; i < linkImageTestCases.length; i++) {
    const testCase = linkImageTestCases[i];
    console.log(`链接图片测试步骤 ${i + 1}: ${testCase.description}`);
    markdownContent.value = testCase.content;
    await new Promise((resolve) => setTimeout(resolve, 2000)); // 增加等待时间以便观察
  }

  isStreaming.value = false;
};

onMounted(() => {
  // Auto-start streaming on page load
  simulateStreaming(testContent);
});
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-6">
    <div class="mx-auto max-w-5xl">
      <!-- Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">
          Markdown Streaming Test
        </h1>
        <p class="mt-2 text-gray-600">
          Complete mixed content streaming test with all features
        </p>
        <div class="mt-4 flex items-center gap-2">
          <Badge v-if="isStreaming" variant="default"> 🔄 Streaming... </Badge>
          <Badge v-else-if="isMermaidTesting" variant="default">
            🔄 Mermaid Testing...
          </Badge>
          <Badge v-else variant="secondary"> ✅ Complete </Badge>
        </div>
        <div class="mt-4 flex gap-2">
          <button
            @click="simulateStreaming(testContent)"
            :disabled="isStreaming || isMermaidTesting"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
          >
            重新测试完整内容
          </button>
          <button
            @click="testMermaidIncomplete"
            :disabled="isStreaming || isMermaidTesting"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400"
          >
            {{ isMermaidTesting ? "测试中..." : "测试 Mermaid 完整性" }}
          </button>
          <button
            @click="testLinksAndImagesIncomplete"
            :disabled="isStreaming || isMermaidTesting"
            class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-400"
          >
            {{ isStreaming ? "测试中..." : "测试链接图片完整性" }}
          </button>
        </div>
      </div>

      <!-- Rendered Output -->
      <Card>
        <CardHeader>
          <CardTitle>Rendered Output</CardTitle>
          <CardDescription>
            Live preview of streaming markdown content with all features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div class="bg-white border rounded-lg p-6">
            <Markdown :source="markdownContent" class="max-w-none" />
            <div v-if="!markdownContent" class="text-center text-gray-500 py-8">
              Loading streaming content...
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<style scoped>
/* Ensure proper spacing and presentation */
:deep(.prose) {
  max-width: none;
}
</style>
